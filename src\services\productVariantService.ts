import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  orderBy,
  serverTimestamp,
  DocumentData,
  QueryDocumentSnapshot
} from 'firebase/firestore';
import { db } from '../firebase/config';
import { ProductVariant } from '../types';

// Map Firestore document to ProductVariant
const mapDocToProductVariant = (doc: QueryDocumentSnapshot<DocumentData>, productId: string): ProductVariant => {
  const data = doc.data();

  return {
    id: doc.id,
    productId,
    barcode: data.sku || '',
    variationValues: data.variationValues || {},
    price: data.price || 0,
    mrp: data.mrp || 0,
    stock: data.stock || 0,
    image: data.image || '',
    status: data.status || 'active',
    createdAt: data.createdAt ? new Date(data.createdAt.toDate()).toISOString() : new Date().toISOString(),
    updatedAt: data.updatedAt ? new Date(data.updatedAt.toDate()).toISOString() : new Date().toISOString(),
  };
};

// Map ProductVariant to Firestore document
const mapProductVariantToDoc = (variant: Omit<ProductVariant, 'id' | 'productId' | 'createdAt' | 'updatedAt'>): any => {
  return {
    sku: variant.barcode,
    variationValues: variant.variationValues,
    price: variant.price,
    mrp: variant.mrp,
    stock: variant.stock,
    image: variant.image || '',
    status: variant.status,
  };
};

// Get all variants for a product
export const getProductVariants = async (productId: string): Promise<ProductVariant[]> => {
  try {
    console.log('Fetching variants for product:', productId);

    // First check if the product exists
    const productRef = doc(db, 'Products', productId);
    const productSnap = await getDoc(productRef);

    if (!productSnap.exists()) {
      console.log('Product does not exist yet:', productId);
      return [];
    }

    const variantsRef = collection(db, 'Products', productId, 'Variants');

    // Try with orderBy first, fallback to simple query if it fails
    let querySnapshot;
    try {
      const q = query(variantsRef, orderBy('createdAt'));
      querySnapshot = await getDocs(q);
    } catch (orderError) {
      console.log('OrderBy failed, trying simple query:', orderError);
      // Fallback to simple query without orderBy
      querySnapshot = await getDocs(variantsRef);
    }

    const variants = querySnapshot.docs.map(doc => mapDocToProductVariant(doc, productId));
    console.log(`Found ${variants.length} variants for product ${productId}`);
    return variants;
  } catch (error) {
    console.error('Error getting product variants:', error);
    return [];
  }
};

// Get a single variant by ID
export const getProductVariantById = async (productId: string, variantId: string): Promise<ProductVariant | null> => {
  try {
    const docRef = doc(db, 'Products', productId, 'Variants', variantId);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      return mapDocToProductVariant(docSnap as QueryDocumentSnapshot<DocumentData>, productId);
    } else {
      console.log('No such variant!');
      return null;
    }
  } catch (error) {
    console.error('Error getting product variant:', error);
    return null;
  }
};

// Add a new variant to a product
export const addProductVariant = async (
  productId: string,
  variant: Omit<ProductVariant, 'id' | 'productId' | 'createdAt' | 'updatedAt'>
): Promise<string | null> => {
  try {
    console.log('Adding product variant:', variant);

    const docData = {
      ...mapProductVariantToDoc(variant),
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    };

    console.log('Variant document data being saved to Firestore:', docData);

    const variantsRef = collection(db, 'Products', productId, 'Variants');
    const docRef = await addDoc(variantsRef, docData);
    console.log('Product variant added successfully with ID:', docRef.id);
    return docRef.id;
  } catch (error) {
    console.error('Error adding product variant:', error);
    return null;
  }
};

// Update an existing variant
export const updateProductVariant = async (
  productId: string,
  variantId: string,
  variant: Partial<Omit<ProductVariant, 'id' | 'productId' | 'createdAt' | 'updatedAt'>>
): Promise<boolean> => {
  try {
    console.log('Updating product variant with ID:', variantId, 'Data:', variant);

    const docRef = doc(db, 'Products', productId, 'Variants', variantId);
    const updateData = {
      ...variant,
      updatedAt: serverTimestamp()
    };

    await updateDoc(docRef, updateData);
    console.log('Product variant updated successfully');
    return true;
  } catch (error) {
    console.error('Error updating product variant:', error);
    return false;
  }
};

// Delete a variant
export const deleteProductVariant = async (productId: string, variantId: string): Promise<boolean> => {
  try {
    const docRef = doc(db, 'Products', productId, 'Variants', variantId);
    await deleteDoc(docRef);
    return true;
  } catch (error) {
    console.error('Error deleting product variant:', error);
    return false;
  }
};

// Update variant stock
export const updateVariantStock = async (
  productId: string,
  variantId: string,
  newStock: number
): Promise<boolean> => {
  try {
    const docRef = doc(db, 'Products', productId, 'Variants', variantId);
    await updateDoc(docRef, {
      stock: newStock,
      updatedAt: serverTimestamp()
    });
    return true;
  } catch (error) {
    console.error('Error updating variant stock:', error);
    return false;
  }
};
