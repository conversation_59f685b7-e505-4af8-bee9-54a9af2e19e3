import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import {
  ArrowLeft,
  Calendar,
  User,
  ShoppingBag,
  Package,
  CheckCircle,
  XCircle,
  RefreshCw,
  DollarSign,
  CreditCard
} from 'lucide-react';
import { Card } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { ReturnStatusBadge } from '../../components/ui/StatusBadge';
import { Return, ReturnStatus, getReturnById, updateReturnStatus } from '../../services/returnService';

export function ReturnDetail() {
  const { id } = useParams<{ id: string }>();
  const [returnData, setReturnData] = useState<Return | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [updateLoading, setUpdateLoading] = useState(false);

  // Fetch return data on component mount
  useEffect(() => {
    if (id) {
      fetchReturnData(id);
    }
  }, [id]);

  // Fetch return data from the API
  const fetchReturnData = async (returnId: string) => {
    try {
      setLoading(true);
      setError(null);
      const data = await getReturnById(returnId);
      console.log('Fetched return data:', data);
      setReturnData(data);
    } catch (err) {
      console.error('Error fetching return data:', err);
      setError('Failed to load return data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle status update
  const handleStatusUpdate = async (newStatus: ReturnStatus) => {
    if (!id) return;
    
    try {
      setUpdateLoading(true);
      const success = await updateReturnStatus(id, newStatus);
      if (success) {
        // Refresh the return data
        fetchReturnData(id);
      } else {
        setError('Failed to update return status. Please try again.');
      }
    } catch (err) {
      console.error('Error updating return status:', err);
      setError('Failed to update return status. Please try again.');
    } finally {
      setUpdateLoading(false);
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="flex flex-col items-center">
          <RefreshCw className="w-12 h-12 text-primary-500 animate-spin mb-4" />
          <p className="text-gray-600">Loading return details...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-4">
        <div className="flex items-center">
          <XCircle className="h-5 w-5 text-red-400 mr-2" />
          <p className="text-red-700">{error}</p>
        </div>
        <Button
          variant="outline"
          size="sm"
          className="mt-2"
          onClick={() => id && fetchReturnData(id)}
        >
          Retry
        </Button>
      </div>
    );
  }

  if (!returnData) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <Package className="w-12 h-12 text-gray-400 mb-4" />
        <p className="text-gray-600">Return not found</p>
        <Link to="/returns">
          <Button variant="outline" className="mt-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Returns
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header */}
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
        <div className="flex items-center">
          <Link to="/returns" className="mr-4">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-display font-bold text-gray-900">Return #{returnData.id.slice(0, 8)}</h1>
            <p className="text-gray-500">
              {formatDate(returnData.returnInitiatedTime)}
            </p>
          </div>
        </div>
        <ReturnStatusBadge status={returnData.returnStatus} />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Return Details */}
        <div className="md:col-span-2">
          <Card>
            <div className="p-5">
              <h2 className="text-lg font-semibold mb-4">Return Details</h2>
              
              <div className="flex items-start mb-6">
                <div className="flex-shrink-0 h-24 w-24 rounded-md overflow-hidden bg-gray-100 mr-4">
                  {returnData.productImage ? (
                    <img
                      src={returnData.productImage}
                      alt={returnData.productName}
                      className="h-full w-full object-cover"
                    />
                  ) : (
                    <div className="flex items-center justify-center h-full w-full bg-gray-200">
                      <Package className="h-12 w-12 text-gray-400" />
                    </div>
                  )}
                </div>
                <div>
                  <h3 className="text-lg font-medium">{returnData.productName}</h3>
                  <p className="text-gray-600 mt-1">
                    {returnData.quantity} {returnData.unit} × ₹{returnData.price.toFixed(2)}
                  </p>
                  <p className="text-gray-600 mt-1">
                    Total: ₹{(returnData.price * returnData.quantity).toFixed(2)}
                  </p>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-2">Order Information</h3>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <ShoppingBag className="h-4 w-4 text-gray-400 mr-2" />
                      <span>Order #{returnData.orderId}</span>
                    </div>
                    <div className="flex items-center">
                      <CreditCard className="h-4 w-4 text-gray-400 mr-2" />
                      <span>Payment: {returnData.modeOfPayment}</span>
                    </div>
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                      <span>Return Initiated: {formatDate(returnData.returnInitiatedTime)}</span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-2">Customer Information</h3>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <User className="h-4 w-4 text-gray-400 mr-2" />
                      <span>{returnData.customerName}</span>
                    </div>
                    <div className="flex items-center">
                      <DollarSign className="h-4 w-4 text-gray-400 mr-2" />
                      <span>Branch Code: {returnData.branchCode}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </div>

        {/* Actions */}
        <div>
          <Card>
            <div className="p-5">
              <h2 className="text-lg font-semibold mb-4">Actions</h2>
              
              <div className="space-y-3">
                {returnData.returnStatus === 'pending' && (
                  <>
                    <Button
                      variant="success"
                      className="w-full"
                      onClick={() => handleStatusUpdate('approved')}
                      isLoading={updateLoading}
                    >
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Approve Return
                    </Button>
                    <Button
                      variant="danger"
                      className="w-full"
                      onClick={() => handleStatusUpdate('rejected')}
                      isLoading={updateLoading}
                    >
                      <XCircle className="h-4 w-4 mr-2" />
                      Reject Return
                    </Button>
                  </>
                )}
                
                {returnData.returnStatus === 'approved' && (
                  <Button
                    variant="primary"
                    className="w-full"
                    onClick={() => handleStatusUpdate('completed')}
                    isLoading={updateLoading}
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Mark as Completed
                  </Button>
                )}
                
                {(returnData.returnStatus === 'rejected' || returnData.returnStatus === 'completed') && (
                  <div className="bg-gray-50 p-4 rounded-md text-center">
                    <p className="text-gray-600">
                      This return is {returnData.returnStatus === 'rejected' ? 'rejected' : 'completed'} and cannot be modified.
                    </p>
                  </div>
                )}
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}
