import { useState } from 'react';
import { CheckCircle, Printer, Download, Copy, Share2 } from 'lucide-react';
import { Modal } from '../ui/Modal';
import { Button } from '../ui/Button';
import { CartItem } from '../../types';
import { Customer, Address } from '../../types';

interface OrderConfirmationProps {
  isOpen: boolean;
  onClose: () => void;
  orderItems: CartItem[];
  customer: Customer | null;
  address: Address | null;
  subtotal: number;
  tax: number;
  total: number;
  paymentMethod: 'cash' | 'card' | 'upi';
  paymentStatus?: string;
  orderId: string;
  orderDate: string;
}

export const OrderConfirmation = ({
  isOpen,
  onClose,
  orderItems,
  customer,
  address,
  subtotal,
  tax,
  total,
  paymentMethod,
  paymentStatus,
  orderId,
  orderDate,
}: OrderConfirmationProps) => {
  const [copied, setCopied] = useState(false);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const handleCopyOrderId = () => {
    navigator.clipboard.writeText(orderId);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const handlePrint = () => {
    window.print();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Order Confirmation"
      maxWidth="lg"
    >
      <div className="space-y-6 print:p-6">
        {/* Success Message */}
        <div className="flex flex-col items-center justify-center py-6 bg-green-50 rounded-lg">
          <CheckCircle className="w-16 h-16 text-green-500 mb-2" />
          <h2 className="text-xl font-bold text-gray-900">Order Placed Successfully!</h2>
          <p className="text-gray-600">Your order has been confirmed and is being processed.</p>
        </div>

        {/* Order Details */}
        <div className="border border-gray-200 rounded-lg overflow-hidden">
          <div className="bg-gray-50 p-4 border-b border-gray-200">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold text-gray-900">Order Details</h3>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-500">Order ID:</span>
                <div className="flex items-center space-x-1">
                  <span className="font-medium">#{orderId}</span>
                  <button
                    onClick={handleCopyOrderId}
                    className="text-primary-500 hover:text-primary-600"
                    title="Copy Order ID"
                  >
                    {copied ? (
                      <CheckCircle className="w-4 h-4" />
                    ) : (
                      <Copy className="w-4 h-4" />
                    )}
                  </button>
                </div>
              </div>
            </div>
            <p className="text-sm text-gray-500">
              {formatDate(orderDate)}
            </p>
          </div>

          <div className="p-4 space-y-4">
            {/* Customer Information */}
            {customer && (
              <div className="space-y-2">
                <h4 className="font-medium text-gray-900">Customer Information</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-500">Name</p>
                    <p className="font-medium">{customer.name}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Phone</p>
                    <p className="font-medium">{customer.phone}</p>
                  </div>
                  {customer.email && (
                    <div className="md:col-span-2">
                      <p className="text-sm text-gray-500">Email</p>
                      <p className="font-medium">{customer.email}</p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Delivery Address */}
            {address && (
              <div className="space-y-2">
                <h4 className="font-medium text-gray-900">Delivery Address</h4>
                <div className="text-sm">
                  <p className="capitalize">{address.type || 'Delivery'} Address</p>
                  <p>{address.line1}</p>
                  {address.line2 && <p>{address.line2}</p>}
                  <p>{address.city}, {address.state} - {address.pincode}</p>
                  {address.phoneNumber && <p>Phone: {address.phoneNumber}</p>}
                </div>
              </div>
            )}

            {/* Order Items */}
            <div className="space-y-2">
              <h4 className="font-medium text-gray-900">Order Items</h4>
              <div className="border border-gray-200 rounded-md overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Item
                      </th>
                      <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Price
                      </th>
                      <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Qty
                      </th>
                      <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Total
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {orderItems.map((item) => (
                      <tr key={item.id}>
                        <td className="px-4 py-3 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{item.productName}</div>
                          {item.tamilName && <div className="text-xs text-gray-500">{item.tamilName}</div>}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-right text-sm text-gray-500">
                          ₹{item.price.toFixed(2)}/{item.unit}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-right text-sm text-gray-500">
                          {item.quantity}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-right text-sm font-medium">
                          ₹{item.subtotal.toFixed(2)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Order Summary */}
            <div className="space-y-2">
              <h4 className="font-medium text-gray-900">Order Summary</h4>
              <div className="bg-gray-50 p-4 rounded-md">
                <div className="flex justify-between py-1">
                  <span className="text-sm text-gray-500">Subtotal</span>
                  <span className="text-sm font-medium">₹{subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between py-1">
                  <span className="text-sm text-gray-500">Tax (5%)</span>
                  <span className="text-sm font-medium">₹{tax.toFixed(2)}</span>
                </div>
                <div className="flex justify-between py-2 border-t border-gray-200 mt-2">
                  <span className="font-medium">Total</span>
                  <span className="font-bold text-lg">₹{total.toFixed(2)}</span>
                </div>
                <div className="flex justify-between py-1 border-t border-gray-200 mt-2">
                  <span className="text-sm text-gray-500">Payment Method</span>
                  <span className="text-sm font-medium capitalize">{paymentMethod}</span>
                </div>
                <div className="flex justify-between py-1">
                  <span className="text-sm text-gray-500">Payment Status</span>
                  <span className={`text-sm font-medium ${
                    paymentStatus === 'unpaid' || (paymentStatus === undefined && paymentMethod === 'cash')
                      ? 'text-amber-600'
                      : 'text-green-600'
                  }`}>
                    {paymentStatus ?
                      paymentStatus.charAt(0).toUpperCase() + paymentStatus.slice(1) :
                      (paymentMethod === 'cash' ? 'Unpaid' : 'Paid')
                    }
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-2 justify-center print:hidden">
          <Button
            variant="outline"
            onClick={handlePrint}
            icon={<Printer className="w-4 h-4 mr-2" />}
          >
            Print Receipt
          </Button>
          <Button
            variant="outline"
            icon={<Download className="w-4 h-4 mr-2" />}
          >
            Download Invoice
          </Button>
          <Button
            variant="outline"
            icon={<Share2 className="w-4 h-4 mr-2" />}
          >
            Share Receipt
          </Button>
          <Button
            variant="primary"
            onClick={onClose}
          >
            Done
          </Button>
        </div>
      </div>
    </Modal>
  );
};




