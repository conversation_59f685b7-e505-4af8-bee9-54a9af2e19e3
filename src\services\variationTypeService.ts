import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  orderBy,
  serverTimestamp,
  DocumentData,
  QueryDocumentSnapshot
} from 'firebase/firestore';
import { db } from '../firebase/config';
import { VariationType } from '../types';

const COLLECTION_NAME = 'VariationTypes';

// Map Firestore document to VariationType
const mapDocToVariationType = (doc: QueryDocumentSnapshot<DocumentData>): VariationType => {
  const data = doc.data();

  return {
    id: doc.id,
    name: data.name || '',
    options: data.options || [],
    description: data.description || '',
    createdAt: data.createdAt ? new Date(data.createdAt.toDate()).toISOString() : new Date().toISOString(),
    updatedAt: data.updatedAt ? new Date(data.updatedAt.toDate()).toISOString() : new Date().toISOString(),
  };
};

// Map VariationType to Firestore document
const mapVariationTypeToDoc = (variationType: Omit<VariationType, 'id' | 'createdAt' | 'updatedAt'>): any => {
  return {
    name: variationType.name,
    options: variationType.options,
    description: variationType.description || '',
  };
};

// Get all variation types
export const getAllVariationTypes = async (): Promise<VariationType[]> => {
  try {
    console.log('Fetching variation types from collection:', COLLECTION_NAME);
    const variationTypesRef = collection(db, COLLECTION_NAME);

    // Try without orderBy first to see if that's causing issues
    let querySnapshot;
    try {
      const q = query(variationTypesRef, orderBy('name'));
      querySnapshot = await getDocs(q);
    } catch (orderError) {
      console.log('OrderBy failed, trying simple query:', orderError);
      querySnapshot = await getDocs(variationTypesRef);
    }

    console.log(`Found ${querySnapshot.docs.length} variation type documents`);

    const variationTypes = querySnapshot.docs.map((doc, index) => {
      const data = doc.data();
      console.log(`Variation type ${index + 1}:`, { id: doc.id, name: data.name, options: data.options });
      return mapDocToVariationType(doc);
    });

    console.log('Mapped variation types:', variationTypes);
    return variationTypes;
  } catch (error) {
    console.error('Error getting variation types:', error);
    return [];
  }
};

// Get a single variation type by ID
export const getVariationTypeById = async (id: string): Promise<VariationType | null> => {
  try {
    const docRef = doc(db, COLLECTION_NAME, id);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      return mapDocToVariationType(docSnap as QueryDocumentSnapshot<DocumentData>);
    } else {
      console.log('No such variation type!');
      return null;
    }
  } catch (error) {
    console.error('Error getting variation type:', error);
    return null;
  }
};

// Add a new variation type
export const addVariationType = async (
  variationType: Omit<VariationType, 'id' | 'createdAt' | 'updatedAt'>
): Promise<string | null> => {
  try {
    console.log('Adding variation type:', variationType);

    const docData = {
      ...mapVariationTypeToDoc(variationType),
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    };

    console.log('Document data being saved to Firestore:', docData);

    const docRef = await addDoc(collection(db, COLLECTION_NAME), docData);
    console.log('Variation type added successfully with ID:', docRef.id);
    return docRef.id;
  } catch (error) {
    console.error('Error adding variation type:', error);
    return null;
  }
};

// Update an existing variation type
export const updateVariationType = async (
  id: string,
  variationType: Partial<Omit<VariationType, 'id' | 'createdAt' | 'updatedAt'>>
): Promise<boolean> => {
  try {
    console.log('Updating variation type with ID:', id, 'Data:', variationType);

    const docRef = doc(db, COLLECTION_NAME, id);
    const updateData = {
      ...variationType,
      updatedAt: serverTimestamp()
    };

    await updateDoc(docRef, updateData);
    console.log('Variation type updated successfully');
    return true;
  } catch (error) {
    console.error('Error updating variation type:', error);
    return false;
  }
};

// Delete a variation type
export const deleteVariationType = async (id: string): Promise<boolean> => {
  try {
    const docRef = doc(db, COLLECTION_NAME, id);
    await deleteDoc(docRef);
    return true;
  } catch (error) {
    console.error('Error deleting variation type:', error);
    return false;
  }
};
