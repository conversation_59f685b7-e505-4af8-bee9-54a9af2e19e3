import { useState, useEffect } from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import { SideNav } from './SideNav';
import { TopBar } from './TopBar';
import { MobileNav } from './MobileNav';
import { MobileWarning } from './MobileWarning';
import { useAuth } from '../../contexts/AuthContext';
import { Loader } from '../ui/Loader';

interface AdminLayoutProps {
  toggleDarkMode: () => void;
  isDarkMode: boolean;
}

export const AdminLayout = ({ toggleDarkMode, isDarkMode }: AdminLayoutProps) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(localStorage.getItem('sidebarCollapsed') === 'true');
  const { isLoading } = useAuth();
  const location = useLocation();

  // Close sidebar when location changes (especially helpful on mobile)
  useEffect(() => {
    setSidebarOpen(false);
  }, [location]);

  // Initialize sidebar collapsed state from localStorage
  useEffect(() => {
    const savedState = localStorage.getItem('sidebarCollapsed');
    if (savedState !== null) {
      setSidebarCollapsed(savedState === 'true');
    }
  }, []);

  const toggleSidebarCollapse = () => {
    const newState = !sidebarCollapsed;
    setSidebarCollapsed(newState);
    localStorage.setItem('sidebarCollapsed', newState.toString());
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background">
        <Loader size="large" />
      </div>
    );
  }

  return (
    <div className="flex h-screen overflow-hidden bg-background">
      {/* Mobile warning message */}
      <MobileWarning />

      {/* Sidebar for desktop */}
      <div className="hidden md:block relative">
        <SideNav collapsed={sidebarCollapsed} toggleCollapse={toggleSidebarCollapse} />
      </div>

      {/* Mobile navigation overlay */}
      <MobileNav open={sidebarOpen} setOpen={setSidebarOpen} />

      {/* Main content area */}
      <div className="flex flex-col flex-1 overflow-hidden transition-all duration-300 ease-in-out">
        <TopBar
          toggleSidebar={() => setSidebarOpen(true)}
          toggleDarkMode={toggleDarkMode}
          isDarkMode={isDarkMode}
        />

        <main className="flex-1 overflow-y-auto p-4 md:p-6 bg-background transition-all duration-300 ease-in-out">
          <div className="container mx-auto">
            <Outlet />
          </div>
        </main>
      </div>
    </div>
  );
};
