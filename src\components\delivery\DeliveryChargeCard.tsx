import React from 'react';
import { Edit2, Trash2, MapPin, Building, IndianRupee } from 'lucide-react';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';
import { DeliveryCharge } from '../../types';

interface DeliveryChargeCardProps {
  deliveryCharge: DeliveryCharge;
  onEdit: (deliveryCharge: DeliveryCharge) => void;
  onDelete: (deliveryCharge: DeliveryCharge) => void;
}

export const DeliveryChargeCard = ({ deliveryCharge, onEdit, onDelete }: DeliveryChargeCardProps) => {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }).format(amount);
  };

  const getDeliveryBadge = () => {
    if (deliveryCharge.deliveryCharge === 0) {
      return <Badge variant="success">Free Delivery</Badge>;
    } else if (deliveryCharge.deliveryCharge <= 50) {
      return <Badge variant="info">Low Cost</Badge>;
    } else if (deliveryCharge.deliveryCharge <= 100) {
      return <Badge variant="warning">Standard</Badge>;
    } else {
      return <Badge variant="danger">Premium</Badge>;
    }
  };

  return (
    <Card className="p-6 hover:shadow-md transition-shadow">
      <div className="flex justify-between items-start mb-4">
        <div className="flex-1">
          <div className="flex items-center gap-3 mb-2">
            <div className="flex items-center gap-2">
              <MapPin className="w-4 h-4 text-gray-500" />
              <span className="font-mono text-lg font-bold text-gray-900">
                {deliveryCharge.pincode}
              </span>
            </div>
            {getDeliveryBadge()}
          </div>
          
          <div className="flex items-center gap-2 mb-2">
            <Building className="w-4 h-4 text-gray-500" />
            <span className="text-gray-700 font-medium">{deliveryCharge.areaName}</span>
          </div>
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onEdit(deliveryCharge)}
          >
            <Edit2 className="w-4 h-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onDelete(deliveryCharge)}
            className="text-red-600 hover:text-red-700 hover:border-red-300"
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-4">
        <div>
          <div className="text-xs text-gray-500 mb-1">Delivery Charge</div>
          <div className="flex items-center text-lg font-bold text-green-600">
            <IndianRupee className="w-4 h-4 mr-1" />
            {deliveryCharge.deliveryCharge}
          </div>
        </div>

        <div>
          <div className="text-xs text-gray-500 mb-1">Branch</div>
          <div className="text-sm font-medium text-gray-700">
            {deliveryCharge.branchName}
          </div>
          <div className="text-xs text-gray-500">
            Code: {deliveryCharge.branchCode}
          </div>
        </div>
      </div>

      <div className="pt-3 border-t border-gray-100">
        <div className="text-xs text-gray-500">
          Pincode: {deliveryCharge.pincode} • Area: {deliveryCharge.areaName}
        </div>
      </div>
    </Card>
  );
};
