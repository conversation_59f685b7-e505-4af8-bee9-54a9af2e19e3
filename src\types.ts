// Product Types
export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  mrp?: number; // Maximum Retail Price
  stock: number;
  // Legacy fields for backward compatibility
  category: string;
  subcategory?: string;
  // New multi-category system
  categoryIDs?: string[];
  primaryCategoryID?: string;
  unit: string;
  barcode: string;
  status: 'available' | 'out_of_stock' | 'coming_soon';
  isVisible: boolean;
  images?: string[];
  farmerId?: string;
  nutrition?: string;
  storageInstruction?: string;
  // New variant system
  hasVariants?: boolean;
  defaultVariantID?: string;
  // New harvest day and pre-order system
  isPreOrder?: boolean;
  preOrderStartAt?: any; // timestamp | null
  preOrderEndAt?: any; // timestamp | null
  harvestOffsetDays?: number; // -1, 0, 1
  createdAt?: any;
  updatedAt?: any;
}

export interface ProductVariant {
  id: string;
  productId: string;
  barcode: string;
  variationValues: Record<string, string>;
  price: number;
  mrp: number;
  stock: number;
  image?: string;
  status: 'active' | 'inActive';
  // New harvest day and pre-order system (can override product defaults)
  isPreOrder?: boolean | null; // null = inherit from product
  preOrderStartAt?: any | null; // timestamp | null
  preOrderEndAt?: any | null; // timestamp | null
  harvestOffsetDays?: number | null; // -1, 0, 1 | null = inherit from product
  createdAt?: any;
  updatedAt?: any;
}

export interface VariationType {
  id: string;
  name: string;
  options: string[];
  description?: string;
  createdAt?: any;
  updatedAt?: any;
}

// Customer Types
export interface Address {
  id: string;
  type: 'home' | 'office' | 'other';
  line1: string;
  line2?: string;
  city: string;
  state: string;
  pincode: string;
  isDefault: boolean;
}

export interface Customer {
  id: string;
  name: string;
  email?: string;
  phone: string;
  addresses: Address[];
  totalOrders: number;
  totalSpent: number;
  lastOrderDate?: string;
  createdAt: string;
  status: 'active' | 'inactive';
}

// Order Types
export interface OrderItem {
  id: string;
  productId: string;
  productName: string;
  tamilName?: string;
  price: number;
  quantity: number;
  unit: string;
  subtotal: number;
  // New variant support
  variantID?: string | null;
  variationValues?: Record<string, string> | null;
}

export interface CartItem extends OrderItem {
  product: Product;
  variant?: ProductVariant;
}

export type OrderStatus = 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
export type PaymentStatus = 'pending' | 'paid' | 'failed' | 'refunded';
export type PaymentMethod = 'cash' | 'card' | 'upi' | 'bank_transfer';

export interface Order {
  id: string;
  customerId: string;
  customerName: string;
  customerPhone: string;
  customerEmail?: string;
  items: OrderItem[];
  subtotal: number;
  tax: number;
  total: number;
  status: OrderStatus;
  paymentStatus: PaymentStatus;
  paymentMethod: PaymentMethod;
  createdAt: string;
  updatedAt: string;
  cancellationReason?: string;
}

// Category Types
export interface Category {
  id: string;
  name: string;
  description?: string;
  image?: string;
  productCount: number;
  createdAt: string;
  updatedAt: string;
}

// User Types
export interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'staff';
  avatar?: string;
  createdAt: string;
  lastLogin?: string;
}

// Report Types
export interface SalesReport {
  period: string;
  revenue: number;
  orders: number;
  averageOrderValue: number;
}

export interface ProductReport {
  productId: string;
  productName: string;
  quantity: number;
  revenue: number;
}

export interface CustomerReport {
  customerId: string;
  customerName: string;
  orders: number;
  revenue: number;
}



