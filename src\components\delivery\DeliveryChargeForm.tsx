import React, { useState, useEffect } from 'react';
import { X, MapPin, IndianRupee, Building } from 'lucide-react';
import { Button } from '../ui/Button';
import { Modal } from '../ui/Modal';
import { Input } from '../ui/Input';
import { DeliveryCharge } from '../../types';
import { checkPincodeExists } from '../../services/deliveryChargeService';

interface DeliveryChargeFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (deliveryCharge: Omit<DeliveryCharge, 'id' | 'createdAt' | 'updatedAt'>) => void;
  initialData?: DeliveryCharge;
  isEditing?: boolean;
}

export const DeliveryChargeForm = ({
  isOpen,
  onClose,
  onSave,
  initialData,
  isEditing = false
}: DeliveryChargeFormProps) => {
  const [formData, setFormData] = useState({
    pincode: '',
    areaName: '',
    branchCode: 'test',
    branchName: 'test default',
    deliveryCharge: ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isValidating, setIsValidating] = useState(false);

  // Load initial data if editing
  useEffect(() => {
    if (initialData && isOpen) {
      setFormData({
        pincode: initialData.pincode,
        areaName: initialData.areaName,
        branchCode: initialData.branchCode,
        branchName: initialData.branchName,
        deliveryCharge: initialData.deliveryCharge.toString()
      });
    } else if (!isEditing && isOpen) {
      // Reset form for new delivery charge
      setFormData({
        pincode: '',
        areaName: '',
        branchCode: 'test',
        branchName: 'test default',
        deliveryCharge: ''
      });
    }
    setErrors({});
  }, [initialData, isOpen, isEditing]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validatePincode = async (pincode: string) => {
    if (!pincode) return;
    if (isEditing && pincode === initialData?.pincode) return; // Skip validation if editing and pincode hasn't changed
    
    setIsValidating(true);
    try {
      const exists = await checkPincodeExists(pincode, isEditing ? initialData?.id : undefined);
      if (exists) {
        setErrors(prev => ({ ...prev, pincode: 'This pincode already exists' }));
      } else {
        setErrors(prev => ({ ...prev, pincode: '' }));
      }
    } catch (error) {
      setErrors(prev => ({ ...prev, pincode: 'Error validating pincode' }));
    } finally {
      setIsValidating(false);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Required fields
    if (!formData.pincode.trim()) {
      newErrors.pincode = 'Pincode is required';
    } else if (!/^\d{6}$/.test(formData.pincode.trim())) {
      newErrors.pincode = 'Pincode must be exactly 6 digits';
    }

    if (!formData.areaName.trim()) {
      newErrors.areaName = 'Area name is required';
    }

    if (!formData.branchCode.trim()) {
      newErrors.branchCode = 'Branch code is required';
    }

    if (!formData.branchName.trim()) {
      newErrors.branchName = 'Branch name is required';
    }

    if (!formData.deliveryCharge || parseFloat(formData.deliveryCharge) < 0) {
      newErrors.deliveryCharge = 'Delivery charge must be 0 or greater';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    const deliveryChargeData: Omit<DeliveryCharge, 'id' | 'createdAt' | 'updatedAt'> = {
      pincode: formData.pincode.trim(),
      areaName: formData.areaName.trim(),
      branchCode: formData.branchCode.trim(),
      branchName: formData.branchName.trim(),
      deliveryCharge: parseFloat(formData.deliveryCharge)
    };

    onSave(deliveryChargeData);
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={isEditing ? 'Edit Delivery Charge' : 'Add New Delivery Charge'}
      maxWidth="md"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Pincode */}
        <Input
          label="Pincode"
          value={formData.pincode}
          onChange={(e) => handleInputChange('pincode', e.target.value)}
          onBlur={(e) => validatePincode(e.target.value)}
          placeholder="Enter 6-digit pincode"
          error={errors.pincode}
          startIcon={<MapPin className="w-4 h-4 text-gray-400" />}
          maxLength={6}
          pattern="[0-9]{6}"
          required
        />

        {/* Area Name */}
        <Input
          label="Area Name"
          value={formData.areaName}
          onChange={(e) => handleInputChange('areaName', e.target.value)}
          placeholder="Enter area name"
          error={errors.areaName}
          startIcon={<Building className="w-4 h-4 text-gray-400" />}
          required
        />

        {/* Branch Code */}
        <Input
          label="Branch Code"
          value={formData.branchCode}
          onChange={(e) => handleInputChange('branchCode', e.target.value)}
          placeholder="Enter branch code"
          error={errors.branchCode}
          required
        />

        {/* Branch Name */}
        <Input
          label="Branch Name"
          value={formData.branchName}
          onChange={(e) => handleInputChange('branchName', e.target.value)}
          placeholder="Enter branch name"
          error={errors.branchName}
          required
        />

        {/* Delivery Charge */}
        <Input
          label="Delivery Charge (₹)"
          type="number"
          value={formData.deliveryCharge}
          onChange={(e) => handleInputChange('deliveryCharge', e.target.value)}
          placeholder="0"
          error={errors.deliveryCharge}
          startIcon={<IndianRupee className="w-4 h-4 text-gray-400" />}
          min="0"
          step="0.01"
          required
        />

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-4 border-t">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="primary"
            disabled={isValidating}
          >
            {isEditing ? 'Update Delivery Charge' : 'Add Delivery Charge'}
          </Button>
        </div>
      </form>
    </Modal>
  );
};
