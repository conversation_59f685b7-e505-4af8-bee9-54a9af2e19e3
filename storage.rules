rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Temporary rule to allow all read and write operations until October 5, 2024
    // This must be the FIRST rule to take precedence
    match /{allPaths=**} {
      allow read, write: if request.time < timestamp.date(2024, 10, 5);
    }

    // The rules below will only apply AFTER October 5, 2024
    // They are included here for future reference

    // Allow public read access to all files
    match /{allPaths=**} {
      allow read: if true;
    }

    // Allow authenticated users to write to product images
    match /products/{productId}/{fileName} {
      allow write: if request.auth != null;
    }

    // Allow users to access their own files
    match /users/{userId}/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.uid == userId;
    }

    // Default deny rule - this should always be last
    match /{allPaths=**} {
      allow write: if false;
    }
  }
}
