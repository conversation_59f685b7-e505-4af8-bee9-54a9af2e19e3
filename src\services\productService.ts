import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  serverTimestamp,
  Timestamp
} from 'firebase/firestore';
import { db } from '../firebase/config';
import { Product } from '../types';
import { uploadFile, deleteFile } from '../firebase/storage';
import { checkAuth } from '../services/authService';

// Collection name
const COLLECTION_NAME = 'Products';

// Helper function to convert Date objects to Firestore timestamps
const convertDateToTimestamp = (date: any): Timestamp | null => {
  if (!date) return null;

  try {
    // If it's already a Firestore timestamp, return as is
    if (date.toDate && typeof date.toDate === 'function') {
      return date;
    }

    // Convert Date object or string to Firestore timestamp
    return Timestamp.fromDate(new Date(date));
  } catch (error) {
    console.error('Error converting date to timestamp:', error);
    return null;
  }
};

// Map Firestore document to Product type
const mapDocToProduct = (doc: any): Product => {
  const data = doc.data();
  console.log('Mapping product from Firestore:', doc.id, {
    categoryID: data.categoryID,
    subcategoryID: data.subcategoryID,
    farmerId: data.farmerId // Log farmerId for debugging
  });

  return {
    id: doc.id,
    name: data.name || '',
    description: data.description || '',
    // Legacy fields for backward compatibility
    category: data.categoryID || (data.categoryIDs && data.categoryIDs[0]) || '',
    subcategory: data.subcategoryID || '',
    // New multi-category system
    categoryIDs: data.categoryIDs || (data.categoryID ? [data.categoryID] : []),
    primaryCategoryID: data.primaryCategoryID || data.categoryID || '',
    images: data.image ? [data.image] : [],
    price: data.price || 0,
    mrp: data.mrp || 0, // Fetch MRP from database
    stock: data.maxQuantity || 0,
    unit: data.unit || 'kg',
    sku: data.barcode || '',
    isVisible: data.status === 'active',
    status: data.status === 'active' ? 'available' : 'out_of_stock',
    farmerId: data.farmerId || '',
    nutrition: data.nutrition || '',
    storageInstruction: data.storageInstruction || '',
    // New variant system
    hasVariants: data.hasVariants || false,
    defaultVariantID: data.defaultVariantID || '',
    // New harvest day and pre-order system
    isPreOrder: data.isPreOrder || false,
    preOrderStartAt: data.preOrderStartAt || null,
    preOrderEndAt: data.preOrderEndAt || null,
    harvestOffsetDays: data.harvestOffsetDays !== undefined ? data.harvestOffsetDays : 0,
    createdAt: data.createdAt ? new Date(data.createdAt.toDate()).toISOString() : new Date().toISOString(),
    updatedAt: data.updatedAt ? new Date(data.updatedAt.toDate()).toISOString() : new Date().toISOString(),
  };
};

// Map Product type to Firestore document
const mapProductToDoc = (product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): any => {
  return {
    name: product.name,
    description: product.description || '',
    // Legacy fields for backward compatibility
    categoryID: product.category,
    subcategoryID: product.subcategory || null,
    // New multi-category system
    categoryIDs: product.categoryIDs || (product.category ? [product.category] : []),
    primaryCategoryID: product.primaryCategoryID || product.category || '',
    image: product.images && product.images.length > 0 ? product.images[0] : '',
    price: product.price,
    mrp: product.mrp || 0, // Use actual MRP value or 0 if not provided
    maxQuantity: product.stock,
    minQuantity: 1,
    incrementalQuantity: 1,
    unit: product.unit,
    barcode: product.sku,
    status: product.isVisible ? 'active' : 'inActive',
    keyword: [product.name.toLowerCase()], // Basic keyword for searching
    index: 0, // Default index
    branchCode: 'main', // Default branch code
    farmerId: product.farmerId || null,
    nutrition: product.nutrition || '',
    storageInstruction: product.storageInstruction || '',
    // New variant system
    hasVariants: product.hasVariants || false,
    defaultVariantID: product.defaultVariantID || null,
    // New harvest day and pre-order system
    isPreOrder: product.isPreOrder || false,
    preOrderStartAt: convertDateToTimestamp(product.preOrderStartAt),
    preOrderEndAt: convertDateToTimestamp(product.preOrderEndAt),
    harvestOffsetDays: product.harvestOffsetDays !== undefined ? product.harvestOffsetDays : 0,
  };
};

// Get all products
export const getAllProducts = async (): Promise<Product[]> => {
  try {
    const productsRef = collection(db, COLLECTION_NAME);
    const q = query(productsRef, orderBy('name'));
    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map(mapDocToProduct);
  } catch (error) {
    console.error('Error getting products:', error);
    return [];
  }
};

// Get product by ID
export const getProductById = async (id: string): Promise<Product | null> => {
  try {
    const docRef = doc(db, COLLECTION_NAME, id);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      return mapDocToProduct(docSnap);
    } else {
      return null;
    }
  } catch (error) {
    console.error('Error getting product:', error);
    return null;
  }
};

// Add a new product
export const addProduct = async (
  product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>
): Promise<string | null> => {
  try {
    console.log('Adding product with farmerId:', product.farmerId);

    const docData = {
      ...mapProductToDoc(product),
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    };

    console.log('Document data being saved to Firestore:', docData);

    const docRef = await addDoc(collection(db, COLLECTION_NAME), docData);
    console.log('Product added successfully with ID:', docRef.id);
    return docRef.id;
  } catch (error) {
    console.error('Error adding product:', error);
    return null;
  }
};

// Update a product
export const updateProduct = async (
  id: string,
  updates: Partial<Product>
): Promise<boolean> => {
  try {
    const docRef = doc(db, COLLECTION_NAME, id);
    const docSnap = await getDoc(docRef);

    if (!docSnap.exists()) {
      console.error('Product not found');
      return false;
    }

    // Convert Product updates to Firestore document updates
    const updateData: any = {};

    if (updates.name !== undefined) updateData.name = updates.name;
    if (updates.tamilName !== undefined) updateData.tamilName = updates.tamilName;
    if (updates.description !== undefined) updateData.description = updates.description;
    // Legacy category fields
    if (updates.category !== undefined) updateData.categoryID = updates.category;
    if (updates.subcategory !== undefined) updateData.subcategoryID = updates.subcategory;
    // New multi-category system
    if (updates.categoryIDs !== undefined) updateData.categoryIDs = updates.categoryIDs;
    if (updates.primaryCategoryID !== undefined) updateData.primaryCategoryID = updates.primaryCategoryID;
    if (updates.price !== undefined) {
      updateData.price = updates.price;
    }
    if (updates.mrp !== undefined) {
      updateData.mrp = updates.mrp;
    }
    // Handle farmerId field - ensure it's properly saved to Firestore
    if (updates.farmerId !== undefined) {
      console.log('Setting farmerId in updateData:', updates.farmerId);
      updateData.farmerId = updates.farmerId || null;
    }
    // Handle nutrition and storage instruction fields
    if (updates.nutrition !== undefined) {
      updateData.nutrition = updates.nutrition;
    }
    if (updates.storageInstruction !== undefined) {
      updateData.storageInstruction = updates.storageInstruction;
    }
    if (updates.stock !== undefined) updateData.maxQuantity = updates.stock;
    if (updates.unit !== undefined) updateData.unit = updates.unit;
    if (updates.sku !== undefined) updateData.barcode = updates.sku;
    if (updates.isVisible !== undefined) updateData.status = updates.isVisible ? 'active' : 'inActive';
    if (updates.images !== undefined && updates.images.length > 0) {
      updateData.image = updates.images[0];
    }
    // New variant system fields
    if (updates.hasVariants !== undefined) updateData.hasVariants = updates.hasVariants;
    if (updates.defaultVariantID !== undefined) updateData.defaultVariantID = updates.defaultVariantID;
    // New harvest day and pre-order system fields
    if (updates.isPreOrder !== undefined) updateData.isPreOrder = updates.isPreOrder;
    if (updates.preOrderStartAt !== undefined) updateData.preOrderStartAt = convertDateToTimestamp(updates.preOrderStartAt);
    if (updates.preOrderEndAt !== undefined) updateData.preOrderEndAt = convertDateToTimestamp(updates.preOrderEndAt);
    if (updates.harvestOffsetDays !== undefined) updateData.harvestOffsetDays = updates.harvestOffsetDays;

    // Add updatedAt timestamp
    updateData.updatedAt = serverTimestamp();

    console.log('Final updateData being sent to Firestore:', updateData);

    await updateDoc(docRef, updateData);
    console.log('Product updated successfully with ID:', id);
    return true;
  } catch (error) {
    console.error('Error updating product:', error);
    return false;
  }
};

// Delete a product
export const deleteProduct = async (id: string): Promise<boolean> => {
  try {
    const docRef = doc(db, COLLECTION_NAME, id);
    await deleteDoc(docRef);
    return true;
  } catch (error) {
    console.error('Error deleting product:', error);
    return false;
  }
};

// Get products by category
export const getProductsByCategory = async (categoryId: string): Promise<Product[]> => {
  try {
    const productsRef = collection(db, COLLECTION_NAME);
    const q = query(
      productsRef,
      where('categoryID', '==', categoryId),
      orderBy('name')
    );
    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map(mapDocToProduct);
  } catch (error) {
    console.error('Error getting products by category:', error);
    return [];
  }
};

// Upload product image and return URL
export const uploadProductImage = async (
  file: File,
  productId: string
): Promise<string | null> => {
  const path = `products/${productId}/${file.name}`;
  return await uploadFile(file, path);
};

// Get all categories with subcategories
export const getAllCategories = async (): Promise<Category[]> => {
  try {
    const categoriesRef = collection(db, 'Categories');
    const querySnapshot = await getDocs(categoriesRef);

    const categories = [];
    const subcategoriesMap = {};

    // First pass: collect all categories
    querySnapshot.docs.forEach(doc => {
      const data = doc.data();

      // Check if this is a subcategory (has parentCategoryID)
      if (data.parentCategoryID) {
        // Store subcategories temporarily by parent ID
        if (!subcategoriesMap[data.parentCategoryID]) {
          subcategoriesMap[data.parentCategoryID] = [];
        }

        subcategoriesMap[data.parentCategoryID].push({
          id: doc.id,
          name: data.name || '',
          tamilName: data.tamilName || '',
          branchCode: data.branchCode || '',
          image: data.image || '',
          index: data.index || 0,
          parentCategoryID: data.parentCategoryID
        });
      } else {
        // This is a parent category
        categories.push({
          id: doc.id,
          name: data.name || '',
          tamilName: data.tamilName || '',
          branchCode: data.branchCode || '',
          image: data.image || '',
          index: data.index || 0,
          subcategories: [] // Will be populated in second pass
        });
      }
    });

    // Second pass: add subcategories to their parent categories
    categories.forEach(category => {
      if (subcategoriesMap[category.id]) {
        category.subcategories = subcategoriesMap[category.id];
      }
    });

    console.log('Processed categories with subcategories:', categories);
    return categories;
  } catch (error) {
    console.error('Error getting categories:', error);
    return [];
  }
};
