import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import {
  Package2,
  Plus,
  Filter,
  Search,
  RefreshCw,
  AlertCircle,
  X,
  Tag,
  ChevronRight,
  ChevronLeft,
  Layers,
  ChevronsLeft,
  ChevronsRight
} from 'lucide-react';
import { Card } from '../../components/ui/Card';
import { Badge } from '../../components/ui/Badge';
import { Button } from '../../components/ui/Button';
import { ProductStatusBadge } from '../../components/ui/StatusBadge';
import { useProducts } from '../../contexts/ProductContext';
import { getAllCategories } from '../../services/productService';
import { formatHarvestDate, getPreOrderStatusText, isPreOrderActive } from '../../utils/harvestUtils';
import { BulkProductManager } from '../../components/products/BulkProductManager';

export function ProductsList() {
  const { products, loading, error, refreshProducts } = useProducts();
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [searchQuery, setSearchQuery] = useState('');
  const [categories, setCategories] = useState([{ id: 'All', name: 'All' }]);
  const [categoryMap, setCategoryMap] = useState({});
  // Map from subcategory ID to parent category ID
  const [subcategoryParentMap, setSubcategoryParentMap] = useState({});
  const [showClearSearch, setShowClearSearch] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const categoryScrollRef = useRef<HTMLDivElement>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [productsPerPage] = useState(20);

  // Fetch categories and create a mapping from ID to name
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        console.log('Fetching categories...');
        const fetchedCategories = await getAllCategories();
        console.log('Fetched categories:', fetchedCategories);

        // Store only main categories (not subcategories) for the filter
        // Main categories are those without a parentCategoryID
        const mainCategories = fetchedCategories.filter(cat => {
          // Check if this is a main category (no parentCategoryID)
          const isMainCategory = !cat.parentCategoryID;
          console.log(`Category ${cat.name} (${cat.id}) is main category: ${isMainCategory}`);
          return isMainCategory;
        });

        const mainCategoryObjects = [
          { id: 'All', name: 'All' },
          ...mainCategories.map(cat => ({ id: cat.id, name: cat.name }))
        ];
        setCategories(mainCategoryObjects);
        console.log('Main category objects for UI:', mainCategoryObjects);

        // Create a mapping from category ID to name
        const mapping = {};
        // Create a mapping from subcategory ID to parent category ID
        const parentMapping = {};

        fetchedCategories.forEach(cat => {
          // Map category ID to name
          mapping[cat.id] = cat.name;
          console.log(`Mapping category ID ${cat.id} to name ${cat.name}`);

          // Also add mappings for any subcategories
          if (cat.subcategories && cat.subcategories.length > 0) {
            cat.subcategories.forEach(subcat => {
              // Map subcategory ID to name
              mapping[subcat.id] = subcat.name;
              console.log(`Mapping subcategory ID ${subcat.id} to name ${subcat.name}`);

              // Map subcategory ID to parent category ID
              parentMapping[subcat.id] = cat.id;
              console.log(`Mapping subcategory ${subcat.name} (${subcat.id}) to parent category ${cat.name} (${cat.id})`);
            });
          }
        });

        setCategoryMap(mapping);
        setSubcategoryParentMap(parentMapping);

        console.log('Complete category mapping:', mapping);

        // Debug: Check if any products have categories that aren't in the mapping
        setTimeout(() => {
          if (products.length > 0) {
            products.forEach(product => {
              if (product.category && !mapping[product.category]) {
                console.warn(`Product ${product.name} has category ID ${product.category} which is not in the mapping`);
              }
            });
          }
        }, 1000);
      } catch (error) {
        console.error('Error fetching categories:', error);
      }
    };

    fetchCategories();
  }, [products]);

  // Handle refresh button click
  const handleRefresh = () => {
    refreshProducts();
  };

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    setShowClearSearch(value.length > 0);
  };

  // Clear search input
  const clearSearch = () => {
    setSearchQuery('');
    setShowClearSearch(false);
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  };

  // Scroll category carousel
  const scrollCategories = (direction: 'left' | 'right') => {
    if (categoryScrollRef.current) {
      // Get the width of the container
      const containerWidth = categoryScrollRef.current.clientWidth;
      // Scroll by 80% of the container width
      const scrollAmount = direction === 'left' ? -containerWidth * 0.8 : containerWidth * 0.8;
      categoryScrollRef.current.scrollBy({ left: scrollAmount, behavior: 'smooth' });
    }
  };

  const filteredProducts = products.filter(product => {
    // Get the category name for display purposes
    const categoryName = categoryMap[product.category] || 'Unknown';

    // Check if this product's category is a subcategory
    const parentCategoryId = subcategoryParentMap[product.category];
    const isSubcategory = !!parentCategoryId;

    // A product matches the selected category if:
    // 1. "All" is selected, OR
    // 2. The product's category matches the selected category directly, OR
    // 3. The product's category is a subcategory of the selected category
    const matchesCategory =
      selectedCategory === 'All' ||
      product.category === selectedCategory ||
      (isSubcategory && parentCategoryId === selectedCategory);

    console.log('Filtering product:', product.name, {
      productCategory: product.category,
      categoryName: categoryName,
      isSubcategory: isSubcategory,
      parentCategoryId: parentCategoryId,
      selectedCategory: selectedCategory,
      matchesCategory: matchesCategory
    });

    const matchesSearch =
      product.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (product.tamilName && product.tamilName.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (product.barcode && product.barcode.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (product.description && product.description.toLowerCase().includes(searchQuery.toLowerCase()));

    return matchesCategory && matchesSearch;
  });

  // Function to get category name from ID
  const getCategoryName = (categoryId: string) => {
    if (!categoryId) {
      console.log('No category ID provided');
      return 'Uncategorized';
    }

    console.log('Getting category name for ID:', categoryId);
    console.log('Category map keys:', Object.keys(categoryMap));
    console.log('Category map values:', Object.values(categoryMap));

    // Try to find the category name in the map
    const categoryName = categoryMap[categoryId];

    if (!categoryName) {
      console.log('Category name not found for ID:', categoryId);
      // Return the ID as a fallback instead of "Uncategorized"
      return categoryId;
    }

    return categoryName;
  };

  // Function to get the appropriate tag to display (subcategory if available, otherwise category)
  const getProductTag = (product) => {
    // If product has a subcategory, use that
    if (product.subcategory && categoryMap[product.subcategory]) {
      return categoryMap[product.subcategory];
    }

    // Check if the product's category is actually a subcategory
    const isSubcategory = !!subcategoryParentMap[product.category];

    // If the product's category is a subcategory, use that
    if (isSubcategory) {
      return getCategoryName(product.category);
    }

    // Otherwise, use the main category
    return getCategoryName(product.category);
  };

  // Calculate pagination
  const indexOfLastProduct = currentPage * productsPerPage;
  const indexOfFirstProduct = indexOfLastProduct - productsPerPage;
  const currentProducts = filteredProducts.slice(indexOfFirstProduct, indexOfLastProduct);
  const totalPages = Math.ceil(filteredProducts.length / productsPerPage);

  // Change page
  const goToPage = (pageNumber) => {
    setCurrentPage(Math.max(1, Math.min(pageNumber, totalPages)));
  };

  // Pagination component
  const Pagination = () => {
    if (totalPages <= 1) return null;

    return (
      <div className="flex justify-center items-center mt-8 gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => goToPage(1)}
          disabled={currentPage === 1}
        >
          <ChevronsLeft className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => goToPage(currentPage - 1)}
          disabled={currentPage === 1}
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>

        <div className="flex items-center gap-1 mx-2">
          <span className="text-sm font-medium">
            Page {currentPage} of {totalPages}
          </span>
        </div>

        <Button
          variant="outline"
          size="sm"
          onClick={() => goToPage(currentPage + 1)}
          disabled={currentPage === totalPages}
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => goToPage(totalPages)}
          disabled={currentPage === totalPages}
        >
          <ChevronsRight className="h-4 w-4" />
        </Button>
      </div>
    );
  };

  return (
    <div className="p-6 animate-fade-in">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Products</h1>
          <p className="text-gray-600">Manage your product inventory and categories</p>
        </div>
        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={loading}
            className="shadow-sm hover:shadow transition-shadow"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Link to="/products/new">
            <Button
              variant="primary"
              className="shadow-md hover:shadow-lg transition-shadow"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Product
            </Button>
          </Link>
        </div>
      </div>

      {/* Bulk Product Management */}
      <Card className="mb-6">
        <div className="p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Bulk Product Management</h2>
          <BulkProductManager onImportComplete={refreshProducts} />
        </div>
      </Card>

      {/* Error message */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center text-red-700">
          <AlertCircle className="w-5 h-5 mr-2" />
          <span>{error}</span>
        </div>
      )}

      {/* Search Bar */}
      <div className="mb-4">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            ref={searchInputRef}
            type="text"
            placeholder="Search products by name, barcode, or description..."
            className="pl-10 pr-10 py-3 w-full text-base border-2 border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
            value={searchQuery}
            onChange={handleSearchChange}
          />
          {showClearSearch && (
            <button
              className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
              onClick={clearSearch}
            >
              <X className="h-5 w-5" />
            </button>
          )}
        </div>
      </div>

      {/* Category Filter */}
      <div className="mb-6">
        <div className="flex items-center mb-2">
          <Tag className="h-4 w-4 text-primary-500 mr-2" />
          <span className="text-sm font-medium text-gray-700">Categories</span>
        </div>

        <div className="relative">
          <div className="absolute left-0 top-1/2 -translate-y-1/2 z-10">
            <button
              className="flex items-center justify-center h-8 w-8 rounded-full bg-white shadow-md text-gray-600 hover:text-primary-500 hover:bg-primary-50 transition-colors"
              onClick={() => scrollCategories('left')}
            >
              <ChevronLeft className="h-5 w-5" />
            </button>
          </div>

          <div className="px-10">
            <div
              ref={categoryScrollRef}
              className="flex gap-3 overflow-x-auto py-2 scrollbar-hide scroll-smooth"
              style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
            >
              {categories.map(category => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`
                    flex items-center px-4 py-2 rounded-lg transition-all duration-200 whitespace-nowrap
                    ${selectedCategory === category.id
                      ? 'bg-primary-500 text-white shadow-md'
                      : 'bg-white border-2 border-gray-200 text-gray-700 hover:border-primary-200 hover:bg-primary-50'
                    }
                  `}
                >
                  {category.id === 'All' ?
                    <Layers className="h-4 w-4 mr-2" /> :
                    <Tag className="h-4 w-4 mr-2" />
                  }
                  <span className="font-medium">{category.name}</span>
                </button>
              ))}
            </div>
          </div>

          <div className="absolute right-0 top-1/2 -translate-y-1/2 z-10">
            <button
              className="flex items-center justify-center h-8 w-8 rounded-full bg-white shadow-md text-gray-600 hover:text-primary-500 hover:bg-primary-50 transition-colors"
              onClick={() => scrollCategories('right')}
            >
              <ChevronRight className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>

      {/* Loading state */}
      {loading ? (
        <div className="flex justify-center items-center h-40">
          <div className="flex items-center gap-3 bg-white px-6 py-4 rounded-lg shadow-sm border border-gray-200">
            <RefreshCw className="w-6 h-6 text-primary-500 animate-spin" />
            <p className="text-gray-600">Loading products...</p>
          </div>
        </div>
      ) : filteredProducts.length === 0 ? (
        <Card className="p-6 border border-gray-200 shadow-sm">
          <div className="flex flex-col sm:flex-row items-center text-center sm:text-left gap-4">
            <div className="bg-gray-100 p-4 rounded-full">
              <Package2 className="w-10 h-10 text-gray-400" />
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-gray-800 mb-1">No products found</h3>
              <p className="text-gray-600 text-sm mb-4">
                Try adjusting your search or filters to find what you're looking for.
              </p>
              <div className="flex flex-wrap justify-center sm:justify-start gap-3">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSearchQuery('');
                    setSelectedCategory('All');
                    setShowClearSearch(false);
                  }}
                >
                  Clear filters
                </Button>
                <Link to="/products/new">
                  <Button variant="primary" size="sm">
                    <Plus className="w-3 h-3 mr-1" />
                    Add product
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </Card>
      ) : (
        <>
          <div className="grid gap-5 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {currentProducts.map((product) => (
              <Link key={product.id} to={`/products/${product.id}`} className="group">
                <Card className="h-full overflow-hidden border border-gray-200 hover:border-primary-200 hover:shadow-md transition-all duration-300">
                  <div className="aspect-square overflow-hidden relative">
                    <img
                      src={product.images && product.images.length > 0 ? product.images[0] : '/placeholder-image.png'}
                      alt={product.name}
                      className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                      onError={(e) => {
                        // Use a data URI as fallback to ensure it always works
                        e.currentTarget.src = 'data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%22300%22%20height%3D%22300%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Crect%20width%3D%22300%22%20height%3D%22300%22%20fill%3D%22%23f0f0f0%22%2F%3E%3Ctext%20x%3D%22150%22%20y%3D%22150%22%20font-size%3D%2220%22%20text-anchor%3D%22middle%22%20alignment-baseline%3D%22middle%22%20fill%3D%22%23999%22%3ENo%20Image%3C%2Ftext%3E%3C%2Fsvg%3E';
                      }}
                    />
                    <div className="absolute top-2 right-2">
                      <ProductStatusBadge status={product.status || 'available'} />
                    </div>
                  </div>
                  <div className="p-4">
                    <div className="mb-3">
                      <div className="flex flex-wrap gap-1 mb-2">
                        <Badge variant="secondary">{getProductTag(product)}</Badge>
                        {isPreOrderActive(product) && (
                          <Badge variant="primary" className="text-xs">
                            {getPreOrderStatusText(product)}
                          </Badge>
                        )}
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900 group-hover:text-primary-600 transition-colors line-clamp-1">
                        {product.name}
                      </h3>
                      {product.harvestOffsetDays !== undefined && (
                        <p className="text-xs text-green-600 mt-1">
                          {formatHarvestDate(product.harvestOffsetDays)}
                        </p>
                      )}
                    </div>

                    <div className="flex justify-between items-end">
                      <div>
                        <p className="text-sm text-gray-500 mb-1">
                          Barcode: {product.barcode || 'N/A'}
                        </p>
                        <div className="flex items-center gap-2">
                          <p className="text-lg font-bold text-primary-600">
                            ₹{product.price}
                            <span className="text-sm font-normal text-gray-500">/{product.unit}</span>
                          </p>
                          {product.mrp && product.mrp > product.price && (
                            <p className="text-sm text-gray-500 line-through">
                              ₹{product.mrp}
                            </p>
                          )}
                        </div>
                      </div>
                      <div className="bg-gray-100 px-3 py-1 rounded-full">
                        <span className={`text-sm font-medium ${product.stock > 10 ? 'text-green-600' : product.stock > 0 ? 'text-amber-600' : 'text-red-600'}`}>
                          {product.stock > 0 ? `${product.stock} in stock` : 'Out of stock'}
                        </span>
                      </div>
                    </div>
                  </div>
                </Card>
              </Link>
            ))}
          </div>

          {/* Add pagination component */}
          <Pagination />
        </>
      )}
    </div>
  );
}


