import React, { useState, useEffect } from 'react';
import { X, RefreshCw, Calendar, Percent, IndianRupee } from 'lucide-react';
import { Button } from '../ui/Button';
import { Modal } from '../ui/Modal';
import { Input } from '../ui/Input';
import { Select } from '../ui/Select';
import { Textarea } from '../ui/Textarea';
import { Coupon } from '../../types';
import { generateCouponCode, checkCouponCodeExists } from '../../services/couponService';

interface CouponFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (coupon: Omit<Coupon, 'id' | 'createdAt' | 'updatedAt'>) => void;
  initialData?: Coupon;
  isEditing?: boolean;
}

export const CouponForm = ({
  isOpen,
  onClose,
  onSave,
  initialData,
  isEditing = false
}: CouponFormProps) => {
  const [formData, setFormData] = useState({
    code: '',
    type: 'FLAT' as 'FLAT' | 'PERCENT',
    flatAmount: '',
    percent: '',
    maxDiscount: '',
    minCartTotal: '',
    startAt: '',
    expiresAt: '',
    usageLimitGlobal: '',
    usageLimitPerUser: '',
    isActive: true,
    description: ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isGenerating, setIsGenerating] = useState(false);
  const [isValidating, setIsValidating] = useState(false);

  // Load initial data if editing
  useEffect(() => {
    if (initialData && isOpen) {
      setFormData({
        code: initialData.code,
        type: initialData.type,
        flatAmount: initialData.flatAmount?.toString() || '',
        percent: initialData.percent?.toString() || '',
        maxDiscount: initialData.maxDiscount?.toString() || '',
        minCartTotal: initialData.minCartTotal?.toString() || '',
        startAt: initialData.startAt ? new Date(initialData.startAt).toISOString().slice(0, 16) : '',
        expiresAt: new Date(initialData.expiresAt).toISOString().slice(0, 16),
        usageLimitGlobal: initialData.usageLimitGlobal?.toString() || '',
        usageLimitPerUser: initialData.usageLimitPerUser?.toString() || '',
        isActive: initialData.isActive,
        description: initialData.description || ''
      });
    } else if (!isEditing && isOpen) {
      // Reset form for new coupon
      setFormData({
        code: '',
        type: 'FLAT',
        flatAmount: '',
        percent: '',
        maxDiscount: '',
        minCartTotal: '',
        startAt: '',
        expiresAt: '',
        usageLimitGlobal: '',
        usageLimitPerUser: '',
        isActive: true,
        description: ''
      });
    }
    setErrors({});
  }, [initialData, isOpen, isEditing]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const generateRandomCode = async () => {
    setIsGenerating(true);
    try {
      let newCode;
      let attempts = 0;
      do {
        newCode = generateCouponCode(8);
        attempts++;
      } while (await checkCouponCodeExists(newCode) && attempts < 10);
      
      if (attempts < 10) {
        handleInputChange('code', newCode);
      } else {
        setErrors(prev => ({ ...prev, code: 'Unable to generate unique code. Please try again.' }));
      }
    } catch (error) {
      setErrors(prev => ({ ...prev, code: 'Error generating code. Please try again.' }));
    } finally {
      setIsGenerating(false);
    }
  };

  const validateCode = async (code: string) => {
    if (!code) return;
    if (isEditing && code === initialData?.code) return; // Skip validation if editing and code hasn't changed
    
    setIsValidating(true);
    try {
      const exists = await checkCouponCodeExists(code);
      if (exists) {
        setErrors(prev => ({ ...prev, code: 'This coupon code already exists' }));
      } else {
        setErrors(prev => ({ ...prev, code: '' }));
      }
    } catch (error) {
      setErrors(prev => ({ ...prev, code: 'Error validating code' }));
    } finally {
      setIsValidating(false);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Required fields
    if (!formData.code.trim()) {
      newErrors.code = 'Coupon code is required';
    } else if (formData.code.length < 3) {
      newErrors.code = 'Coupon code must be at least 3 characters';
    }

    if (!formData.expiresAt) {
      newErrors.expiresAt = 'Expiry date is required';
    } else if (new Date(formData.expiresAt) <= new Date()) {
      newErrors.expiresAt = 'Expiry date must be in the future';
    }

    // Type-specific validation
    if (formData.type === 'FLAT') {
      if (!formData.flatAmount || parseFloat(formData.flatAmount) <= 0) {
        newErrors.flatAmount = 'Flat amount must be greater than 0';
      }
    } else if (formData.type === 'PERCENT') {
      if (!formData.percent || parseFloat(formData.percent) <= 0 || parseFloat(formData.percent) > 100) {
        newErrors.percent = 'Percentage must be between 1 and 100';
      }
    }

    // Optional field validation
    if (formData.minCartTotal && parseFloat(formData.minCartTotal) < 0) {
      newErrors.minCartTotal = 'Minimum cart total cannot be negative';
    }

    if (formData.maxDiscount && parseFloat(formData.maxDiscount) <= 0) {
      newErrors.maxDiscount = 'Maximum discount must be greater than 0';
    }

    if (formData.usageLimitGlobal && parseInt(formData.usageLimitGlobal) <= 0) {
      newErrors.usageLimitGlobal = 'Global usage limit must be greater than 0';
    }

    if (formData.usageLimitPerUser && parseInt(formData.usageLimitPerUser) <= 0) {
      newErrors.usageLimitPerUser = 'Per-user usage limit must be greater than 0';
    }

    // Start date validation
    if (formData.startAt && formData.expiresAt && new Date(formData.startAt) >= new Date(formData.expiresAt)) {
      newErrors.startAt = 'Start date must be before expiry date';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    const couponData: Omit<Coupon, 'id' | 'createdAt' | 'updatedAt'> = {
      code: formData.code.trim().toUpperCase(),
      type: formData.type,
      usedCount: initialData?.usedCount || 0,
      isActive: formData.isActive,
      expiresAt: new Date(formData.expiresAt).toISOString(),
      ...(formData.type === 'FLAT' && formData.flatAmount && { flatAmount: parseFloat(formData.flatAmount) }),
      ...(formData.type === 'PERCENT' && formData.percent && { percent: parseFloat(formData.percent) }),
      ...(formData.maxDiscount && { maxDiscount: parseFloat(formData.maxDiscount) }),
      ...(formData.minCartTotal && { minCartTotal: parseFloat(formData.minCartTotal) }),
      ...(formData.startAt && { startAt: new Date(formData.startAt).toISOString() }),
      ...(formData.usageLimitGlobal && { usageLimitGlobal: parseInt(formData.usageLimitGlobal) }),
      ...(formData.usageLimitPerUser && { usageLimitPerUser: parseInt(formData.usageLimitPerUser) }),
      ...(formData.description.trim() && { description: formData.description.trim() })
    };

    onSave(couponData);
  };

  const typeOptions = [
    { value: 'FLAT', label: 'Flat Amount (₹)' },
    { value: 'PERCENT', label: 'Percentage (%)' }
  ];

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={isEditing ? 'Edit Coupon' : 'Create New Coupon'}
      maxWidth="lg"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Coupon Code */}
        <div className="flex gap-2">
          <div className="flex-1">
            <Input
              label="Coupon Code"
              value={formData.code}
              onChange={(e) => handleInputChange('code', e.target.value.toUpperCase())}
              onBlur={(e) => validateCode(e.target.value)}
              placeholder="Enter coupon code"
              error={errors.code}
              disabled={isEditing} // Don't allow editing code for existing coupons
              required
            />
          </div>
          {!isEditing && (
            <div className="flex items-end">
              <Button
                type="button"
                variant="outline"
                onClick={generateRandomCode}
                disabled={isGenerating}
                className="mb-1"
              >
                {isGenerating ? (
                  <RefreshCw className="w-4 h-4 animate-spin" />
                ) : (
                  <RefreshCw className="w-4 h-4" />
                )}
              </Button>
            </div>
          )}
        </div>

        {/* Coupon Type */}
        <Select
          label="Discount Type"
          value={formData.type}
          onChange={(e) => handleInputChange('type', e.target.value)}
          options={typeOptions}
          required
        />

        {/* Amount/Percentage Fields */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {formData.type === 'FLAT' ? (
            <Input
              label="Flat Amount (₹)"
              type="number"
              value={formData.flatAmount}
              onChange={(e) => handleInputChange('flatAmount', e.target.value)}
              placeholder="0"
              error={errors.flatAmount}
              startIcon={<IndianRupee className="w-4 h-4 text-gray-400" />}
              min="0"
              step="0.01"
              required
            />
          ) : (
            <Input
              label="Percentage (%)"
              type="number"
              value={formData.percent}
              onChange={(e) => handleInputChange('percent', e.target.value)}
              placeholder="0"
              error={errors.percent}
              startIcon={<Percent className="w-4 h-4 text-gray-400" />}
              min="0"
              max="100"
              step="0.01"
              required
            />
          )}

          {formData.type === 'PERCENT' && (
            <Input
              label="Maximum Discount (₹)"
              type="number"
              value={formData.maxDiscount}
              onChange={(e) => handleInputChange('maxDiscount', e.target.value)}
              placeholder="Optional"
              error={errors.maxDiscount}
              startIcon={<IndianRupee className="w-4 h-4 text-gray-400" />}
              min="0"
              step="0.01"
            />
          )}
        </div>

        {/* Minimum Cart Total */}
        <Input
          label="Minimum Cart Total (₹)"
          type="number"
          value={formData.minCartTotal}
          onChange={(e) => handleInputChange('minCartTotal', e.target.value)}
          placeholder="Optional"
          error={errors.minCartTotal}
          startIcon={<IndianRupee className="w-4 h-4 text-gray-400" />}
          min="0"
          step="0.01"
        />

        {/* Date Fields */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            label="Start Date (Optional)"
            type="datetime-local"
            value={formData.startAt}
            onChange={(e) => handleInputChange('startAt', e.target.value)}
            error={errors.startAt}
            startIcon={<Calendar className="w-4 h-4 text-gray-400" />}
          />
          <Input
            label="Expiry Date"
            type="datetime-local"
            value={formData.expiresAt}
            onChange={(e) => handleInputChange('expiresAt', e.target.value)}
            error={errors.expiresAt}
            startIcon={<Calendar className="w-4 h-4 text-gray-400" />}
            required
          />
        </div>

        {/* Usage Limits */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            label="Global Usage Limit"
            type="number"
            value={formData.usageLimitGlobal}
            onChange={(e) => handleInputChange('usageLimitGlobal', e.target.value)}
            placeholder="Unlimited"
            error={errors.usageLimitGlobal}
            min="1"
          />
          <Input
            label="Per-User Usage Limit"
            type="number"
            value={formData.usageLimitPerUser}
            onChange={(e) => handleInputChange('usageLimitPerUser', e.target.value)}
            placeholder="Unlimited"
            error={errors.usageLimitPerUser}
            min="1"
          />
        </div>

        {/* Description */}
        <Textarea
          label="Description (Optional)"
          value={formData.description}
          onChange={(e) => handleInputChange('description', e.target.value)}
          placeholder="Enter a description for this coupon"
          rows={3}
        />

        {/* Active Status */}
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="isActive"
            checked={formData.isActive}
            onChange={(e) => handleInputChange('isActive', e.target.checked)}
            className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
          />
          <label htmlFor="isActive" className="text-sm font-medium text-gray-700">
            Active
          </label>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-4 border-t">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="primary"
            disabled={isValidating}
          >
            {isEditing ? 'Update Coupon' : 'Create Coupon'}
          </Button>
        </div>
      </form>
    </Modal>
  );
};
