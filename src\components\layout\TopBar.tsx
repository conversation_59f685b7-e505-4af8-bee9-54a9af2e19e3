import { useEffect, useState, useRef } from 'react';
import { Bell, Menu, User } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { Link, useNavigate } from 'react-router-dom';

interface TopBarProps {
  toggleSidebar: () => void;
}

export const TopBar = ({ toggleSidebar }: TopBarProps) => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const [scrolled, setScrolled] = useState(false);
  const [showProfileMenu, setShowProfileMenu] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const [notificationCount, setNotificationCount] = useState(3);
  
  const profileRef = useRef<HTMLDivElement>(null);
  const notificationRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Close profile menu if clicked outside
      if (profileRef.current && !profileRef.current.contains(event.target as Node)) {
        setShowProfileMenu(false);
      }
      
      // Close notifications if clicked outside
      if (notificationRef.current && !notificationRef.current.contains(event.target as Node)) {
        setShowNotifications(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  return (
    <header
      className={`transition-all duration-300 border-b z-30 ${
        scrolled
          ? 'bg-white/95 backdrop-blur-sm shadow-sm'
          : 'bg-primary-500 text-white'
      }`}
    >
      <div className="flex items-center justify-between h-16 px-4 md:px-6">
        {/* Left section */}
        <div className="flex items-center">
          <button
            onClick={toggleSidebar}
            className="md:hidden mr-2 rounded-full p-2 hover:bg-primary-600/20 transition-colors"
          >
            <Menu className={`h-5 w-5 ${scrolled ? 'text-primary-500' : 'text-white'}`} />
          </button>

          <div className="hidden md:block">
            <h1 className={`text-lg font-semibold ${scrolled ? 'text-primary-600' : 'text-white'}`}>
              Vrisham Organic
            </h1>
          </div>
        </div>

        {/* Center section - Empty space */}
        <div className="flex-1 md:flex-grow"></div>

        {/* Right section */}
        <div className="flex items-center space-x-3">
          {/* Notifications */}
          <div className="relative" ref={notificationRef}>
            <button
              onClick={() => setShowNotifications(!showNotifications)}
              className={`rounded-full p-2 hover:bg-primary-600/20 transition-colors ${
                scrolled ? 'text-gray-600 hover:text-primary-500' : 'text-white'
              }`}
            >
              <Bell className="h-5 w-5" />
              {notificationCount > 0 && (
                <span className="absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full bg-secondary-500 text-[10px] font-bold text-white">
                  {notificationCount}
                </span>
              )}
            </button>

            {/* Notification dropdown */}
            {showNotifications && (
              <div className="absolute right-0 mt-2 w-80 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-50">
                <div className="px-4 py-3">
                  <p className="text-sm font-medium text-gray-900">Notifications</p>
                </div>
                <div className="py-1">
                  <Link
                    to="/orders"
                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    onClick={() => setShowNotifications(false)}
                  >
                    <p className="font-medium">New Order Received</p>
                    <p className="text-xs text-gray-500">Order #1234 - 5 mins ago</p>
                  </Link>
                  <Link
                    to="/orders"
                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    onClick={() => setShowNotifications(false)}
                  >
                    <p className="font-medium">Order Cancelled</p>
                    <p className="text-xs text-gray-500">Order #1230 - 2 hours ago</p>
                  </Link>
                  <Link
                    to="/products"
                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    onClick={() => setShowNotifications(false)}
                  >
                    <p className="font-medium">Low Stock Alert</p>
                    <p className="text-xs text-gray-500">Organic Tomatoes - 1 day ago</p>
                  </Link>
                </div>
                <div className="py-1">
                  <button
                    className="block w-full px-4 py-2 text-center text-sm font-medium text-primary-500 hover:bg-gray-100"
                    onClick={() => {
                      setNotificationCount(0);
                      setShowNotifications(false);
                    }}
                  >
                    Mark all as read
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Profile dropdown */}
          <div className="relative" ref={profileRef}>
            <button
              onClick={() => setShowProfileMenu(!showProfileMenu)}
              className="flex items-center space-x-2 rounded-full p-1 hover:bg-primary-600/20 transition-colors"
            >
              <div className="h-8 w-8 rounded-full overflow-hidden border-2 border-white">
                {user?.avatar ? (
                  <img
                    src={user.avatar}
                    alt={user.username}
                    className="h-full w-full object-cover"
                  />
                ) : (
                  <div className={`flex h-full w-full items-center justify-center bg-primary-200 ${
                    scrolled ? 'text-primary-500' : 'text-primary-500'
                  }`}>
                    <User className="h-4 w-4" />
                  </div>
                )}
              </div>
              <span className={`hidden md:block text-sm font-medium ${
                scrolled ? 'text-gray-800' : 'text-white'
              }`}>
                {user?.username}
              </span>
            </button>

            {showProfileMenu && (
              <div className="absolute right-0 mt-2 w-48 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-50">
                <div className="py-1">
                  <Link
                    to="/settings"
                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    onClick={() => setShowProfileMenu(false)}
                  >
                    Settings
                  </Link>
                  <button
                    onClick={() => {
                      handleLogout();
                      setShowProfileMenu(false);
                    }}
                    className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
                  >
                    Logout
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

