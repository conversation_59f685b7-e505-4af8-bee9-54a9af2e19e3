import { db, auth } from '../firebase/config';
import { collection, getDocs, query, where, orderBy, limit, Timestamp } from 'firebase/firestore';
import { Order, SalesData, OrderSummary } from '../types';
import { mapDocToOrder } from './orderService';

const ORDERS_COLLECTION = 'Orders';

// Check if user is authenticated
const checkAuth = async (): Promise<boolean> => {
  return new Promise((resolve) => {
    const unsubscribe = auth.onAuthStateChanged((user) => {
      unsubscribe();
      if (user) {
        console.log('User is authenticated:', user.uid);
        resolve(true);
      } else {
        console.log('No authenticated user found');
        resolve(false);
      }
    });
  });
};

// Get order summary statistics
export const getOrderSummary = async (): Promise<OrderSummary> => {
  try {
    // Check if user is authenticated
    const isAuthenticated = await checkAuth();
    if (!isAuthenticated) {
      console.log('User not authenticated');
      return {
        totalOrders: 0,
        newOrders: 0,
        processing: 0,
        outForDelivery: 0,
        delivered: 0,
        cancelled: 0
      };
    }

    console.log('Fetching order summary statistics');
    const ordersRef = collection(db, ORDERS_COLLECTION);
    const ordersSnapshot = await getDocs(ordersRef);

    if (ordersSnapshot.empty) {
      console.log('No orders found');
      return {
        totalOrders: 0,
        newOrders: 0,
        processing: 0,
        outForDelivery: 0,
        delivered: 0,
        cancelled: 0
      };
    }

    // Map documents to Order type
    const orders = ordersSnapshot.docs.map(mapDocToOrder);

    // Get today's date at midnight for filtering
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Filter orders for today only
    const todaysOrders = orders.filter(order => {
      const orderDate = new Date(order.createdAt);
      return orderDate >= today;
    });

    // Calculate order statistics
    const totalOrders = todaysOrders.length; // Today's orders only

    // Count today's orders by status
    const placed = todaysOrders.filter(order => order.status === 'placed').length;
    const processing = todaysOrders.filter(order => order.status === 'processing').length;
    const outForDelivery = todaysOrders.filter(order => order.status === 'out_for_delivery').length;
    const delivered = todaysOrders.filter(order => order.status === 'delivered').length;
    const cancelled = todaysOrders.filter(order => order.status === 'cancelled').length;

    // All new orders are today's orders
    const newOrders = totalOrders;

    console.log(`Order summary: total=${totalOrders}, new=${newOrders}, processing=${processing}, outForDelivery=${outForDelivery}, delivered=${delivered}, cancelled=${cancelled}`);

    return {
      totalOrders,
      newOrders,
      processing,
      outForDelivery,
      delivered,
      cancelled
    };
  } catch (error) {
    console.error('Error getting order summary:', error);
    return {
      totalOrders: 0,
      newOrders: 0,
      processing: 0,
      outForDelivery: 0,
      delivered: 0,
      cancelled: 0
    };
  }
};

// Get recent orders
export const getRecentOrders = async (count: number = 5): Promise<Order[]> => {
  try {
    // Check if user is authenticated
    const isAuthenticated = await checkAuth();
    if (!isAuthenticated) {
      console.log('User not authenticated');
      return [];
    }

    console.log(`Fetching ${count} recent orders`);
    const ordersRef = collection(db, ORDERS_COLLECTION);

    try {
      // First try with the compound query that requires an index
      const q = query(
        ordersRef,
        orderBy('orderedTime', 'desc'),
        limit(count)
      );

      const querySnapshot = await getDocs(q);
      console.log(`Found ${querySnapshot.docs.length} recent orders with compound query`);

      // Map documents to Order type
      const orders = querySnapshot.docs.map(mapDocToOrder);

      // Get today's date at midnight for filtering
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // Filter orders for today only
      const todaysOrders = orders.filter(order => {
        const orderDate = new Date(order.createdAt);
        return orderDate >= today;
      });

      // Sort by createdAt and take the most recent ones
      return todaysOrders
        .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
        .slice(0, count);
    } catch (indexError) {
      console.log('Compound query failed, falling back to simple query:', indexError.message);

      // If the compound query fails (likely due to missing index), fall back to a simple query
      const simpleQ = query(ordersRef);

      const querySnapshot = await getDocs(simpleQ);
      console.log(`Found ${querySnapshot.docs.length} orders with simple query`);

      // Map documents to Order type
      const orders = querySnapshot.docs.map(mapDocToOrder);

      // Get today's date at midnight for filtering
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // Filter orders for today only
      const todaysOrders = orders.filter(order => {
        const orderDate = new Date(order.createdAt);
        return orderDate >= today;
      });

      // Sort by createdAt and take the most recent ones
      return todaysOrders
        .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
        .slice(0, count);
    }
  } catch (error) {
    console.error('Error getting recent orders:', error);
    return [];
  }
};

// Get sales data for the dashboard chart
export const getSalesData = async (period: 'daily' | 'weekly' | 'monthly'): Promise<SalesData> => {
  try {
    // Check if user is authenticated
    const isAuthenticated = await checkAuth();
    if (!isAuthenticated) {
      console.log('User not authenticated');
      return createEmptySalesData(period);
    }

    console.log(`Fetching sales data for ${period} period`);
    const ordersRef = collection(db, ORDERS_COLLECTION);
    const ordersSnapshot = await getDocs(ordersRef);

    if (ordersSnapshot.empty) {
      console.log('No orders found');
      return createEmptySalesData(period);
    }

    // Map documents to Order type
    const orders = ordersSnapshot.docs.map(mapDocToOrder);

    // Calculate sales data based on period
    switch (period) {
      case 'daily':
        return calculateDailySales(orders);
      case 'weekly':
        return calculateWeeklySales(orders);
      case 'monthly':
        return calculateMonthlySales(orders);
      default:
        return createEmptySalesData(period);
    }
  } catch (error) {
    console.error(`Error getting ${period} sales data:`, error);
    return createEmptySalesData(period);
  }
};

// Helper function to create empty sales data
const createEmptySalesData = (period: 'daily' | 'weekly' | 'monthly'): SalesData => {
  switch (period) {
    case 'daily':
      return {
        period: 'daily',
        data: {
          labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
          values: [0, 0, 0, 0, 0, 0, 0],
        },
      };
    case 'weekly':
      return {
        period: 'weekly',
        data: {
          labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
          values: [0, 0, 0, 0],
        },
      };
    case 'monthly':
      return {
        period: 'monthly',
        data: {
          labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
          values: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
        },
      };
    default:
      return {
        period: 'daily',
        data: {
          labels: [],
          values: [],
        },
      };
  }
};

// Calculate daily sales for the current week
const calculateDailySales = (orders: Order[]): SalesData => {
  const daysOfWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  const salesByDay = new Array(7).fill(0);

  // Get the current date
  const now = new Date();

  // Calculate the start of the week (Sunday)
  const startOfWeek = new Date(now);
  startOfWeek.setDate(now.getDate() - now.getDay());
  startOfWeek.setHours(0, 0, 0, 0);

  // Filter orders for the current week and aggregate sales by day
  orders.forEach(order => {
    const orderDate = new Date(order.createdAt);
    if (orderDate >= startOfWeek) {
      const dayOfWeek = orderDate.getDay(); // 0 = Sunday, 1 = Monday, etc.
      salesByDay[dayOfWeek] += order.total;
    }
  });

  // Reorder days to start with Monday
  const mondayFirstLabels = [...daysOfWeek.slice(1), daysOfWeek[0]];
  const mondayFirstValues = [...salesByDay.slice(1), salesByDay[0]];

  return {
    period: 'daily',
    data: {
      labels: mondayFirstLabels,
      values: mondayFirstValues,
    },
  };
};

// Calculate weekly sales for the current month
const calculateWeeklySales = (orders: Order[]): SalesData => {
  const weeksInMonth = 4;
  const salesByWeek = new Array(weeksInMonth).fill(0);

  // Get the current date
  const now = new Date();

  // Calculate the start of the month
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

  // Filter orders for the current month and aggregate sales by week
  orders.forEach(order => {
    const orderDate = new Date(order.createdAt);
    if (orderDate >= startOfMonth) {
      const dayOfMonth = orderDate.getDate();
      const weekOfMonth = Math.floor((dayOfMonth - 1) / 7); // 0-indexed week
      if (weekOfMonth < weeksInMonth) {
        salesByWeek[weekOfMonth] += order.total;
      }
    }
  });

  return {
    period: 'weekly',
    data: {
      labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
      values: salesByWeek,
    },
  };
};

// Calculate monthly sales for the current year
const calculateMonthlySales = (orders: Order[]): SalesData => {
  const monthsOfYear = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  const salesByMonth = new Array(12).fill(0);

  // Get the current year
  const currentYear = new Date().getFullYear();

  // Filter orders for the current year and aggregate sales by month
  orders.forEach(order => {
    const orderDate = new Date(order.createdAt);
    if (orderDate.getFullYear() === currentYear) {
      const monthOfYear = orderDate.getMonth(); // 0 = January, 1 = February, etc.
      salesByMonth[monthOfYear] += order.total;
    }
  });

  return {
    period: 'monthly',
    data: {
      labels: monthsOfYear,
      values: salesByMonth,
    },
  };
};
