/**
 * Recursively cleans an object by removing undefined values and specific fields
 * @param obj The object to clean
 * @param fieldsToRemove Array of field names to remove from all objects
 * @returns A new object with undefined values and specified fields removed
 */
export const cleanObject = (obj: any, fieldsToRemove: string[] = []): any => {
  // If it's not an object or is null, return it as is
  if (typeof obj !== 'object' || obj === null) {
    return obj;
  }
  
  // If it's an array, clean each element
  if (Array.isArray(obj)) {
    return obj.map(item => cleanObject(item, fieldsToRemove));
  }
  
  // For objects, recursively clean each property
  const cleaned: any = {};
  for (const [key, value] of Object.entries(obj)) {
    // Skip fields that should be removed
    if (fieldsToRemove.includes(key)) continue;
    
    // Skip undefined values
    if (value === undefined) continue;
    
    // Recursively clean nested objects
    cleaned[key] = cleanObject(value, fieldsToRemove);
  }
  
  return cleaned;
};