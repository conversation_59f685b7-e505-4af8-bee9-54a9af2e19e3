import { useEffect, useRef } from 'react';
import { Chart, registerables } from 'chart.js';
import { SalesData } from '../../types';

Chart.register(...registerables);

interface SalesChartProps {
  data: SalesData;
}

export const SalesChart = ({ data }: SalesChartProps) => {
  const chartRef = useRef<HTMLCanvasElement>(null);
  const chartInstanceRef = useRef<Chart | null>(null);

  useEffect(() => {
    if (!chartRef.current) return;

    // Destroy previous chart instance if exists
    if (chartInstanceRef.current) {
      chartInstanceRef.current.destroy();
    }

    const ctx = chartRef.current.getContext('2d');
    if (!ctx) return;

    // Create new chart instance
    chartInstanceRef.current = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: data.data.labels,
        datasets: [
          {
            label: 'Sales',
            data: data.data.values,
            backgroundColor: 'rgba(125, 71, 162, 0.7)',
            borderColor: 'rgba(125, 71, 162, 1)',
            borderWidth: 1,
            borderRadius: 5,
            barThickness: 'flex',
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        interaction: {
          mode: 'index',
          intersect: false,
        },
        plugins: {
          legend: {
            display: false,
          },
          tooltip: {
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            titleColor: '#1f2937',
            bodyColor: '#4b5563',
            bodyFont: {
              family: "'Montserrat', sans-serif",
            },
            borderColor: 'rgba(125, 71, 162, 0.2)',
            borderWidth: 1,
            padding: 10,
            boxPadding: 5,
            usePointStyle: true,
            callbacks: {
              label: function(context) {
                return `₹${context.parsed.y.toLocaleString()}`;
              }
            }
          },
        },
        scales: {
          x: {
            grid: {
              display: false,
            },
            ticks: {
              font: {
                family: "'Montserrat', sans-serif",
              },
            },
          },
          y: {
            beginAtZero: true,
            grid: {
              color: 'rgba(0, 0, 0, 0.05)',
            },
            ticks: {
              font: {
                family: "'Montserrat', sans-serif",
              },
              callback: function(value) {
                return `₹${value.toLocaleString()}`;
              }
            },
          },
        },
      },
    });

    return () => {
      if (chartInstanceRef.current) {
        chartInstanceRef.current.destroy();
      }
    };
  }, [data]);

  return <canvas ref={chartRef} />;
};