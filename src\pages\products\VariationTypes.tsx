import React, { useState, useEffect } from 'react';
import { Card } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { Badge } from '../../components/ui/Badge';
import { Plus, Edit2, Trash2, Type, Tag, X } from 'lucide-react';
import { VariationType } from '../../types';
import {
  getAllVariationTypes,
  addVariationType,
  updateVariationType,
  deleteVariationType
} from '../../services/variationTypeService';

export function VariationTypes() {
  const [variationTypes, setVariationTypes] = useState<VariationType[]>([]);
  const [showNewForm, setShowNewForm] = useState(false);
  const [editingType, setEditingType] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [newVariationType, setNewVariationType] = useState({
    name: '',
    description: '',
    options: [] as string[]
  });

  const [editVariationTypeData, setEditVariationTypeData] = useState({
    name: '',
    description: '',
    options: [] as string[]
  });

  const [newOption, setNewOption] = useState('');

  // Fetch variation types on component mount
  useEffect(() => {
    const fetchVariationTypes = async () => {
      try {
        setLoading(true);
        const fetchedTypes = await getAllVariationTypes();
        setVariationTypes(fetchedTypes);
      } catch (err) {
        console.error('Error fetching variation types:', err);
        setError('Failed to load variation types. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchVariationTypes();
  }, []);

  // Add a new variation type
  const handleAddVariationType = async () => {
    if (newVariationType.name && newVariationType.options.length > 0) {
      try {
        const docRef = await addVariationType(newVariationType);

        if (docRef) {
          // Add to local state
          setVariationTypes([...variationTypes, {
            id: docRef,
            ...newVariationType,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }]);

          // Reset form
          setNewVariationType({ name: '', description: '', options: [] });
          setShowNewForm(false);
        }
      } catch (err) {
        console.error('Error adding variation type:', err);
        setError('Failed to add variation type. Please try again.');
      }
    }
  };

  // Start editing a variation type
  const startEditingVariationType = (typeId: string) => {
    const typeToEdit = variationTypes.find(type => type.id === typeId);
    if (typeToEdit) {
      setEditVariationTypeData({
        name: typeToEdit.name,
        description: typeToEdit.description || '',
        options: [...typeToEdit.options]
      });
      setEditingType(typeId);
    }
  };

  // Save edited variation type
  const handleSaveEdit = async () => {
    if (editingType && editVariationTypeData.name && editVariationTypeData.options.length > 0) {
      try {
        const success = await updateVariationType(editingType, editVariationTypeData);

        if (success) {
          // Update local state
          const updatedTypes = variationTypes.map(type =>
            type.id === editingType
              ? { ...type, ...editVariationTypeData, updatedAt: new Date().toISOString() }
              : type
          );
          setVariationTypes(updatedTypes);

          // Reset editing state
          setEditingType(null);
          setEditVariationTypeData({ name: '', description: '', options: [] });
        }
      } catch (err) {
        console.error('Error updating variation type:', err);
        setError('Failed to update variation type. Please try again.');
      }
    }
  };

  // Delete a variation type
  const handleDeleteVariationType = async (typeId: string) => {
    if (window.confirm('Are you sure you want to delete this variation type?')) {
      try {
        const success = await deleteVariationType(typeId);

        if (success) {
          // Remove from local state
          setVariationTypes(variationTypes.filter(type => type.id !== typeId));
        }
      } catch (err) {
        console.error('Error deleting variation type:', err);
        setError('Failed to delete variation type. Please try again.');
      }
    }
  };

  // Add option to new variation type
  const addOptionToNew = () => {
    if (newOption.trim() && !newVariationType.options.includes(newOption.trim())) {
      setNewVariationType({
        ...newVariationType,
        options: [...newVariationType.options, newOption.trim()]
      });
      setNewOption('');
    }
  };

  // Remove option from new variation type
  const removeOptionFromNew = (optionToRemove: string) => {
    setNewVariationType({
      ...newVariationType,
      options: newVariationType.options.filter(option => option !== optionToRemove)
    });
  };

  // Add option to edit variation type
  const addOptionToEdit = () => {
    if (newOption.trim() && !editVariationTypeData.options.includes(newOption.trim())) {
      setEditVariationTypeData({
        ...editVariationTypeData,
        options: [...editVariationTypeData.options, newOption.trim()]
      });
      setNewOption('');
    }
  };

  // Remove option from edit variation type
  const removeOptionFromEdit = (optionToRemove: string) => {
    setEditVariationTypeData({
      ...editVariationTypeData,
      options: editVariationTypeData.options.filter(option => option !== optionToRemove)
    });
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="text-center">Loading variation types...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="text-center text-red-600">{error}</div>
      </div>
    );
  }

  return (
    <div className="p-6 animate-fade-in">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Variation Types</h1>
          <p className="text-gray-600">Manage product variation types like Size, Color, Weight, etc.</p>
        </div>
        <div className="flex gap-2">
          {variationTypes.length === 0 && (
            <Button
              variant="outline"
              onClick={async () => {
                try {
                  // Create sample variation types
                  const sampleTypes = [
                    { name: 'Size', description: 'Product size variations', options: ['Small', 'Medium', 'Large', 'Extra Large'] },
                    { name: 'Color', description: 'Product color variations', options: ['Red', 'Blue', 'Green', 'Yellow', 'Black', 'White'] },
                    { name: 'Weight', description: 'Product weight variations', options: ['250g', '500g', '1kg', '2kg', '5kg'] }
                  ];

                  for (const type of sampleTypes) {
                    await addVariationType(type);
                  }

                  // Refresh the list
                  const fetchedTypes = await getAllVariationTypes();
                  setVariationTypes(fetchedTypes);
                } catch (err) {
                  console.error('Error creating sample types:', err);
                }
              }}
              className="shadow-md hover:shadow-lg transition-shadow"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Sample Types
            </Button>
          )}
          <Button
            variant="primary"
            onClick={() => setShowNewForm(true)}
            className="shadow-md hover:shadow-lg transition-shadow"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Variation Type
          </Button>
        </div>
      </div>

      <div className="grid gap-6">
        {/* New Variation Type Form */}
        {showNewForm && (
          <Card>
            <div className="p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Add New Variation Type</h2>
              <div className="space-y-4">
                <Input
                  label="Name"
                  type="text"
                  value={newVariationType.name}
                  onChange={(e) => setNewVariationType({ ...newVariationType, name: e.target.value })}
                  required
                  startIcon={<Type className="h-4 w-4 text-gray-400" />}
                  placeholder="e.g., Size, Color, Weight"
                />
                <Input
                  label="Description (Optional)"
                  type="text"
                  value={newVariationType.description}
                  onChange={(e) => setNewVariationType({ ...newVariationType, description: e.target.value })}
                  placeholder="Brief description of this variation type"
                />

                {/* Options Section */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Options</label>
                  <div className="flex gap-2 mb-3">
                    <Input
                      type="text"
                      value={newOption}
                      onChange={(e) => setNewOption(e.target.value)}
                      placeholder="Add an option (e.g., Small, Medium, Large)"
                      onKeyPress={(e) => e.key === 'Enter' && addOptionToNew()}
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={addOptionToNew}
                      disabled={!newOption.trim()}
                    >
                      Add
                    </Button>
                  </div>

                  {/* Display added options */}
                  <div className="flex flex-wrap gap-2">
                    {newVariationType.options.map((option, index) => (
                      <Badge key={index} variant="secondary" className="flex items-center gap-1">
                        {option}
                        <button
                          onClick={() => removeOptionFromNew(option)}
                          className="ml-1 hover:text-red-600"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                </div>

                <div className="flex gap-3">
                  <Button
                    variant="primary"
                    onClick={handleAddVariationType}
                    disabled={!newVariationType.name || newVariationType.options.length === 0}
                  >
                    Add Variation Type
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setShowNewForm(false);
                      setNewVariationType({ name: '', description: '', options: [] });
                      setNewOption('');
                    }}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        )}

        {/* Variation Types List */}
        <div className="grid gap-4">
          {variationTypes.map((variationType) => (
            <Card key={variationType.id}>
              <div className="p-6">
                {editingType === variationType.id ? (
                  // Edit form
                  <div className="space-y-4">
                    <Input
                      label="Name"
                      type="text"
                      value={editVariationTypeData.name}
                      onChange={(e) => setEditVariationTypeData({ ...editVariationTypeData, name: e.target.value })}
                      required
                    />
                    <Input
                      label="Description (Optional)"
                      type="text"
                      value={editVariationTypeData.description}
                      onChange={(e) => setEditVariationTypeData({ ...editVariationTypeData, description: e.target.value })}
                    />

                    {/* Options Section for Edit */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Options</label>
                      <div className="flex gap-2 mb-3">
                        <Input
                          type="text"
                          value={newOption}
                          onChange={(e) => setNewOption(e.target.value)}
                          placeholder="Add an option"
                          onKeyPress={(e) => e.key === 'Enter' && addOptionToEdit()}
                        />
                        <Button
                          type="button"
                          variant="outline"
                          onClick={addOptionToEdit}
                          disabled={!newOption.trim()}
                        >
                          Add
                        </Button>
                      </div>

                      <div className="flex flex-wrap gap-2">
                        {editVariationTypeData.options.map((option, index) => (
                          <Badge key={index} variant="secondary" className="flex items-center gap-1">
                            {option}
                            <button
                              onClick={() => removeOptionFromEdit(option)}
                              className="ml-1 hover:text-red-600"
                            >
                              <X className="h-3 w-3" />
                            </button>
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div className="flex gap-3">
                      <Button variant="primary" onClick={handleSaveEdit}>
                        Save Changes
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => {
                          setEditingType(null);
                          setEditVariationTypeData({ name: '', description: '', options: [] });
                          setNewOption('');
                        }}
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                ) : (
                  // Display mode
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <Tag className="h-5 w-5 text-primary-600" />
                        <h3 className="text-lg font-semibold text-gray-900">{variationType.name}</h3>
                      </div>
                      {variationType.description && (
                        <p className="text-gray-600 mb-3">{variationType.description}</p>
                      )}
                      <div className="flex flex-wrap gap-2">
                        {variationType.options.map((option, index) => (
                          <Badge key={index} variant="outline">
                            {option}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => startEditingVariationType(variationType.id)}
                      >
                        <Edit2 className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteVariationType(variationType.id)}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </Card>
          ))}
        </div>

        {variationTypes.length === 0 && (
          <Card>
            <div className="p-12 text-center">
              <Tag className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No variation types yet</h3>
              <p className="text-gray-600 mb-4">
                Create variation types like Size, Color, Weight to organize your product variants.
              </p>
              <Button variant="primary" onClick={() => setShowNewForm(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Add Your First Variation Type
              </Button>
            </div>
          </Card>
        )}
      </div>
    </div>
  );
}
