import { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import {
  Search,
  Filter,
  ChevronDown,
  RefreshCw,
  AlertCircle,
  RotateCcw,
  Calendar,
  Package,
  User,
  DollarSign,
  ShoppingBag,
  Plus
} from 'lucide-react';
import { Card } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { ReturnStatusBadge } from '../../components/ui/StatusBadge';
import { Return, ReturnStatus, getAllReturns, updateReturnStatus } from '../../services/returnService';
import { CreateReturnModal } from '../../components/returns/CreateReturnModal';

export function ReturnsList() {
  const [returns, setReturns] = useState<Return[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<ReturnStatus | 'all'>('all');
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [showCreateReturnModal, setShowCreateReturnModal] = useState(false);

  // Fetch returns on component mount
  useEffect(() => {
    fetchReturns();
  }, []);

  // Fetch returns from the API
  const fetchReturns = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await getAllReturns();
      console.log('Fetched returns:', data);
      setReturns(data);
    } catch (err) {
      console.error('Error fetching returns:', err);
      setError('Failed to load returns. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle refresh button click
  const handleRefresh = () => {
    console.log('Refreshing returns...');
    fetchReturns();
  };

  // Handle status update
  const handleStatusUpdate = async (returnId: string, newStatus: ReturnStatus) => {
    try {
      const success = await updateReturnStatus(returnId, newStatus);
      if (success) {
        // Refresh the returns list
        fetchReturns();
      } else {
        setError('Failed to update return status. Please try again.');
      }
    } catch (err) {
      console.error('Error updating return status:', err);
      setError('Failed to update return status. Please try again.');
    }
  };

  // Filter returns based on search query and status filter
  const filteredReturns = returns.filter(returnItem => {
    // Filter by search query
    const matchesSearch =
      returnItem.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      returnItem.productName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      returnItem.orderId.toString().includes(searchQuery);

    // Filter by status
    const matchesStatus = statusFilter === 'all' || returnItem.returnStatus === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-display font-bold text-gray-900">Returns</h1>
          <p className="text-gray-500">Manage product returns and refunds</p>
        </div>
        <div className="flex space-x-2 w-full md:w-auto">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button
            variant="primary"
            size="sm"
            onClick={() => setShowCreateReturnModal(true)}
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Return
          </Button>
        </div>
      </div>

      {/* Create Return Modal */}
      <CreateReturnModal
        isOpen={showCreateReturnModal}
        onClose={() => setShowCreateReturnModal(false)}
        onSuccess={fetchReturns}
      />

      {/* Search and Filters */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search by customer, product, or order ID..."
            className="pl-10 pr-4 py-2 w-full border-2 border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="relative">
          <Button
            variant="outline"
            onClick={() => setIsFilterOpen(!isFilterOpen)}
            className="w-full md:w-auto justify-between"
          >
            <div className="flex items-center">
              <Filter className="h-4 w-4 mr-2" />
              <span>Filter: {statusFilter === 'all' ? 'All Status' : statusFilter}</span>
            </div>
            <ChevronDown className="h-4 w-4 ml-2" />
          </Button>
          {isFilterOpen && (
            <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200">
              <div className="py-1">
                <button
                  className={`block px-4 py-2 text-sm w-full text-left ${statusFilter === 'all' ? 'bg-primary-50 text-primary-700' : 'text-gray-700 hover:bg-gray-100'}`}
                  onClick={() => {
                    setStatusFilter('all');
                    setIsFilterOpen(false);
                  }}
                >
                  All Status
                </button>
                <button
                  className={`block px-4 py-2 text-sm w-full text-left ${statusFilter === 'pending' ? 'bg-primary-50 text-primary-700' : 'text-gray-700 hover:bg-gray-100'}`}
                  onClick={() => {
                    setStatusFilter('pending');
                    setIsFilterOpen(false);
                  }}
                >
                  Pending
                </button>
                <button
                  className={`block px-4 py-2 text-sm w-full text-left ${statusFilter === 'approved' ? 'bg-primary-50 text-primary-700' : 'text-gray-700 hover:bg-gray-100'}`}
                  onClick={() => {
                    setStatusFilter('approved');
                    setIsFilterOpen(false);
                  }}
                >
                  Approved
                </button>
                <button
                  className={`block px-4 py-2 text-sm w-full text-left ${statusFilter === 'rejected' ? 'bg-primary-50 text-primary-700' : 'text-gray-700 hover:bg-gray-100'}`}
                  onClick={() => {
                    setStatusFilter('rejected');
                    setIsFilterOpen(false);
                  }}
                >
                  Rejected
                </button>
                <button
                  className={`block px-4 py-2 text-sm w-full text-left ${statusFilter === 'completed' ? 'bg-primary-50 text-primary-700' : 'text-gray-700 hover:bg-gray-100'}`}
                  onClick={() => {
                    setStatusFilter('completed');
                    setIsFilterOpen(false);
                  }}
                >
                  Completed
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-4">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-red-400 mr-2" />
            <p className="text-red-700">{error}</p>
          </div>
          <Button
            variant="outline"
            size="sm"
            className="mt-2"
            onClick={handleRefresh}
          >
            Retry
          </Button>
        </div>
      )}

      {/* Returns List */}
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="flex flex-col items-center">
            <RefreshCw className="w-12 h-12 text-primary-500 animate-spin mb-4" />
            <p className="text-gray-600">Loading returns...</p>
          </div>
        </div>
      ) : filteredReturns.length === 0 ? (
        <div className="flex justify-center items-center h-64">
          <div className="flex flex-col items-center">
            <RotateCcw className="w-12 h-12 text-gray-400 mb-4" />
            <p className="text-gray-600">No returns found</p>
            <p className="text-gray-500 text-sm mt-2">Try adjusting your search or filters</p>
          </div>
        </div>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {filteredReturns.map(returnItem => (
            <Card key={returnItem.id} className="hover:shadow-md transition-shadow">
              <div className="p-4">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{returnItem.productName}</h3>
                    <div className="mt-1">
                      <ReturnStatusBadge status={returnItem.returnStatus} />
                    </div>
                  </div>
                  <div className="flex-shrink-0 h-16 w-16 rounded-md overflow-hidden bg-gray-100">
                    {returnItem.productImage ? (
                      <img
                        src={returnItem.productImage}
                        alt={returnItem.productName}
                        className="h-full w-full object-cover"
                      />
                    ) : (
                      <div className="flex items-center justify-center h-full w-full bg-gray-200">
                        <Package className="h-8 w-8 text-gray-400" />
                      </div>
                    )}
                  </div>
                </div>

                <div className="space-y-2 text-sm">
                  <div className="flex items-center text-gray-600">
                    <User className="h-4 w-4 mr-2" />
                    <span>{returnItem.customerName}</span>
                  </div>
                  <div className="flex items-center text-gray-600">
                    <ShoppingBag className="h-4 w-4 mr-2" />
                    <span>Order #{returnItem.orderId}</span>
                  </div>
                  <div className="flex items-center text-gray-600">
                    <DollarSign className="h-4 w-4 mr-2" />
                    <span>₹{returnItem.price.toFixed(2)} × {returnItem.quantity} {returnItem.unit}</span>
                  </div>
                  <div className="flex items-center text-gray-600">
                    <Calendar className="h-4 w-4 mr-2" />
                    <span>{formatDate(returnItem.returnInitiatedTime)}</span>
                  </div>
                </div>

                {/* Action buttons based on status */}
                <div className="mt-4 flex gap-2">
                  {returnItem.returnStatus === 'pending' && (
                    <>
                      <Button
                        variant="success"
                        size="sm"
                        className="flex-1"
                        onClick={() => handleStatusUpdate(returnItem.id, 'approved')}
                      >
                        Approve
                      </Button>
                      <Button
                        variant="danger"
                        size="sm"
                        className="flex-1"
                        onClick={() => handleStatusUpdate(returnItem.id, 'rejected')}
                      >
                        Reject
                      </Button>
                    </>
                  )}
                  {returnItem.returnStatus === 'approved' && (
                    <Button
                      variant="primary"
                      size="sm"
                      className="w-full"
                      onClick={() => handleStatusUpdate(returnItem.id, 'completed')}
                    >
                      Mark as Completed
                    </Button>
                  )}
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
