import { auth } from '../firebase/config';

// Check if user is authenticated
export const checkAuth = async (): Promise<boolean> => {
  return new Promise((resolve) => {
    const unsubscribe = auth.onAuthStateChanged((user) => {
      unsubscribe();
      if (user) {
        console.log('User is authenticated:', user.uid);
        resolve(true);
      } else {
        console.log('No authenticated user found');
        resolve(false);
      }
    });
  });
};

// Get current user
export const getCurrentUser = () => {
  return auth.currentUser;
};


