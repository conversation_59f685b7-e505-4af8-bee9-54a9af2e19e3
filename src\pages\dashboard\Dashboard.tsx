import { Card } from '../../components/ui/Card';
import {
  BarChart,
  ShoppingCart,
  Package,
  Truck,
  CheckCircle,
  AlertCircle,
  TrendingUp,
  TrendingDown,
  RefreshCw
} from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { SalesChart } from './SalesChart';
import { RecentOrders } from './RecentOrders';
import { useState, useEffect } from 'react';
import { SalesData, OrderSummary } from '../../types';
import { getOrderSummary, getSalesData } from '../../services/dashboardService';

export const Dashboard = () => {
  const [activePeriod, setActivePeriod] = useState<'daily' | 'weekly' | 'monthly'>('weekly');
  const [orderSummary, setOrderSummary] = useState<OrderSummary>({
    totalOrders: 0,
    newOrders: 0,
    processing: 0,
    outForDelivery: 0,
    delivered: 0,
    cancelled: 0
  });
  const [salesData, setSalesData] = useState<Record<string, SalesData>>({
    daily: {
      period: 'daily',
      data: {
        labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        values: [0, 0, 0, 0, 0, 0, 0],
      },
    },
    weekly: {
      period: 'weekly',
      data: {
        labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
        values: [0, 0, 0, 0],
      },
    },
    monthly: {
      period: 'monthly',
      data: {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
        values: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      },
    },
  });
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch order summary
        const summary = await getOrderSummary();
        setOrderSummary(summary);

        // Fetch sales data for all periods
        const dailyData = await getSalesData('daily');
        const weeklyData = await getSalesData('weekly');
        const monthlyData = await getSalesData('monthly');

        setSalesData({
          daily: dailyData,
          weekly: weeklyData,
          monthly: monthlyData
        });
      } catch (err) {
        console.error('Error fetching dashboard data:', err);
        setError('Failed to load dashboard data. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  // Handle refresh
  const handleRefresh = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch order summary
      const summary = await getOrderSummary();
      setOrderSummary(summary);

      // Fetch sales data for the active period
      const periodData = await getSalesData(activePeriod);
      setSalesData(prevData => ({
        ...prevData,
        [activePeriod]: periodData
      }));
    } catch (err) {
      console.error('Error refreshing dashboard data:', err);
      setError('Failed to refresh dashboard data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex flex-col md:flex-row items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-display font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-500">Welcome back to your admin dashboard</p>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="primary"
            size="sm"
            onClick={handleRefresh}
            disabled={loading}
          >
            {loading ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Refreshing...
              </>
            ) : (
              <>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh Data
              </>
            )}
          </Button>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5">
        <StatCard
          title="Today's Orders"
          value={orderSummary.totalOrders}
          trend={{
            direction: 'up',
            value: 100,
            label: 'today only',
          }}
          icon={<ShoppingCart className="h-5 w-5 text-primary-500" />}
          loading={loading}
        />

        <StatCard
          title="Processing Today"
          value={orderSummary.processing}
          trend={{
            direction: 'up',
            value: Math.round((orderSummary.processing / (orderSummary.totalOrders || 1)) * 100),
            label: 'of today',
          }}
          icon={<Package className="h-5 w-5 text-primary-500" />}
          loading={loading}
        />

        <StatCard
          title="Out for Delivery Today"
          value={orderSummary.outForDelivery}
          trend={{
            direction: 'up',
            value: Math.round((orderSummary.outForDelivery / (orderSummary.totalOrders || 1)) * 100),
            label: 'of today',
          }}
          icon={<Truck className="h-5 w-5 text-primary-500" />}
          loading={loading}
        />

        <StatCard
          title="Delivered Today"
          value={orderSummary.delivered}
          trend={{
            direction: 'up',
            value: Math.round((orderSummary.delivered / (orderSummary.totalOrders || 1)) * 100),
            label: 'of today',
          }}
          icon={<CheckCircle className="h-5 w-5 text-primary-500" />}
          loading={loading}
        />
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 gap-6">
        <Card>
          <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Sales Overview</h3>
            <div className="mt-2 md:mt-0 flex space-x-2">
              <Button
                variant={activePeriod === 'daily' ? 'primary' : 'outline'}
                size="sm"
                onClick={() => setActivePeriod('daily')}
                disabled={loading}
              >
                Daily
              </Button>
              <Button
                variant={activePeriod === 'weekly' ? 'primary' : 'outline'}
                size="sm"
                onClick={() => setActivePeriod('weekly')}
                disabled={loading}
              >
                Weekly
              </Button>
              <Button
                variant={activePeriod === 'monthly' ? 'primary' : 'outline'}
                size="sm"
                onClick={() => setActivePeriod('monthly')}
                disabled={loading}
              >
                Monthly
              </Button>
            </div>
          </div>
          <div className="h-72">
            {loading ? (
              <div className="flex items-center justify-center h-full">
                <RefreshCw className="h-8 w-8 text-primary-500 animate-spin" />
              </div>
            ) : (
              <SalesChart data={salesData[activePeriod]} />
            )}
          </div>
        </Card>
      </div>

      {/* Today's Orders */}
      <div>
        <Card title="Today's Orders">
          <RecentOrders />
        </Card>
      </div>
    </div>
  );
};

// Types for the StatCard component
interface StatCardProps {
  title: string;
  value: number;
  trend: {
    direction: 'up' | 'down';
    value: number;
    label: string;
  };
  icon: React.ReactNode;
  loading?: boolean;
}

const StatCard = ({ title, value, trend, icon, loading = false }: StatCardProps) => {
  return (
    <Card className="transition-all duration-300 hover:shadow-md hover:border-primary-100">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-500">{title}</p>
          {loading ? (
            <div className="h-8 w-16 bg-gray-200 animate-pulse rounded mt-1"></div>
          ) : (
            <p className="text-2xl font-bold text-gray-900 mt-1">{value}</p>
          )}
        </div>
        <div className="bg-primary-50 p-3 rounded-full">{icon}</div>
      </div>
      <div className="mt-3">
        {loading ? (
          <div className="h-4 w-24 bg-gray-200 animate-pulse rounded"></div>
        ) : (
          <div className="flex items-center">
            {trend.direction === 'up' ? (
              <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
            ) : (
              <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
            )}
            <span className={`text-sm ${
              trend.direction === 'up' ? 'text-green-500' : 'text-red-500'
            }`}>
              {trend.value}%
            </span>
            <span className="text-xs text-gray-500 ml-1">{trend.label}</span>
          </div>
        )}
      </div>
    </Card>
  );
};
