import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  setDoc,
  query,
  where,
  orderBy,
  serverTimestamp,
  Timestamp,
  limit,
  startAfter,
  QueryDocumentSnapshot,
  getCountFromServer
} from 'firebase/firestore';
import { db } from '../firebase/config';
import { Coupon, Redemption } from '../types';
import { checkAuth } from './authService';

// Collection names
const COUPONS_COLLECTION = 'Coupons';
const REDEMPTIONS_SUBCOLLECTION = 'Redemptions';

// Helper function to convert Firestore timestamp to string
const timestampToString = (timestamp: any): string => {
  if (!timestamp) return '';
  if (timestamp.toDate) {
    return timestamp.toDate().toISOString();
  }
  return timestamp;
};

// Helper function to map Firestore document to Coupon type
const mapDocToCoupon = (doc: any): Coupon => {
  const data = doc.data();
  return {
    id: doc.id,
    code: data.code || '',
    type: data.type || 'FLAT',
    flatAmount: data.flatAmount || undefined,
    percent: data.percent || undefined,
    maxDiscount: data.maxDiscount || null,
    minCartTotal: data.minCartTotal || null,
    startAt: data.startAt ? timestampToString(data.startAt) : null,
    expiresAt: timestampToString(data.expiresAt),
    usageLimitGlobal: data.usageLimitGlobal || null,
    usageLimitPerUser: data.usageLimitPerUser || null,
    usedCount: data.usedCount || 0,
    isActive: data.isActive !== undefined ? data.isActive : true,
    description: data.description || null,
    createdAt: timestampToString(data.createdAt),
    updatedAt: timestampToString(data.updatedAt)
  };
};

// Helper function to map Coupon type to Firestore document
const mapCouponToDoc = (coupon: Omit<Coupon, 'id' | 'createdAt' | 'updatedAt'>) => {
  const doc: any = {
    code: coupon.code,
    type: coupon.type,
    usedCount: coupon.usedCount || 0,
    isActive: coupon.isActive !== undefined ? coupon.isActive : true
  };

  // Add conditional fields
  if (coupon.type === 'FLAT' && coupon.flatAmount !== undefined) {
    doc.flatAmount = coupon.flatAmount;
  }
  if (coupon.type === 'PERCENT' && coupon.percent !== undefined) {
    doc.percent = coupon.percent;
  }
  if (coupon.maxDiscount !== undefined && coupon.maxDiscount !== null) {
    doc.maxDiscount = coupon.maxDiscount;
  }
  if (coupon.minCartTotal !== undefined && coupon.minCartTotal !== null) {
    doc.minCartTotal = coupon.minCartTotal;
  }
  if (coupon.startAt) {
    doc.startAt = new Date(coupon.startAt);
  }
  if (coupon.expiresAt) {
    doc.expiresAt = new Date(coupon.expiresAt);
  }
  if (coupon.usageLimitGlobal !== undefined && coupon.usageLimitGlobal !== null) {
    doc.usageLimitGlobal = coupon.usageLimitGlobal;
  }
  if (coupon.usageLimitPerUser !== undefined && coupon.usageLimitPerUser !== null) {
    doc.usageLimitPerUser = coupon.usageLimitPerUser;
  }
  if (coupon.description) {
    doc.description = coupon.description;
  }

  return doc;
};

// Generate a random coupon code
export const generateCouponCode = (length: number = 8): string => {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return result;
};

// Check if coupon code already exists
export const checkCouponCodeExists = async (code: string): Promise<boolean> => {
  try {
    const docRef = doc(db, COUPONS_COLLECTION, code);
    const docSnap = await getDoc(docRef);
    return docSnap.exists();
  } catch (error) {
    console.error('Error checking coupon code:', error);
    return false;
  }
};

// Get all coupons with pagination
export const getCoupons = async (
  pageSize: number = 30,
  lastDoc?: QueryDocumentSnapshot
): Promise<{ coupons: Coupon[]; lastDoc: QueryDocumentSnapshot | null; hasMore: boolean }> => {
  try {
    await checkAuth();

    let q = query(
      collection(db, COUPONS_COLLECTION),
      orderBy('createdAt', 'desc'),
      limit(pageSize + 1)
    );

    if (lastDoc) {
      q = query(
        collection(db, COUPONS_COLLECTION),
        orderBy('createdAt', 'desc'),
        startAfter(lastDoc),
        limit(pageSize + 1)
      );
    }

    const querySnapshot = await getDocs(q);
    const docs = querySnapshot.docs;
    const hasMore = docs.length > pageSize;

    if (hasMore) {
      docs.pop(); // Remove the extra document
    }

    const coupons = docs.map(mapDocToCoupon);
    const newLastDoc = docs.length > 0 ? docs[docs.length - 1] : null;

    return { coupons, lastDoc: newLastDoc, hasMore };
  } catch (error) {
    console.error('Error getting coupons:', error);
    return { coupons: [], lastDoc: null, hasMore: false };
  }
};

// Get coupon by ID
export const getCouponById = async (id: string): Promise<Coupon | null> => {
  try {
    await checkAuth();
    const docRef = doc(db, COUPONS_COLLECTION, id);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      return mapDocToCoupon(docSnap);
    }
    return null;
  } catch (error) {
    console.error('Error getting coupon:', error);
    return null;
  }
};

// Get coupon by code
export const getCouponByCode = async (code: string): Promise<Coupon | null> => {
  try {
    const docRef = doc(db, COUPONS_COLLECTION, code);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      return mapDocToCoupon(docSnap);
    }
    return null;
  } catch (error) {
    console.error('Error getting coupon by code:', error);
    return null;
  }
};

// Add a new coupon
export const addCoupon = async (
  coupon: Omit<Coupon, 'id' | 'createdAt' | 'updatedAt'>
): Promise<string | null> => {
  try {
    await checkAuth();

    // Check if code already exists
    const codeExists = await checkCouponCodeExists(coupon.code);
    if (codeExists) {
      throw new Error('Coupon code already exists');
    }

    const docData = {
      ...mapCouponToDoc(coupon),
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    };

    // Use the coupon code as the document ID
    const docRef = doc(db, COUPONS_COLLECTION, coupon.code);
    await setDoc(docRef, docData);

    return coupon.code;
  } catch (error) {
    console.error('Error adding coupon:', error);
    return null;
  }
};

// Update an existing coupon
export const updateCoupon = async (
  id: string,
  updates: Partial<Omit<Coupon, 'id' | 'createdAt' | 'updatedAt'>>
): Promise<boolean> => {
  try {
    await checkAuth();

    const docRef = doc(db, COUPONS_COLLECTION, id);
    const updateData = {
      ...mapCouponToDoc(updates as any),
      updatedAt: serverTimestamp()
    };

    await updateDoc(docRef, updateData);
    return true;
  } catch (error) {
    console.error('Error updating coupon:', error);
    return false;
  }
};

// Delete a coupon
export const deleteCoupon = async (id: string): Promise<boolean> => {
  try {
    await checkAuth();
    const docRef = doc(db, COUPONS_COLLECTION, id);
    await deleteDoc(docRef);
    return true;
  } catch (error) {
    console.error('Error deleting coupon:', error);
    return false;
  }
};

// Validate coupon for use
export const validateCoupon = async (
  code: string,
  userId: string,
  cartTotal: number
): Promise<{ valid: boolean; coupon?: Coupon; error?: string }> => {
  try {
    const coupon = await getCouponByCode(code);

    if (!coupon) {
      return { valid: false, error: 'Coupon not found' };
    }

    if (!coupon.isActive) {
      return { valid: false, error: 'Coupon is not active' };
    }

    const now = new Date();

    // Check start date
    if (coupon.startAt && new Date(coupon.startAt) > now) {
      return { valid: false, error: 'Coupon is not yet active' };
    }

    // Check expiry date
    if (new Date(coupon.expiresAt) < now) {
      return { valid: false, error: 'Coupon has expired' };
    }

    // Check minimum cart total
    if (coupon.minCartTotal && cartTotal < coupon.minCartTotal) {
      return { valid: false, error: `Minimum order amount is ₹${coupon.minCartTotal}` };
    }

    // Check global usage limit
    if (coupon.usageLimitGlobal && coupon.usedCount >= coupon.usageLimitGlobal) {
      return { valid: false, error: 'Coupon usage limit exceeded' };
    }

    // Check per-user usage limit
    if (coupon.usageLimitPerUser) {
      const userUsageCount = await getUserCouponUsageCount(code, userId);
      if (userUsageCount >= coupon.usageLimitPerUser) {
        return { valid: false, error: 'You have already used this coupon the maximum number of times' };
      }
    }

    return { valid: true, coupon };
  } catch (error) {
    console.error('Error validating coupon:', error);
    return { valid: false, error: 'Error validating coupon' };
  }
};

// Get user's usage count for a specific coupon
export const getUserCouponUsageCount = async (couponCode: string, userId: string): Promise<number> => {
  try {
    const q = query(
      collection(db, COUPONS_COLLECTION, couponCode, REDEMPTIONS_SUBCOLLECTION),
      where('uid', '==', userId)
    );

    const countSnapshot = await getCountFromServer(q);
    return countSnapshot.data().count;
  } catch (error) {
    console.error('Error getting user coupon usage count:', error);
    return 0;
  }
};

// Calculate discount amount
export const calculateDiscount = (coupon: Coupon, cartTotal: number): number => {
  if (coupon.type === 'FLAT') {
    return Math.min(coupon.flatAmount || 0, cartTotal);
  } else if (coupon.type === 'PERCENT') {
    const discountAmount = (cartTotal * (coupon.percent || 0)) / 100;
    if (coupon.maxDiscount) {
      return Math.min(discountAmount, coupon.maxDiscount);
    }
    return discountAmount;
  }
  return 0;
};
