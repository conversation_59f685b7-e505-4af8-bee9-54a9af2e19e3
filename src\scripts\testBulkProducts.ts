import { generateCSVTemplate, downloadCSV } from '../utils/csvUtils';
import { BulkExportOptions } from '../types/bulkProduct';

// Test script to generate and download CSV template
const testBulkProducts = async () => {
  try {
    console.log('Testing bulk product functionality...');
    
    // Test 1: Generate CSV template
    console.log('1. Generating CSV template...');
    const template = generateCSVTemplate();
    console.log('Template generated successfully');
    console.log('Template preview (first 200 chars):', template.substring(0, 200));
    
    // Test 2: Download template (in browser environment)
    if (typeof window !== 'undefined') {
      console.log('2. Downloading template...');
      downloadCSV(template, 'test-template.csv');
      console.log('Template download initiated');
    } else {
      console.log('2. Skipping download test (not in browser environment)');
    }
    
    console.log('Bulk product functionality test completed successfully!');
  } catch (error) {
    console.error('Error testing bulk products:', error);
  }
};

// Export for use in browser console
if (typeof window !== 'undefined') {
  (window as any).testBulkProducts = testBulkProducts;
}

export default testBulkProducts;
