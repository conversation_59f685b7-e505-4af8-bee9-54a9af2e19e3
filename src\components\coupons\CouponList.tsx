import React, { useState, useEffect } from 'react';
import { Plus, RefreshCw, Search, Filter } from 'lucide-react';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Select } from '../ui/Select';
import { Loader } from '../ui/Loader';
import { CouponCard } from './CouponCard';
import { CouponForm } from './CouponForm';
import { Coupon } from '../../types';
import { getCoupons, addCoupon, updateCoupon, deleteCoupon } from '../../services/couponService';

export const CouponList = () => {
  const [coupons, setCoupons] = useState<Coupon[]>([]);
  const [filteredCoupons, setFilteredCoupons] = useState<Coupon[]>([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingCoupon, setEditingCoupon] = useState<Coupon | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');

  // Load coupons
  const loadCoupons = async () => {
    setLoading(true);
    try {
      const result = await getCoupons(100); // Load more coupons for now
      setCoupons(result.coupons);
      setFilteredCoupons(result.coupons);
    } catch (error) {
      console.error('Error loading coupons:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadCoupons();
  }, []);

  // Filter coupons based on search and filters
  useEffect(() => {
    let filtered = coupons;

    // Search filter
    if (searchQuery) {
      filtered = filtered.filter(coupon =>
        coupon.code.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (coupon.description && coupon.description.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Status filter
    if (statusFilter !== 'all') {
      const now = new Date();
      filtered = filtered.filter(coupon => {
        const expiryDate = new Date(coupon.expiresAt);
        const startDate = coupon.startAt ? new Date(coupon.startAt) : null;

        switch (statusFilter) {
          case 'active':
            return coupon.isActive && expiryDate > now && (!startDate || startDate <= now) && 
                   (!coupon.usageLimitGlobal || coupon.usedCount < coupon.usageLimitGlobal);
          case 'inactive':
            return !coupon.isActive;
          case 'expired':
            return expiryDate < now;
          case 'scheduled':
            return startDate && startDate > now;
          case 'limit_reached':
            return coupon.usageLimitGlobal && coupon.usedCount >= coupon.usageLimitGlobal;
          default:
            return true;
        }
      });
    }

    // Type filter
    if (typeFilter !== 'all') {
      filtered = filtered.filter(coupon => coupon.type === typeFilter);
    }

    setFilteredCoupons(filtered);
  }, [coupons, searchQuery, statusFilter, typeFilter]);

  const handleAddCoupon = async (couponData: Omit<Coupon, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      const result = await addCoupon(couponData);
      if (result) {
        await loadCoupons(); // Reload to get the latest data
        setShowForm(false);
      } else {
        alert('Failed to create coupon. Please try again.');
      }
    } catch (error) {
      console.error('Error adding coupon:', error);
      alert('Error creating coupon. Please try again.');
    }
  };

  const handleEditCoupon = async (couponData: Omit<Coupon, 'id' | 'createdAt' | 'updatedAt'>) => {
    if (!editingCoupon) return;

    try {
      const result = await updateCoupon(editingCoupon.id, couponData);
      if (result) {
        await loadCoupons(); // Reload to get the latest data
        setEditingCoupon(null);
        setShowForm(false);
      } else {
        alert('Failed to update coupon. Please try again.');
      }
    } catch (error) {
      console.error('Error updating coupon:', error);
      alert('Error updating coupon. Please try again.');
    }
  };

  const handleDeleteCoupon = async (coupon: Coupon) => {
    if (!confirm(`Are you sure you want to delete the coupon "${coupon.code}"?`)) {
      return;
    }

    try {
      const result = await deleteCoupon(coupon.id);
      if (result) {
        await loadCoupons(); // Reload to get the latest data
      } else {
        alert('Failed to delete coupon. Please try again.');
      }
    } catch (error) {
      console.error('Error deleting coupon:', error);
      alert('Error deleting coupon. Please try again.');
    }
  };

  const openEditForm = (coupon: Coupon) => {
    setEditingCoupon(coupon);
    setShowForm(true);
  };

  const closeForm = () => {
    setShowForm(false);
    setEditingCoupon(null);
  };

  const statusOptions = [
    { value: 'all', label: 'All Status' },
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'Inactive' },
    { value: 'expired', label: 'Expired' },
    { value: 'scheduled', label: 'Scheduled' },
    { value: 'limit_reached', label: 'Limit Reached' }
  ];

  const typeOptions = [
    { value: 'all', label: 'All Types' },
    { value: 'FLAT', label: 'Flat Amount' },
    { value: 'PERCENT', label: 'Percentage' }
  ];

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <Loader size="large" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Coupons</h2>
          <p className="text-gray-600">Manage discount coupons and promotional codes</p>
        </div>
        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={loadCoupons}
            disabled={loading}
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button
            variant="primary"
            onClick={() => setShowForm(true)}
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Coupon
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Input
          placeholder="Search coupons..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          startIcon={<Search className="w-4 h-4 text-gray-400" />}
        />
        <Select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          options={statusOptions}
        />
        <Select
          value={typeFilter}
          onChange={(e) => setTypeFilter(e.target.value)}
          options={typeOptions}
        />
        <div className="flex items-center text-sm text-gray-600">
          <Filter className="w-4 h-4 mr-2" />
          {filteredCoupons.length} of {coupons.length} coupons
        </div>
      </div>

      {/* Coupons Grid */}
      {filteredCoupons.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-500 mb-4">
            {coupons.length === 0 ? 'No coupons found' : 'No coupons match your filters'}
          </div>
          {coupons.length === 0 && (
            <Button
              variant="primary"
              onClick={() => setShowForm(true)}
            >
              <Plus className="w-4 h-4 mr-2" />
              Create Your First Coupon
            </Button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredCoupons.map((coupon) => (
            <CouponCard
              key={coupon.id}
              coupon={coupon}
              onEdit={openEditForm}
              onDelete={handleDeleteCoupon}
            />
          ))}
        </div>
      )}

      {/* Coupon Form Modal */}
      <CouponForm
        isOpen={showForm}
        onClose={closeForm}
        onSave={editingCoupon ? handleEditCoupon : handleAddCoupon}
        initialData={editingCoupon || undefined}
        isEditing={!!editingCoupon}
      />
    </div>
  );
};
