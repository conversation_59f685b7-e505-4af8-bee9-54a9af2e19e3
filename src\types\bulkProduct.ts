// Bulk Product Management Types

export interface BulkProductRow {
  // Core product fields
  name: string;
  description: string;
  price: number;
  mrp: number;
  stock: number;
  unit: string;
  barcode: string;
  status: 'available' | 'out_of_stock' | 'coming_soon';
  isVisible: boolean;

  // Category fields
  category: string; // Legacy field
  categoryIDs: string; // Comma-separated category IDs
  categoryPath: string; // Full category path like "Vegetable>Fruits"
  primaryCategoryID: string;

  // Farmer and additional info
  farmerId: string;
  nutrition: string;
  storageInstruction: string;

  // Pre-order fields
  isPreOrder: boolean;
  preOrderStartAt: string; // ISO date string
  preOrderEndAt: string; // ISO date string
  harvestOffsetDays: number;

  // Variant fields
  hasVariants: boolean;
  defaultVariantID: string;

  // Variant-specific fields (for variant rows)
  isVariant?: boolean;
  parentBarcode?: string; // Barcode of parent product for variants
  variationValues?: string; // JSON string of variation values
  variantImage?: string;

  // Images
  images: string; // Comma-separated image URLs
}

export interface BulkImportResult {
  success: boolean;
  totalRows: number;
  successCount: number;
  errorCount: number;
  skippedCount: number;
  errors: BulkImportError[];
  warnings: BulkImportWarning[];
}

export interface BulkImportError {
  row: number;
  field?: string;
  message: string;
  data?: any;
}

export interface BulkImportWarning {
  row: number;
  message: string;
  data?: any;
}

export interface BulkImportOptions {
  overwriteExisting: boolean;
  validateOnly: boolean;
  skipInvalidRows: boolean;
}

export interface BulkExportOptions {
  includeVariants: boolean;
  includeImages: boolean;
  categoryFilter?: string;
  statusFilter?: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// CSV Template headers
export const CSV_HEADERS = [
  'name',
  'description',
  'price',
  'mrp',
  'stock',
  'unit',
  'barcode',
  'status',
  'isVisible',
  'category',
  'categoryPath',
  'primaryCategoryID',
  'farmerId',
  'nutrition',
  'storageInstruction',
  'isPreOrder',
  'preOrderStartAt',
  'preOrderEndAt',
  'harvestOffsetDays',
  'hasVariants',
  'defaultVariantID',
  'isVariant',
  'parentBarcode',
  'variationValues',
  'variantImage',
  'images'
] as const;

export type CSVHeader = typeof CSV_HEADERS[number];
