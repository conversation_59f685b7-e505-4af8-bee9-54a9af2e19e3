// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getAnalytics, isSupported } from "firebase/analytics";
import { getAuth } from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import { getStorage } from "firebase/storage";

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: "AIzaSyDuFqIFNTnydJciJ97oOYbB4Rmsj58ABc4",
  authDomain: "vrisham-cad24.firebaseapp.com",
  projectId: "vrisham-cad24",
  storageBucket: "vrisham-cad24.appspot.com",
  messagingSenderId: "404878904416",
  appId: "1:404878904416:web:ea282cffe2e6fe3ade8705",
  measurementId: "G-BQGD3Y99HH"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Only initialize analytics in browser environments
const analytics = typeof window !== 'undefined' ? getAnalytics(app) : null;
const auth = getAuth(app);
const db = getFirestore(app);
const storage = getStorage(app);

export { app, analytics, auth, db, storage };

