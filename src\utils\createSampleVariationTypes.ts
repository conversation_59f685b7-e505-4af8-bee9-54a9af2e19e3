import { addVariationType } from '../services/variationTypeService';

export const createSampleVariationTypes = async () => {
  try {
    console.log('Creating sample variation types...');

    // Create Size variation type
    const sizeId = await addVariationType({
      name: 'Size',
      description: 'Product size variations',
      options: ['Small', 'Medium', 'Large', 'Extra Large']
    });

    // Create Color variation type
    const colorId = await addVariationType({
      name: 'Color',
      description: 'Product color variations',
      options: ['Red', 'Blue', 'Green', 'Yellow', 'Black', 'White']
    });

    // Create Weight variation type
    const weightId = await addVariationType({
      name: 'Weight',
      description: 'Product weight variations',
      options: ['250g', '500g', '1kg', '2kg', '5kg']
    });

    console.log('Sample variation types created:', { sizeId, colorId, weightId });
    return { sizeId, colorId, weightId };
  } catch (error) {
    console.error('Error creating sample variation types:', error);
    return null;
  }
};
