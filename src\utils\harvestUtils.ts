import { Product, ProductVariant } from '../types';

/**
 * Calculate the harvest date based on offset days
 * @param offsetDays -1 (yesterday), 0 (today), 1 (tomorrow)
 * @param baseDate Optional base date (defaults to today)
 * @returns Date object representing the harvest date
 */
export const calculateHarvestDate = (offsetDays: number, baseDate?: Date): Date => {
  const date = baseDate ? new Date(baseDate) : new Date();
  date.setDate(date.getDate() + offsetDays);
  return date;
};

/**
 * Format harvest date for display
 * @param offsetDays -1 (yesterday), 0 (today), 1 (tomorrow)
 * @param baseDate Optional base date (defaults to today)
 * @returns Formatted string like "Harvest on 15-Dec" or "Harvested on 13-Dec"
 */
export const formatHarvestDate = (offsetDays: number, baseDate?: Date): string => {
  const harvestDate = calculateHarvestDate(offsetDays, baseDate);
  const today = new Date();
  
  // Format date as DD-MMM
  const options: Intl.DateTimeFormatOptions = { 
    day: '2-digit', 
    month: 'short' 
  };
  const formattedDate = harvestDate.toLocaleDateString('en-GB', options);
  
  // Determine tense based on offset
  if (offsetDays === -1) {
    return `Harvested on ${formattedDate}`;
  } else if (offsetDays === 0) {
    return `Harvest today (${formattedDate})`;
  } else {
    return `Harvest on ${formattedDate}`;
  }
};

/**
 * Get harvest offset days from product or variant (variant overrides product)
 * @param product Product object
 * @param variant Optional variant object
 * @returns Harvest offset days
 */
export const getHarvestOffsetDays = (product: Product, variant?: ProductVariant | null): number => {
  // Variant overrides product if specified
  if (variant && variant.harvestOffsetDays !== null && variant.harvestOffsetDays !== undefined) {
    return variant.harvestOffsetDays;
  }
  
  // Fall back to product setting
  return product.harvestOffsetDays !== undefined ? product.harvestOffsetDays : 0;
};

/**
 * Check if product/variant is in pre-order mode
 * @param product Product object
 * @param variant Optional variant object
 * @returns True if in pre-order mode
 */
export const isPreOrder = (product: Product, variant?: ProductVariant | null): boolean => {
  // Variant overrides product if specified
  if (variant && variant.isPreOrder !== null && variant.isPreOrder !== undefined) {
    return variant.isPreOrder;
  }
  
  // Fall back to product setting
  return product.isPreOrder || false;
};

/**
 * Check if pre-order is currently active (within date range if specified)
 * @param product Product object
 * @param variant Optional variant object
 * @returns True if pre-order is active
 */
export const isPreOrderActive = (product: Product, variant?: ProductVariant | null): boolean => {
  if (!isPreOrder(product, variant)) {
    return false;
  }
  
  const now = new Date();
  
  // Get start and end dates (variant overrides product)
  let startAt = variant?.preOrderStartAt || product.preOrderStartAt;
  let endAt = variant?.preOrderEndAt || product.preOrderEndAt;
  
  // Convert Firestore timestamps to Date objects if needed
  if (startAt && typeof startAt.toDate === 'function') {
    startAt = startAt.toDate();
  }
  if (endAt && typeof endAt.toDate === 'function') {
    endAt = endAt.toDate();
  }
  
  // Check if current time is within the pre-order window
  if (startAt && now < new Date(startAt)) {
    return false; // Pre-order hasn't started yet
  }
  
  if (endAt && now > new Date(endAt)) {
    return false; // Pre-order has ended
  }
  
  return true; // Pre-order is active
};

/**
 * Get pre-order status text for display
 * @param product Product object
 * @param variant Optional variant object
 * @returns Status text like "Pre-Order", "Pre-Order Starts Soon", "Pre-Order Ended"
 */
export const getPreOrderStatusText = (product: Product, variant?: ProductVariant | null): string => {
  if (!isPreOrder(product, variant)) {
    return '';
  }
  
  const now = new Date();
  
  // Get start and end dates (variant overrides product)
  let startAt = variant?.preOrderStartAt || product.preOrderStartAt;
  let endAt = variant?.preOrderEndAt || product.preOrderEndAt;
  
  // Convert Firestore timestamps to Date objects if needed
  if (startAt && typeof startAt.toDate === 'function') {
    startAt = startAt.toDate();
  }
  if (endAt && typeof endAt.toDate === 'function') {
    endAt = endAt.toDate();
  }
  
  // Check status
  if (startAt && now < new Date(startAt)) {
    const startDate = new Date(startAt).toLocaleDateString();
    return `Pre-Order starts ${startDate}`;
  }
  
  if (endAt && now > new Date(endAt)) {
    return 'Pre-Order ended';
  }
  
  if (endAt) {
    const endDate = new Date(endAt).toLocaleDateString();
    return `Pre-Order until ${endDate}`;
  }
  
  return 'Pre-Order';
};

/**
 * Get harvest day description for offset
 * @param offsetDays -1, 0, or 1
 * @returns Description like "Yesterday", "Today", "Tomorrow"
 */
export const getHarvestDayDescription = (offsetDays: number): string => {
  switch (offsetDays) {
    case -1:
      return 'Yesterday';
    case 0:
      return 'Today';
    case 1:
      return 'Tomorrow';
    default:
      return `${offsetDays > 0 ? '+' : ''}${offsetDays} days`;
  }
};
