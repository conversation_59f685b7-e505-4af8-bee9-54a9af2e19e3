import { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { AlertCircle, Search, Filter, ChevronDown, X, Clock, Truck, Package, CheckCircle, FilePenLine, RefreshCw, Calendar, DollarSign } from 'lucide-react';
import { Card } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { PaymentStatusBadge } from '../../components/ui/StatusBadge';
import { Modal } from '../../components/ui/Modal';
import { Order } from '../../types';
import { getAllOrders, updateOrderStatus as updateOrderStatusInFirebase, updateOrderPaymentStatus } from '../../services/orderService';

export const OrdersKanban = () => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [filteredPaymentMethod, setFilteredPaymentMethod] = useState<string | null>(null);
  const [draggingOrderId, setDraggingOrderId] = useState<string | null>(null);
  const [showCancellationModal, setShowCancellationModal] = useState(false);
  const [showPaymentConfirmation, setShowPaymentConfirmation] = useState(false);
  const [cancellationReason, setCancellationReason] = useState('');
  const pendingOrderRef = useRef<{orderId: string, newStatus: Order['status']} | null>(null);

  // Date filter states
  const [isDateFilterOpen, setIsDateFilterOpen] = useState(false);
  const [dateFilter, setDateFilter] = useState<'today' | '3days' | '5days' | 'week' | 'month' | 'custom'>('today');
  const [customStartDate, setCustomStartDate] = useState<string>('');
  const [customEndDate, setCustomEndDate] = useState<string>('');
  const [showCustomDatePicker, setShowCustomDatePicker] = useState(false);

  // Fetch orders on component mount
  useEffect(() => {
    const fetchOrders = async () => {
      try {
        setLoading(true);
        setError(null);
        const fetchedOrders = await getAllOrders();
        console.log('Fetched orders:', fetchedOrders);
        setOrders(fetchedOrders);
      } catch (err) {
        console.error('Error fetching orders:', err);

        // Check if the error is related to missing index
        if (err.message && err.message.includes('index')) {
          setError('Orders could not be loaded due to a missing Firebase index. Please contact the administrator to create the required index. You can still create new orders from the POS page.');
        } else {
          setError('Failed to load orders. Please try again.');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchOrders();
  }, []);

  // Get date range based on selected filter
  const getDateRange = (): { startDate: Date, endDate: Date } => {
    const now = new Date();
    let endDate = new Date(now);
    endDate.setHours(23, 59, 59, 999); // End of today

    let startDate = new Date(now);
    startDate.setHours(0, 0, 0, 0); // Start of today

    switch (dateFilter) {
      case 'today':
        // Already set to today
        break;
      case '3days':
        startDate.setDate(startDate.getDate() - 2); // 3 days including today
        break;
      case '5days':
        startDate.setDate(startDate.getDate() - 4); // 5 days including today
        break;
      case 'week':
        startDate.setDate(startDate.getDate() - 6); // 7 days including today
        break;
      case 'month':
        startDate.setMonth(startDate.getMonth() - 1); // Last 30 days
        break;
      case 'custom':
        if (customStartDate) {
          try {
            const parsedStartDate = new Date(customStartDate);
            if (!isNaN(parsedStartDate.getTime())) {
              startDate = parsedStartDate;
              startDate.setHours(0, 0, 0, 0);
            }
          } catch (error) {
            console.error("Error parsing start date:", error);
          }
        }
        if (customEndDate) {
          try {
            const parsedEndDate = new Date(customEndDate);
            if (!isNaN(parsedEndDate.getTime())) {
              endDate = parsedEndDate;
              endDate.setHours(23, 59, 59, 999);
            }
          } catch (error) {
            console.error("Error parsing end date:", error);
          }
        }
        break;
    }

    return { startDate, endDate };
  };

  // Filter orders based on search query, payment method, and date range
  const filteredOrders = orders.filter((order) => {
    const matchesSearch =
      order.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
      order.customerName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      order.customerPhone?.includes(searchQuery);

    const matchesPaymentMethod = !filteredPaymentMethod || order.paymentMethod === filteredPaymentMethod;

    // Date filtering
    const { startDate, endDate } = getDateRange();
    const orderDate = new Date(order.createdAt);
    const matchesDateRange = orderDate >= startDate && orderDate <= endDate;

    return matchesSearch && matchesPaymentMethod && matchesDateRange;
  });

  // Group orders by status for the Kanban board
  const ordersByStatus = {
    placed: filteredOrders.filter(order => order.status === 'placed'),
    processing: filteredOrders.filter(order => order.status === 'processing'),
    out_for_delivery: filteredOrders.filter(order => order.status === 'out_for_delivery'),
    delivered: filteredOrders.filter(order => order.status === 'delivered'),
    cancelled: filteredOrders.filter(order => order.status === 'cancelled'),
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-IN', {
      day: 'numeric',
      month: 'short',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const handleDragStart = (e: React.DragEvent, orderId: string) => {
    setDraggingOrderId(orderId);
    e.dataTransfer.setData('orderId', orderId);
    e.dataTransfer.effectAllowed = 'move';

    // Add dragging styles
    if (e.target instanceof HTMLElement) {
      e.target.classList.add('opacity-50');
      e.target.classList.add('scale-105');
      e.target.classList.add('shadow-lg');
    }
  };

  const handleDragEnd = (e: React.DragEvent) => {
    setDraggingOrderId(null);
    // Remove dragging styles
    if (e.target instanceof HTMLElement) {
      e.target.classList.remove('opacity-50');
      e.target.classList.remove('scale-105');
      e.target.classList.remove('shadow-lg');
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    // Add visual feedback for drop target
    if (e.currentTarget instanceof HTMLElement) {
      e.currentTarget.classList.add('bg-primary-50');
      e.currentTarget.classList.add('ring-2');
      e.currentTarget.classList.add('ring-primary-300');
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    // Remove visual feedback
    if (e.currentTarget instanceof HTMLElement) {
      e.currentTarget.classList.remove('bg-primary-50');
      e.currentTarget.classList.remove('ring-2');
      e.currentTarget.classList.remove('ring-primary-300');
    }
  };

  const handleDrop = (e: React.DragEvent, newStatus: Order['status']) => {
    e.preventDefault();
    // Remove visual feedback
    if (e.currentTarget instanceof HTMLElement) {
      e.currentTarget.classList.remove('bg-primary-50');
      e.currentTarget.classList.remove('ring-2');
      e.currentTarget.classList.remove('ring-primary-300');
    }

    const orderId = e.dataTransfer.getData('orderId');
    if (!orderId || !draggingOrderId) return;

    // If dropping to cancelled status, show confirmation modal
    if (newStatus === 'cancelled') {
      pendingOrderRef.current = { orderId, newStatus };
      setShowCancellationModal(true);
      return;
    }

    // For orders being moved to delivered status, check if it's a cash order with unpaid status
    if (newStatus === 'delivered') {
      const order = orders.find(o => o.id === orderId);
      if (order && order.paymentMethod === 'cash' && order.paymentStatus === 'unpaid') {
        // Show payment confirmation dialog
        pendingOrderRef.current = { orderId, newStatus };
        setShowPaymentConfirmation(true);
        return;
      }
    }

    // Update order status for non-cancelled statuses
    updateOrderStatus(orderId, newStatus);
  };

  const updateOrderStatus = async (orderId: string, newStatus: Order['status'], reason?: string) => {
    try {
      // First update the UI optimistically
      setOrders(currentOrders =>
        currentOrders.map(order =>
          order.id === orderId
            ? {
                ...order,
                status: newStatus,
                updatedAt: new Date().toISOString(),
                cancellationReason: newStatus === 'cancelled' ? reason : order.cancellationReason
              }
            : order
        )
      );

      // Then update in Firebase
      const success = await updateOrderStatusInFirebase(orderId, newStatus, reason);

      if (!success) {
        // If the update failed, revert the UI change
        console.error('Failed to update order status in Firebase');
        // Refresh orders to get the current state
        const refreshedOrders = await getAllOrders();
        setOrders(refreshedOrders);
      }
    } catch (error) {
      console.error('Error updating order status:', error);
      // Refresh orders to get the current state
      const refreshedOrders = await getAllOrders();
      setOrders(refreshedOrders);
    }
  };

  const handleCancellationConfirm = async () => {
    if (pendingOrderRef.current && cancellationReason.trim()) {
      const { orderId, newStatus } = pendingOrderRef.current;

      // Close the modal first for better UX
      setShowCancellationModal(false);

      try {
        await updateOrderStatus(orderId, newStatus, cancellationReason);
      } catch (error) {
        console.error('Error confirming cancellation:', error);
      } finally {
        setCancellationReason('');
        pendingOrderRef.current = null;
      }
    }
  };

  // Handle payment confirmation
  const handlePaymentConfirmation = async (isPaid: boolean) => {
    if (pendingOrderRef.current) {
      const { orderId, newStatus } = pendingOrderRef.current;

      // Close the modal first for better UX
      setShowPaymentConfirmation(false);

      try {
        // Update the order status to delivered
        await updateOrderStatus(orderId, newStatus);

        // Update the payment status based on the confirmation
        const paymentStatus = isPaid ? 'paid' : 'unpaid';
        const paymentSuccess = await updateOrderPaymentStatus(orderId, paymentStatus);

        if (paymentSuccess) {
          // Update the local state
          setOrders(currentOrders =>
            currentOrders.map(order =>
              order.id === orderId
                ? {
                    ...order,
                    paymentStatus: paymentStatus
                  }
                : order
            )
          );

          // Show a success message
          alert(`Order marked as delivered and payment status set to ${paymentStatus}`);
        } else {
          alert('Failed to update payment status. Please try again.');
        }
      } catch (error) {
        console.error('Error updating order:', error);
        alert('An error occurred while updating the order.');
      } finally {
        pendingOrderRef.current = null;
      }
    }
  };

  // Get date filter label for display
  const getDateFilterLabel = (): string => {
    switch (dateFilter) {
      case 'today':
        return 'Today';
      case '3days':
        return 'Last 3 Days';
      case '5days':
        return 'Last 5 Days';
      case 'week':
        return 'Last Week';
      case 'month':
        return 'Last Month';
      case 'custom':
        if (customStartDate && customEndDate) {
          try {
            // Validate dates before formatting
            const startDate = new Date(customStartDate);
            const endDate = new Date(customEndDate);

            if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
              return 'Custom Range';
            }

            return `${formatDateShort(customStartDate)} - ${formatDateShort(customEndDate)}`;
          } catch (error) {
            console.error("Error in date filter label:", error);
            return 'Custom Range';
          }
        }
        return 'Custom Range';
      default:
        return 'Date Filter';
    }
  };

  // Format date for display in filter
  const formatDateShort = (dateString: string) => {
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return "Invalid date";
      }
      return date.toLocaleDateString('en-IN', {
        day: 'numeric',
        month: 'short',
      });
    } catch (error) {
      console.error("Error formatting date:", error);
      return "Invalid date";
    }
  };

  // Handle custom date selection
  const handleCustomDateSelect = () => {
    if (!customStartDate || !customEndDate) {
      return; // Don't proceed if dates are not set
    }

    try {
      // Validate dates
      const startDate = new Date(customStartDate);
      const endDate = new Date(customEndDate);

      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        console.error("Invalid date format");
        return;
      }

      // Check if end date is after start date
      if (endDate < startDate) {
        // Swap dates if end date is before start date
        const temp = customStartDate;
        setCustomStartDate(customEndDate);
        setCustomEndDate(temp);
      }

      setDateFilter('custom');
      setShowCustomDatePicker(false);
      setIsDateFilterOpen(false);
    } catch (error) {
      console.error("Error processing custom dates:", error);
    }
  };

  // Reset date filter
  const handleResetDateFilter = () => {
    setDateFilter('today');
    setCustomStartDate('');
    setCustomEndDate('');
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-display font-bold text-gray-900">Orders</h1>
          <p className="text-gray-500">Manage and track all customer orders</p>
        </div>
        <div className="flex space-x-2 w-full md:w-auto">
          <Link to="/pos">
            <Button variant="primary" size="sm">
              Create New Order
            </Button>
          </Link>
        </div>
      </div>

      {/* Cancellation Reason Modal */}
      <Modal
        isOpen={showCancellationModal}
        onClose={() => {
          setShowCancellationModal(false);
          setCancellationReason('');
          pendingOrderRef.current = null;
        }}
        title="Cancellation Reason"
        maxWidth="md"
      >
        <div className="space-y-4">
          <p className="text-gray-600">Please provide a reason for cancelling this order:</p>
          <textarea
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            rows={4}
            value={cancellationReason}
            onChange={(e) => setCancellationReason(e.target.value)}
            placeholder="Enter cancellation reason..."
          />
          <div className="flex justify-end space-x-2 pt-2">
            <Button
              variant="outline"
              onClick={() => {
                setShowCancellationModal(false);
                setCancellationReason('');
                pendingOrderRef.current = null;
              }}
            >
              Cancel
            </Button>
            <Button
              variant="danger"
              onClick={handleCancellationConfirm}
              disabled={!cancellationReason.trim()}
            >
              Confirm Cancellation
            </Button>
          </div>
        </div>
      </Modal>

      {/* Payment Confirmation Modal */}
      <Modal
        isOpen={showPaymentConfirmation}
        onClose={() => {
          setShowPaymentConfirmation(false);
          pendingOrderRef.current = null;
        }}
        title="Payment Confirmation"
        maxWidth="md"
      >
        <div className="p-6">
          <div className="flex items-center mb-4 text-amber-600">
            <DollarSign className="w-6 h-6 mr-2" />
            <h3 className="text-lg font-medium">Confirm Payment Status</h3>
          </div>

          <p className="text-gray-700 mb-6">
            This order has a cash payment method. Has the customer paid for this order?
          </p>

          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={() => handlePaymentConfirmation(false)}
            >
              Not Paid Yet
            </Button>
            <Button
              variant="primary"
              onClick={() => handlePaymentConfirmation(true)}
            >
              Mark as Paid
            </Button>
          </div>
        </div>
      </Modal>

      {/* Filters & Search */}
      <div className="flex flex-col sm:flex-row gap-3">
        <div className="relative flex-grow">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Search className="w-4 h-4 text-gray-400" />
          </div>
          <input
            type="search"
            className="block w-full p-2.5 pl-10 text-sm text-gray-900 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
            placeholder="Search by order ID, customer name or phone..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        {/* Date Filter */}
        <div className="relative">
          <Button
            variant="outline"
            size="md"
            className="w-full sm:w-auto"
            icon={<Calendar size={16} />}
            onClick={() => setIsDateFilterOpen(!isDateFilterOpen)}
          >
            {getDateFilterLabel()}
            <ChevronDown size={16} className="ml-1" />
          </Button>

          {isDateFilterOpen && (
            <div className="absolute z-10 mt-2 w-64 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
              <div className="py-1">
                <button
                  className={`block px-4 py-2 text-sm w-full text-left ${
                    dateFilter === 'today' ? 'bg-primary-50 text-primary-700' : 'text-gray-700 hover:bg-gray-100'
                  }`}
                  onClick={() => {
                    setDateFilter('today');
                    setIsDateFilterOpen(false);
                  }}
                >
                  Today
                </button>
                <button
                  className={`block px-4 py-2 text-sm w-full text-left ${
                    dateFilter === '3days' ? 'bg-primary-50 text-primary-700' : 'text-gray-700 hover:bg-gray-100'
                  }`}
                  onClick={() => {
                    setDateFilter('3days');
                    setIsDateFilterOpen(false);
                  }}
                >
                  Last 3 Days
                </button>
                <button
                  className={`block px-4 py-2 text-sm w-full text-left ${
                    dateFilter === '5days' ? 'bg-primary-50 text-primary-700' : 'text-gray-700 hover:bg-gray-100'
                  }`}
                  onClick={() => {
                    setDateFilter('5days');
                    setIsDateFilterOpen(false);
                  }}
                >
                  Last 5 Days
                </button>
                <button
                  className={`block px-4 py-2 text-sm w-full text-left ${
                    dateFilter === 'week' ? 'bg-primary-50 text-primary-700' : 'text-gray-700 hover:bg-gray-100'
                  }`}
                  onClick={() => {
                    setDateFilter('week');
                    setIsDateFilterOpen(false);
                  }}
                >
                  Last Week
                </button>
                <button
                  className={`block px-4 py-2 text-sm w-full text-left ${
                    dateFilter === 'month' ? 'bg-primary-50 text-primary-700' : 'text-gray-700 hover:bg-gray-100'
                  }`}
                  onClick={() => {
                    setDateFilter('month');
                    setIsDateFilterOpen(false);
                  }}
                >
                  Last Month
                </button>
                <button
                  className={`block px-4 py-2 text-sm w-full text-left ${
                    dateFilter === 'custom' ? 'bg-primary-50 text-primary-700' : 'text-gray-700 hover:bg-gray-100'
                  }`}
                  onClick={() => {
                    setShowCustomDatePicker(true);
                  }}
                >
                  Custom Range
                </button>
              </div>
            </div>
          )}

          {/* Custom Date Picker */}
          {showCustomDatePicker && (
            <div className="absolute z-20 mt-2 p-4 w-72 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
              <div className="space-y-4">
                <h3 className="text-sm font-medium text-gray-900">Select Date Range</h3>
                <div>
                  <label className="block text-xs text-gray-700 mb-1">Start Date</label>
                  <input
                    type="date"
                    className="w-full p-2 text-sm border border-gray-300 rounded-md"
                    value={customStartDate}
                    onChange={(e) => setCustomStartDate(e.target.value)}
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-700 mb-1">End Date</label>
                  <input
                    type="date"
                    className="w-full p-2 text-sm border border-gray-300 rounded-md"
                    value={customEndDate}
                    onChange={(e) => setCustomEndDate(e.target.value)}
                  />
                </div>
                <div className="flex justify-between">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setShowCustomDatePicker(false);
                      setIsDateFilterOpen(false);
                    }}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="primary"
                    size="sm"
                    onClick={handleCustomDateSelect}
                    disabled={!customStartDate || !customEndDate}
                  >
                    Apply
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Payment Method Filter */}
        <div className="relative">
          <Button
            variant="outline"
            size="md"
            className="w-full sm:w-auto"
            icon={<Filter size={16} />}
            onClick={() => setIsFilterOpen(!isFilterOpen)}
          >
            Filters
            <ChevronDown size={16} className="ml-1" />
          </Button>

          {isFilterOpen && (
            <div className="absolute z-10 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
              <div className="py-1">
                <button
                  className={`block px-4 py-2 text-sm w-full text-left ${
                    !filteredPaymentMethod ? 'bg-primary-50 text-primary-700' : 'text-gray-700 hover:bg-gray-100'
                  }`}
                  onClick={() => {
                    setFilteredPaymentMethod(null);
                    setIsFilterOpen(false);
                  }}
                >
                  All Payment Methods
                </button>
                <button
                  className={`block px-4 py-2 text-sm w-full text-left ${
                    filteredPaymentMethod === 'cash' ? 'bg-primary-50 text-primary-700' : 'text-gray-700 hover:bg-gray-100'
                  }`}
                  onClick={() => {
                    setFilteredPaymentMethod('cash');
                    setIsFilterOpen(false);
                  }}
                >
                  Cash
                </button>
                <button
                  className={`block px-4 py-2 text-sm w-full text-left ${
                    filteredPaymentMethod === 'card' ? 'bg-primary-50 text-primary-700' : 'text-gray-700 hover:bg-gray-100'
                  }`}
                  onClick={() => {
                    setFilteredPaymentMethod('card');
                    setIsFilterOpen(false);
                  }}
                >
                  Card
                </button>
                <button
                  className={`block px-4 py-2 text-sm w-full text-left ${
                    filteredPaymentMethod === 'upi' ? 'bg-primary-50 text-primary-700' : 'text-gray-700 hover:bg-gray-100'
                  }`}
                  onClick={() => {
                    setFilteredPaymentMethod('upi');
                    setIsFilterOpen(false);
                  }}
                >
                  UPI
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Active Filters Display */}
      <div className="flex flex-wrap items-center gap-2">
        {filteredPaymentMethod && (
          <div className="flex items-center space-x-1 px-2 py-1 rounded-md bg-gray-100 text-xs text-gray-700">
            <span>Payment: {filteredPaymentMethod}</span>
            <button
              onClick={() => setFilteredPaymentMethod(null)}
              className="ml-1 p-0.5 rounded-full hover:bg-gray-200 transition-colors"
            >
              <X size={12} />
            </button>
          </div>
        )}

        {dateFilter !== 'today' && (
          <div className="flex items-center space-x-1 px-2 py-1 rounded-md bg-gray-100 text-xs text-gray-700">
            <span>Date: {getDateFilterLabel()}</span>
            <button
              onClick={handleResetDateFilter}
              className="ml-1 p-0.5 rounded-full hover:bg-gray-200 transition-colors"
            >
              <X size={12} />
            </button>
          </div>
        )}
      </div>

      {/* Kanban Board */}
      {loading ? (
        <div className="flex flex-col items-center justify-center py-12">
          <RefreshCw className="w-12 h-12 text-primary-500 animate-spin mb-4" />
          <h2 className="text-xl font-semibold text-gray-900">Loading orders...</h2>
        </div>
      ) : error ? (
        <div className="flex flex-col items-center justify-center py-12">
          <AlertCircle className="w-12 h-12 text-red-500 mb-4" />
          <h2 className="text-xl font-semibold text-gray-900">Error loading orders</h2>
          <p className="text-gray-500 mt-2">{error}</p>
          <Button
            variant="primary"
            className="mt-4"
            onClick={async () => {
              try {
                setLoading(true);
                setError(null);
                const fetchedOrders = await getAllOrders();
                setOrders(fetchedOrders);
              } catch (err) {
                console.error('Error refreshing orders:', err);
                setError('Failed to refresh orders. Please try again.');
              } finally {
                setLoading(false);
              }
            }}
          >
            Retry
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
          {/* Order Placed Column */}
          <KanbanColumn
            title="Order Placed"
            icon={<Clock className="h-5 w-5 text-blue-500" />}
            orders={ordersByStatus.placed}
            status="placed"
            bgColor="blue"
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            formatDate={formatDate}
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
          />

          {/* Processing Column */}
          <KanbanColumn
            title="Processing"
            icon={<Package className="h-5 w-5 text-purple-500" />}
            orders={ordersByStatus.processing}
            status="processing"
            bgColor="purple"
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            formatDate={formatDate}
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
          />

          {/* Out for Delivery Column */}
          <KanbanColumn
            title="Out for Delivery"
            icon={<Truck className="h-5 w-5 text-amber-500" />}
            orders={ordersByStatus.out_for_delivery}
            status="out_for_delivery"
            bgColor="amber"
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            formatDate={formatDate}
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
          />

          {/* Delivered Column */}
          <KanbanColumn
            title="Delivered"
            icon={<CheckCircle className="h-5 w-5 text-green-500" />}
            orders={ordersByStatus.delivered}
            status="delivered"
            bgColor="green"
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            formatDate={formatDate}
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
          />

          {/* Cancelled Column */}
          <KanbanColumn
            title="Cancelled"
            icon={<X className="h-5 w-5 text-red-500" />}
            orders={ordersByStatus.cancelled}
            status="cancelled"
            bgColor="red"
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            formatDate={formatDate}
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
          />
        </div>
      )}
    </div>
  );
};

interface KanbanColumnProps {
  title: string;
  icon: React.ReactNode;
  orders: Order[];
  status: Order['status'];
  bgColor: string;
  onDragOver: (e: React.DragEvent) => void;
  onDragLeave: (e: React.DragEvent) => void;
  onDrop: (e: React.DragEvent, status: Order['status']) => void;
  formatDate: (date: string) => string;
  onDragStart: (e: React.DragEvent, orderId: string) => void;
  onDragEnd: (e: React.DragEvent) => void;
}

const KanbanColumn = ({
  title,
  icon,
  orders,
  status,
  bgColor,
  onDragOver,
  onDragLeave,
  onDrop,
  formatDate,
  onDragStart,
  onDragEnd
}: KanbanColumnProps) => {
  return (
    <div className="flex flex-col h-full">
      <div className={`bg-${bgColor}-50 p-3 rounded-t-lg border border-${bgColor}-100 flex items-center gap-2`}>
        {icon}
        <h3 className="font-medium text-gray-900">{title}</h3>
        <span className={`ml-auto bg-${bgColor}-100 text-${bgColor}-800 text-xs px-2 py-0.5 rounded-full`}>
          {orders.length}
        </span>
      </div>

      <div
        className={`flex-1 bg-${bgColor}-50/30 p-4 rounded-b-lg border-x border-b border-${bgColor}-100 min-h-[calc(100vh-20rem)] overflow-y-auto transition-all duration-200`}
        onDragOver={onDragOver}
        onDragLeave={onDragLeave}
        onDrop={(e) => onDrop(e, status)}
        data-status={status}
      >
        <div className="space-y-3">
          {orders.map(order => (
            <KanbanCard
              key={order.id}
              order={order}
              formatDate={formatDate}
              onDragStart={onDragStart}
              onDragEnd={onDragEnd}
            />
          ))}

          {orders.length === 0 && (
            <div className="flex flex-col items-center justify-center h-32 bg-white rounded-lg border border-dashed border-gray-300 text-center p-4">
              <AlertCircle className="h-6 w-6 text-gray-400 mb-2" />
              <p className="text-sm text-gray-500">No orders in this stage</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

interface KanbanCardProps {
  order: Order;
  formatDate: (date: string) => string;
  onDragStart: (e: React.DragEvent, orderId: string) => void;
  onDragEnd: (e: React.DragEvent) => void;
}

const KanbanCard = ({ order, formatDate, onDragStart, onDragEnd }: KanbanCardProps) => {
  return (
    <div
      className="cursor-grab hover:shadow-md transition-all duration-200 transform hover:-translate-y-1 active:cursor-grabbing"
      draggable="true"
      onDragStart={(e) => onDragStart(e, order.id)}
      onDragEnd={onDragEnd}
    >
      <Card>
        <div className="p-4">
          <div className="flex justify-between items-start mb-2">
            <Link
              to={`/orders/${order.id}`}
              className="text-primary-600 font-medium hover:underline"
              onClick={(e) => e.stopPropagation()}
            >
              {order.orderID ? `#${order.orderID}` : order.id}
            </Link>
            <PaymentStatusBadge status={order.paymentStatus} />
          </div>

          <div className="mb-3">
            <div className="font-medium text-gray-900">{order.customerName}</div>
            <div className="text-sm text-gray-500">{order.customerPhone}</div>
          </div>

          <div className="text-xs text-gray-500 mb-3">
            {formatDate(order.createdAt)}
          </div>

          <div className="border-t border-gray-100 pt-3 flex justify-between items-center">
            <div className="font-medium text-gray-900">₹{order.total.toFixed(2)}</div>
            <div className="text-xs text-gray-500">{order.items.length} items</div>
          </div>

          <div className="mt-2 flex justify-end">
            <Link to={`/orders/${order.id}`} onClick={(e) => e.stopPropagation()}>
              <Button variant="ghost" size="sm" className="text-gray-500 hover:text-primary-600">
                <FilePenLine size={14} />
              </Button>
            </Link>
          </div>
        </div>
      </Card>
    </div>
  );
};
