/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        background: '#FDF7ED',
        primary: {
          50: '#F3EBFA',
          100: '#E7D7F4',
          200: '#D0B0E9',
          300: '#B888DE',
          400: '#A161D3',
          500: '#7D47A2',
          600: '#633982',
          700: '#4A2B61',
          800: '#321C41',
          900: '#190E20',
        },
        secondary: {
          50: '#F0F7EA',
          100: '#E1EFD5',
          200: '#C4DFAB',
          300: '#A6CF82',
          400: '#8BBF59',
          500: '#7BAF4B',
          600: '#628C3C',
          700: '#4A692D',
          800: '#31461E',
          900: '#19230F',
        },
        success: {
          500: '#10B981',
          700: '#047857',
        },
        warning: {
          500: '#F59E0B',
          700: '#B45309',
        },
        danger: {
          500: '#EF4444',
          700: '#B91C1C',
        },
        info: {
          500: '#3B82F6',
          700: '#1D4ED8',
        },
      },
      fontFamily: {
        display: ['Playfair Display', 'serif'],
        sans: ['Montserrat', 'sans-serif'],
        script: ['Dancing Script', 'cursive'],
      },
      boxShadow: {
        'top': '0 -4px 6px -1px rgba(0, 0, 0, 0.1)',
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-in': 'slideIn 0.3s ease-out',
        'pulse-subtle': 'pulseSubtle 2s infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideIn: {
          '0%': { transform: 'translateY(-10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        pulseSubtle: {
          '0%, 100%': { opacity: '1' },
          '50%': { opacity: '0.85' },
        },
      },
    },
  },
  plugins: [],
};