import {
  collection,
  doc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  updateDoc,
  serverTimestamp
} from 'firebase/firestore';
import { db } from '../firebase/config';

const PRODUCTS_COLLECTION = 'Products';

// Get the next available Product ID
export const getNextProductId = async (): Promise<number> => {
  try {
    console.log('Getting next available Product ID...');
    
    // Get all products ordered by productId descending to find the highest ID
    const productsRef = collection(db, PRODUCTS_COLLECTION);
    const q = query(
      productsRef,
      orderBy('productId', 'desc'),
      limit(1)
    );
    
    const querySnapshot = await getDocs(q);
    
    if (querySnapshot.empty) {
      console.log('No products found, starting with Product ID 1');
      return 1;
    }
    
    const highestProduct = querySnapshot.docs[0];
    const highestProductId = highestProduct.data().productId;
    
    if (typeof highestProductId === 'number') {
      const nextId = highestProductId + 1;
      console.log(`Highest Product ID found: ${highestProductId}, next ID: ${nextId}`);
      return nextId;
    } else {
      console.log('No valid productId found, starting with Product ID 1');
      return 1;
    }
  } catch (error) {
    console.error('Error getting next Product ID:', error);
    // Fallback to 1 if there's an error
    return 1;
  }
};

// Check if a Product ID already exists
export const checkProductIdExists = async (productId: number, excludeDocId?: string): Promise<boolean> => {
  try {
    console.log(`Checking if Product ID ${productId} exists...`);
    
    const productsRef = collection(db, PRODUCTS_COLLECTION);
    const q = query(productsRef, where('productId', '==', productId));
    const querySnapshot = await getDocs(q);
    
    if (excludeDocId) {
      // Check if any other product (excluding the specified document) has this productId
      const hasConflict = querySnapshot.docs.some(doc => doc.id !== excludeDocId);
      console.log(`Product ID ${productId} exists in other products: ${hasConflict}`);
      return hasConflict;
    }
    
    const exists = !querySnapshot.empty;
    console.log(`Product ID ${productId} exists: ${exists}`);
    return exists;
  } catch (error) {
    console.error('Error checking Product ID existence:', error);
    return false;
  }
};

// Validate Product ID
export const validateProductId = async (
  productId: number, 
  excludeDocId?: string
): Promise<{ isValid: boolean; error?: string }> => {
  // Check if it's a positive integer
  if (!Number.isInteger(productId) || productId <= 0) {
    return {
      isValid: false,
      error: 'Product ID must be a positive integer'
    };
  }
  
  // Check if it already exists
  const exists = await checkProductIdExists(productId, excludeDocId);
  if (exists) {
    return {
      isValid: false,
      error: 'Product ID already exists. Please choose a different number.'
    };
  }
  
  return { isValid: true };
};

// Get product by Product ID
export const getProductByProductId = async (productId: number): Promise<any | null> => {
  try {
    console.log(`Getting product with Product ID: ${productId}`);
    
    const productsRef = collection(db, PRODUCTS_COLLECTION);
    const q = query(productsRef, where('productId', '==', productId));
    const querySnapshot = await getDocs(q);
    
    if (querySnapshot.empty) {
      console.log(`No product found with Product ID: ${productId}`);
      return null;
    }
    
    const doc = querySnapshot.docs[0];
    const data = doc.data();
    
    console.log(`Found product with Product ID ${productId}:`, data.name);
    return {
      id: doc.id,
      ...data
    };
  } catch (error) {
    console.error('Error getting product by Product ID:', error);
    return null;
  }
};

// Get all Product IDs in use
export const getAllProductIds = async (): Promise<number[]> => {
  try {
    console.log('Getting all Product IDs in use...');
    
    const productsRef = collection(db, PRODUCTS_COLLECTION);
    const querySnapshot = await getDocs(productsRef);
    
    const productIds: number[] = [];
    
    querySnapshot.docs.forEach(doc => {
      const data = doc.data();
      if (typeof data.productId === 'number') {
        productIds.push(data.productId);
      }
    });
    
    // Sort the IDs for easier debugging
    productIds.sort((a, b) => a - b);
    
    console.log(`Found ${productIds.length} Product IDs in use:`, productIds);
    return productIds;
  } catch (error) {
    console.error('Error getting all Product IDs:', error);
    return [];
  }
};

// Find gaps in Product ID sequence
export const findProductIdGaps = async (): Promise<number[]> => {
  try {
    const allIds = await getAllProductIds();
    
    if (allIds.length === 0) {
      return [1]; // If no products exist, suggest starting with 1
    }
    
    const gaps: number[] = [];
    
    // Check for gaps in the sequence
    for (let i = 1; i < allIds[allIds.length - 1]; i++) {
      if (!allIds.includes(i)) {
        gaps.push(i);
      }
    }
    
    console.log(`Found ${gaps.length} gaps in Product ID sequence:`, gaps);
    return gaps;
  } catch (error) {
    console.error('Error finding Product ID gaps:', error);
    return [];
  }
};

// Suggest next available Product ID (either next sequential or fill a gap)
export const suggestNextProductId = async (): Promise<number> => {
  try {
    // First check if there are any gaps to fill
    const gaps = await findProductIdGaps();
    
    if (gaps.length > 0) {
      // Return the smallest gap
      const smallestGap = Math.min(...gaps);
      console.log(`Suggesting Product ID ${smallestGap} to fill gap`);
      return smallestGap;
    }
    
    // No gaps, get the next sequential ID
    const nextId = await getNextProductId();
    console.log(`Suggesting next sequential Product ID: ${nextId}`);
    return nextId;
  } catch (error) {
    console.error('Error suggesting next Product ID:', error);
    return 1;
  }
};

// Update Product ID for an existing product
export const updateProductId = async (
  documentId: string, 
  newProductId: number
): Promise<boolean> => {
  try {
    console.log(`Updating Product ID for document ${documentId} to ${newProductId}`);
    
    // Validate the new Product ID
    const validation = await validateProductId(newProductId, documentId);
    if (!validation.isValid) {
      console.error('Product ID validation failed:', validation.error);
      return false;
    }
    
    // Update the document
    const docRef = doc(db, PRODUCTS_COLLECTION, documentId);
    await updateDoc(docRef, {
      productId: newProductId,
      updatedAt: serverTimestamp()
    });
    
    console.log(`Successfully updated Product ID to ${newProductId}`);
    return true;
  } catch (error) {
    console.error('Error updating Product ID:', error);
    return false;
  }
};
