/**
 * Migration Script: Add Product ID field to existing products
 *
 * This script adds a numeric productId field to all existing products in the Firebase Firestore database.
 * It assigns incremental Product IDs starting from 1, ensuring all variants of the same product
 * share the same productId as their parent product.
 *
 * Usage:
 * 1. Install Firebase Admin SDK: npm install firebase-admin
 * 2. Set up Firebase service account credentials
 * 3. Run: node scripts/migrate-add-product-id.js
 */

const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
// Replace with your actual service account key path
const serviceAccount = require('./serviceAccountKey.json'); // Update this path

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  // Replace 'vrisham-cad24' with your actual project ID if different
  databaseURL: 'https://vrisham-cad24-default-rtdb.firebaseio.com'
});

const db = admin.firestore();

async function migrateAddProductId() {
  console.log('Starting migration: Adding Product ID to existing products...');

  let totalProducts = 0;
  let migratedProducts = 0;
  let totalVariants = 0;
  let migratedVariants = 0;
  let errors = 0;
  let nextProductId = 1;

  try {
    // Get all products
    const productsSnapshot = await db.collection('Products').get();
    totalProducts = productsSnapshot.size;

    console.log(`Found ${totalProducts} products to migrate...`);

    // Process each product
    for (const productDoc of productsSnapshot.docs) {
      const productId = productDoc.id;
      const productData = productDoc.data();

      console.log(`\nProcessing product: ${productId} (${productData.name || 'Unnamed'})`);

      try {
        // Check if product already has productId
        if (productData.productId) {
          console.log(`  Product already has Product ID: ${productData.productId}`);

          // Update nextProductId to avoid conflicts
          if (productData.productId >= nextProductId) {
            nextProductId = productData.productId + 1;
          }

          // Still need to check variants
        } else {
          // Assign new Product ID to the main product
          console.log(`  Assigning Product ID ${nextProductId} to product ${productId}`);

          await productDoc.ref.update({
            productId: nextProductId,
            updatedAt: admin.firestore.FieldValue.serverTimestamp()
          });

          migratedProducts++;
          console.log(`  ✓ Successfully assigned Product ID ${nextProductId} to product`);
        }

        // Get current Product ID (either existing or newly assigned)
        const currentProductId = productData.productId || nextProductId;

        // Process variants for this product
        const variantsSnapshot = await db
          .collection('Products')
          .doc(productId)
          .collection('Variants')
          .get();

        if (!variantsSnapshot.empty) {
          totalVariants += variantsSnapshot.size;
          console.log(`  Found ${variantsSnapshot.size} variants for this product`);

          // Update all variants with the same productNumericId
          for (const variantDoc of variantsSnapshot.docs) {
            const variantId = variantDoc.id;
            const variantData = variantDoc.data();

            try {
              // Check if variant already has productNumericId
              if (variantData.productNumericId) {
                console.log(`    Variant ${variantId} already has productNumericId: ${variantData.productNumericId}`);
              } else {
                console.log(`    Assigning productNumericId ${currentProductId} to variant ${variantId}`);

                await variantDoc.ref.update({
                  productNumericId: currentProductId,
                  updatedAt: admin.firestore.FieldValue.serverTimestamp()
                });

                migratedVariants++;
                console.log(`    ✓ Successfully assigned productNumericId to variant ${variantId}`);
              }
            } catch (variantError) {
              console.error(`    ❌ Error updating variant ${variantId}:`, variantError);
              errors++;
            }
          }
        } else {
          console.log(`  No variants found for product ${productId}`);
        }

        // Increment nextProductId only if we assigned a new one
        if (!productData.productId) {
          nextProductId++;
        }

      } catch (productError) {
        console.error(`❌ Error processing product ${productId}:`, productError);
        errors++;
      }
    }

    // Migration summary
    console.log('\n' + '='.repeat(60));
    console.log('MIGRATION SUMMARY');
    console.log('='.repeat(60));
    console.log(`Total products processed: ${totalProducts}`);
    console.log(`Products migrated (new Product ID): ${migratedProducts}`);
    console.log(`Total variants processed: ${totalVariants}`);
    console.log(`Variants migrated (new productNumericId): ${migratedVariants}`);
    console.log(`Next available Product ID: ${nextProductId}`);
    console.log(`Errors encountered: ${errors}`);
    console.log('='.repeat(60));

    if (errors === 0) {
      console.log('✅ Migration completed successfully!');
    } else {
      console.log(`⚠️ Migration completed with ${errors} errors. Please check the logs above.`);
    }

  } catch (error) {
    console.error('❌ Fatal error during migration:', error);
    process.exit(1);
  }
}

// Dry run function to preview changes without making them
async function dryRunMigration() {
  console.log('DRY RUN: Preview of Product ID migration...');

  let totalProducts = 0;
  let productsToMigrate = 0;
  let totalVariants = 0;
  let variantsToMigrate = 0;
  let nextProductId = 1;
  let existingProductIds = [];

  try {
    const productsSnapshot = await db.collection('Products').get();
    totalProducts = productsSnapshot.size;

    console.log(`Found ${totalProducts} products to check...`);

    for (const productDoc of productsSnapshot.docs) {
      const productData = productDoc.data();

      if (productData.productId) {
        existingProductIds.push(productData.productId);
        if (productData.productId >= nextProductId) {
          nextProductId = productData.productId + 1;
        }
      } else {
        productsToMigrate++;
        console.log(`  Would assign Product ID ${nextProductId} to: ${productData.name || 'Unnamed'} (${productDoc.id})`);

        // Check variants
        const variantsSnapshot = await db
          .collection('Products')
          .doc(productDoc.id)
          .collection('Variants')
          .get();

        totalVariants += variantsSnapshot.size;

        for (const variantDoc of variantsSnapshot.docs) {
          const variantData = variantDoc.data();
          if (!variantData.productNumericId) {
            variantsToMigrate++;
            console.log(`    Would assign productNumericId ${nextProductId} to variant: ${variantDoc.id}`);
          }
        }

        nextProductId++;
      }
    }

    console.log('\n' + '='.repeat(60));
    console.log('DRY RUN SUMMARY');
    console.log('='.repeat(60));
    console.log(`Total products: ${totalProducts}`);
    console.log(`Products to migrate: ${productsToMigrate}`);
    console.log(`Products with existing Product ID: ${existingProductIds.length}`);
    console.log(`Total variants: ${totalVariants}`);
    console.log(`Variants to migrate: ${variantsToMigrate}`);
    console.log(`Next available Product ID: ${nextProductId}`);
    console.log(`Existing Product IDs: [${existingProductIds.sort((a, b) => a - b).join(', ')}]`);
    console.log('='.repeat(60));

  } catch (error) {
    console.error('❌ Error during dry run:', error);
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);

  if (args.includes('--dry-run')) {
    await dryRunMigration();
  } else if (args.includes('--execute')) {
    await migrateAddProductId();
  } else {
    console.log('Usage:');
    console.log('  node migrate-add-product-id.js --dry-run    # Preview changes');
    console.log('  node migrate-add-product-id.js --execute    # Execute migration');
    console.log('');
    console.log('⚠️  IMPORTANT: Always run --dry-run first to preview changes!');
  }

  process.exit(0);
}

main().catch(console.error);
