{"name": "project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "setup-admin": "npx tsx src/firebase/setupAdmin.ts", "create-sample-farmer": "npx tsx src/scripts/createSampleFarmer.ts", "test-coupons": "npx tsx src/scripts/testCoupons.ts"}, "dependencies": {"@headlessui/react": "^1.7.18", "chart.js": "^4.4.1", "class-variance-authority": "^0.7.1", "firebase": "^11.7.1", "lucide-react": "latest", "papaparse": "^5.4.1", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.22.1"}, "devDependencies": {"@types/papaparse": "^5.3.14", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "typescript": "^5.2.2", "vite": "^5.0.8"}}