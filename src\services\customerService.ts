
import { db, auth } from '../firebase/config';
import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  serverTimestamp,
  limit,
  startAfter,
  QueryDocumentSnapshot,
  getCountFromServer
} from 'firebase/firestore';
import { Customer, Address } from '../types';
import { checkAuth } from './authService';

// Collection name should be "Users" instead of a custom collection
const COLLECTION_NAME = 'Users';

// Map Firestore document to Customer type
const mapDocToCustomer = (doc: any): Customer => {
  try {
    const data = doc.data() || {};

    // Log the raw data for debugging
    console.log(`Mapping document ID: ${doc.id}, data:`, JSON.stringify(data, null, 2));

    // Check if this is likely an admin user
    const role = data.role || '';
    if (role === 'admin' || role === 'staff') {
      console.log(`Skipping admin/staff user: ${doc.id}`);
    }

    // Extract addresses from listOfAddress array
    let addresses: Address[] = [];
    try {
      const addressList = data.listOfAddress || [];
      if (Array.isArray(addressList)) {
        addresses = addressList.map((addr: any, index: number) => {
          try {
            return {
              id: `addr-${addr.addressID || index}`,
              type: (addr.addressName || '').toLowerCase().includes('home') ? 'home' :
                    (addr.addressName || '').toLowerCase().includes('office') ? 'office' : 'other',
              line1: addr.addressLines || '',
              line2: addr.landmark || '',
              city: addr.city || '',
              pincode: addr.pincode?.toString() || '',
              isDefault: index === 0, // First address is default
              phoneNumber: addr.phoneNumber || addr.phone_number || ''
            };
          } catch (addrError) {
            console.error(`Error mapping address at index ${index}:`, addrError);
            return {
              id: `addr-${index}`,
              type: 'other',
              line1: '',
              line2: '',
              city: '',
              pincode: '',
              isDefault: index === 0,
              phoneNumber: ''
            };
          }
        });
      }
    } catch (addressError) {
      console.error('Error mapping addresses:', addressError);
    }

    // Extract name from various possible fields
    const name = data.displayName || data.display_name || data.name || data.username || 'Unknown Customer';

    // Extract phone from various possible fields
    // First try to get from top-level fields
    let phone = data.phoneNumber || data.phone_number || data.phone || '';

    // If no phone found and we have addresses, try to get from the first address
    if (!phone && Array.isArray(data.listOfAddress) && data.listOfAddress.length > 0) {
      phone = data.listOfAddress[0].phoneNumber || data.listOfAddress[0].phone_number || '';
    }

    // Extract email
    const email = data.email || '';

    // Extract order stats
    const totalOrders = typeof data.totalOrders === 'number' ? data.totalOrders : 0;
    const totalSpent = typeof data.totalSpent === 'number' ? data.totalSpent : 0;
    const lastOrderDate = data.lastOrderDate || null;

    // Extract creation date
    let createdAtString = new Date().toISOString();
    try {
      if (data.createdTime) {
        if (typeof data.createdTime.toDate === 'function') {
          createdAtString = data.createdTime.toDate().toISOString();
        } else if (data.createdTime.seconds) {
          createdAtString = new Date(data.createdTime.seconds * 1000).toISOString();
        }
      } else if (data.created_time) {
        if (typeof data.created_time.toDate === 'function') {
          createdAtString = data.created_time.toDate().toISOString();
        } else if (data.created_time.seconds) {
          createdAtString = new Date(data.created_time.seconds * 1000).toISOString();
        }
      }
    } catch (dateError) {
      console.error('Error parsing date:', dateError);
    }

    // Determine status
    let status: 'active' | 'inactive' = 'active';
    if (data.isDeactivated === true) {
      status = 'inactive';
    }

    // Create a customer object with all fields properly mapped
    const customer: Customer = {
      id: doc.id,
      name: name,
      phone: phone,
      addresses: addresses,
      totalOrders: totalOrders,
      totalSpent: totalSpent,
      lastOrderDate: lastOrderDate,
      createdAt: createdAtString,
      status: status
    };

    // Add email if it exists
    if (email) {
      customer.email = email;
    }

    return customer;
  } catch (error) {
    console.error(`Error mapping document ${doc.id} to customer:`, error);
    // Return a minimal valid customer object
    return {
      id: doc.id,
      name: 'Error Customer',
      phone: '',
      addresses: [],
      totalOrders: 0,
      totalSpent: 0,
      lastOrderDate: null,
      createdAt: new Date().toISOString(),
      status: 'active'
    };
  }
};

// Map Customer type to Firestore document
const mapCustomerToDoc = (customer: Omit<Customer, 'id' | 'createdAt'>): any => {
  // Map addresses to listOfAddress format
  const listOfAddress = customer.addresses.map((addr, index) => {
    // Create a clean address object with no undefined values
    const addressObj: any = {
      addressID: parseInt(addr.id?.replace('addr-', '') || '') || index + 1,
      addressName: addr.type === 'home' ? customer.name : (addr.type?.charAt(0).toUpperCase() + addr.type?.slice(1) || 'Other'),
      addressLines: addr.line1 || '',
      phoneNumber: addr.phoneNumber || customer.phone || '',
      phone_number: addr.phoneNumber || customer.phone || '' // Add both field names for compatibility
    };

    // Only add optional fields if they exist and are not undefined
    if (addr.line2) addressObj.landmark = addr.line2;
    if (addr.pincode) addressObj.pincode = parseInt(addr.pincode) || 0;

    // Note: city and state fields are not saved to the database as they're not in the schema

    return addressObj;
  });

  // Create keywords array for search
  const generateKeywords = (customer) => {
    const keywords = [];

    // Add name keywords
    if (customer.name) {
      // Add full name in lowercase
      const fullName = customer.name.toLowerCase();
      keywords.push(fullName);

      // Add each word in the name separately
      const nameParts = fullName.split(/\s+/);
      nameParts.forEach(part => {
        if (part.length > 0) {
          keywords.push(part);

          // Add progressive parts of each name part
          let partial = '';
          for (const char of part) {
            partial += char;
            if (partial.length > 1) { // Only add if at least 2 chars
              keywords.push(partial);
            }
          }
        }
      });
    }

    // Add phone keywords
    if (customer.phone) {
      // Add full phone number
      keywords.push(customer.phone);

      // Add progressive parts of the phone number
      let partialPhone = '';
      for (const char of customer.phone) {
        partialPhone += char;
        keywords.push(partialPhone);
      }

      // Add without the + prefix if it exists
      if (customer.phone.startsWith('+')) {
        const withoutPlus = customer.phone.substring(1);
        keywords.push(withoutPlus);

        // Add progressive parts without the + prefix
        partialPhone = '';
        for (const char of withoutPlus) {
          partialPhone += char;
          keywords.push(partialPhone);
        }
      }
    }

    // Add email keywords if available
    if (customer.email) {
      keywords.push(customer.email.toLowerCase());

      // Add parts before @ in email
      const emailParts = customer.email.toLowerCase().split('@');
      if (emailParts.length > 0) {
        keywords.push(emailParts[0]);
      }
    }

    // Remove duplicates
    return [...new Set(keywords)];
  };

  // Create a clean document object with no undefined values
  const docObj: any = {
    displayName: customer.name || '',
    display_name: customer.name || '', // Add both field names for compatibility
    phoneNumber: customer.phone || '',
    phone_number: customer.phone || '', // Add both field names for compatibility
    listOfAddress: listOfAddress,
    role: 'customer',
    isDeactivated: customer.status === 'inactive' || false,
    isNewCustomer: true,
    keywords: generateKeywords(customer),
    uid: '', // Will be set to document ID
    totalOrders: customer.totalOrders || 0,
    totalSpent: customer.totalSpent || 0
  };

  // Only add optional fields if they exist and are not undefined
  if (customer.email) docObj.email = customer.email;
  if (customer.lastOrderDate) docObj.lastOrderDate = customer.lastOrderDate;

  return docObj;
};

// Default page size for pagination
export const DEFAULT_PAGE_SIZE = 30;

// Get total customer count
export const getCustomerCount = async (): Promise<number> => {
  try {
    // Check if user is authenticated
    const isAuthenticated = await checkAuth();
    if (!isAuthenticated) {
      return 0;
    }

    console.log('Getting customer count');
    const customersRef = collection(db, COLLECTION_NAME);

    try {
      // First try with role filter
      const q = query(
        customersRef,
        where('role', '==', 'customer')
      );

      const countSnapshot = await getCountFromServer(q);
      const count = countSnapshot.data().count;
      console.log(`Total customer count with role filter: ${count}`);

      return count;
    } catch (roleError) {
      console.error('Error getting count with role filter:', roleError);

      // Fallback to counting all documents
      console.log('Falling back to counting all documents');
      const countSnapshot = await getCountFromServer(customersRef);
      const totalCount = countSnapshot.data().count;

      // Estimate customer count (exclude admin users)
      const estimatedCustomerCount = Math.max(0, totalCount - 5); // Assume at most 5 admin/staff users
      console.log(`Estimated customer count: ${estimatedCustomerCount} (total: ${totalCount})`);

      return estimatedCustomerCount;
    }
  } catch (error) {
    console.error('Error getting customer count:', error);
    return 0;
  }
};

// Get customers with pagination
export const getCustomersPage = async (
  pageSize: number = DEFAULT_PAGE_SIZE,
  lastDoc: QueryDocumentSnapshot | null = null
): Promise<{ customers: Customer[], lastDoc: QueryDocumentSnapshot | null }> => {
  try {
    // Check if user is authenticated
    const isAuthenticated = await checkAuth();
    if (!isAuthenticated) {
      return { customers: [], lastDoc: null };
    }

    console.log(`Fetching customers page with size ${pageSize}, ${lastDoc ? 'with' : 'without'} cursor`);
    const customersRef = collection(db, COLLECTION_NAME);

    // Build the query with pagination
    let querySnapshot;

    try {
      // First try with created_time field
      let q;
      if (lastDoc) {
        // For subsequent pages
        q = query(
          customersRef,
          where('role', '==', 'customer'),
          orderBy('created_time', 'desc'),
          startAfter(lastDoc),
          limit(pageSize)
        );
      } else {
        // For the first page
        q = query(
          customersRef,
          where('role', '==', 'customer'),
          orderBy('created_time', 'desc'),
          limit(pageSize)
        );
      }

      querySnapshot = await getDocs(q);
      console.log(`Retrieved ${querySnapshot.docs.length} customers using created_time ordering`);
    } catch (orderError) {
      console.error('Error with created_time ordering:', orderError);

      // Fallback to simpler query without ordering if index doesn't exist
      console.log('Falling back to simpler query without complex ordering');
      try {
        let q;
        if (lastDoc) {
          q = query(
            customersRef,
            where('role', '==', 'customer'),
            startAfter(lastDoc),
            limit(pageSize)
          );
        } else {
          q = query(
            customersRef,
            where('role', '==', 'customer'),
            limit(pageSize)
          );
        }

        querySnapshot = await getDocs(q);
        console.log(`Retrieved ${querySnapshot.docs.length} customers using fallback query`);
      } catch (fallbackError) {
        console.error('Error with fallback query:', fallbackError);

        // Last resort: just get any customers without filtering by role
        console.log('Using last resort query without role filter');
        const q = query(
          customersRef,
          limit(pageSize)
        );

        querySnapshot = await getDocs(q);
        console.log(`Retrieved ${querySnapshot.docs.length} customers using last resort query`);
      }
    }
    console.log(`Retrieved ${querySnapshot.docs.length} customers for this page`);

    // Map documents to Customer type
    const customers = querySnapshot.docs
      .map(doc => mapDocToCustomer(doc))
      .filter(customer => customer && (customer.name || customer.email || customer.phone)) as Customer[];

    // Get the last document for pagination
    const newLastDoc = querySnapshot.docs.length > 0
      ? querySnapshot.docs[querySnapshot.docs.length - 1]
      : null;

    return {
      customers,
      lastDoc: newLastDoc
    };
  } catch (error) {
    console.error('Error getting customers page:', error);
    return { customers: [], lastDoc: null };
  }
};

// Legacy function for backward compatibility
// This should be gradually phased out in favor of paginated queries
export const getAllCustomers = async (): Promise<Customer[]> => {
  try {
    // Check if user is authenticated
    const isAuthenticated = await checkAuth();
    if (!isAuthenticated) {
      return [];
    }

    console.log('WARNING: Using getAllCustomers which fetches all customers at once. Consider using pagination instead.');
    const customersRef = collection(db, COLLECTION_NAME);

    try {
      // Create a query that limits to only customer role
      const q = query(
        customersRef,
        where('role', '==', 'customer'),
        limit(100) // Add a reasonable limit to prevent excessive reads
      );

      const querySnapshot = await getDocs(q);
      console.log(`Found ${querySnapshot.docs.length} customers in collection (limited to 100)`);

      // Map documents to Customer type
      const customers = querySnapshot.docs
        .map(doc => mapDocToCustomer(doc))
        .filter(customer => customer && (customer.name || customer.email || customer.phone)) as Customer[];

      return customers;
    } catch (innerError) {
      console.error('Error with query:', innerError);
      throw innerError;
    }
  } catch (error) {
    console.error('Error getting customers:', error);
    return [];
  }
};

// Get customer by ID
export const getCustomerById = async (id: string): Promise<Customer | null> => {
  try {
    // Check if user is authenticated
    const isAuthenticated = await checkAuth();
    if (!isAuthenticated) {
      return null;
    }

    console.log(`Fetching customer with ID: ${id}`);
    const docRef = doc(db, COLLECTION_NAME, id);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      const data = docSnap.data();
      console.log('Customer document data:', data);
      console.log('Customer document fields:', Object.keys(data));

      // Log specific fields we're interested in
      console.log('displayName:', data.displayName);
      console.log('phoneNumber:', data.phoneNumber);
      console.log('listOfAddress:', data.listOfAddress);
      console.log('isDeactivated:', data.isDeactivated);
      console.log('totalOrders:', data.totalOrders);
      console.log('totalSpent:', data.totalSpent);
      console.log('lastOrderDate:', data.lastOrderDate);

      const customer = mapDocToCustomer(docSnap);
      console.log('Mapped customer:', customer);

      return customer;
    } else {
      console.log(`No customer found with ID: ${id}`);
      return null;
    }
  } catch (error) {
    console.error('Error getting customer:', error);
    return null;
  }
};

// Add a new customer
export const addCustomer = async (
  customer: Omit<Customer, 'id' | 'createdAt'>
): Promise<string | null> => {
  try {
    // Check if user is authenticated
    if (!checkAuth()) {
      return null;
    }

    // Map customer to document format with no undefined values
    const mappedData = mapCustomerToDoc(customer);

    // Ensure we set the role to 'customer' and initialize order-related fields
    const docData = {
      ...mappedData,
      role: 'customer',
      totalOrders: 0,
      totalSpent: 0,
      lastOrderDate: null,
      createdTime: serverTimestamp(),
      created_time: serverTimestamp(), // Add both field names for compatibility
    };

    console.log('Adding new customer with data:', docData);

    const docRef = await addDoc(collection(db, COLLECTION_NAME), docData);
    return docRef.id;
  } catch (error) {
    console.error('Error adding customer:', error);
    return null;
  }
};

// Update a customer
export const updateCustomer = async (
  id: string,
  updates: Partial<Customer>
): Promise<boolean> => {
  try {
    // Check if user is authenticated
    if (!checkAuth()) {
      return false;
    }

    const docRef = doc(db, COLLECTION_NAME, id);
    const docSnap = await getDoc(docRef);

    if (!docSnap.exists()) {
      console.error('Customer not found');
      return false;
    }

    // Get the current data
    const currentData = docSnap.data();

    // Prepare update data
    const updateData: any = {};

    if (updates.name !== undefined) {
      updateData.displayName = updates.name;
      updateData.display_name = updates.name; // Add both field names for compatibility
    }
    if (updates.email !== undefined) updateData.email = updates.email;
    if (updates.phone !== undefined) {
      updateData.phoneNumber = updates.phone;
      updateData.phone_number = updates.phone; // Add both field names for compatibility

      // Update keywords for phone search
      const phoneKeywords = [];
      // Add full phone number
      phoneKeywords.push(updates.phone);

      // Add progressive parts of the phone number
      let partialPhone = '';
      for (const char of updates.phone) {
        partialPhone += char;
        phoneKeywords.push(partialPhone);
      }

      // Add without the + prefix if it exists
      if (updates.phone.startsWith('+')) {
        const withoutPlus = updates.phone.substring(1);
        phoneKeywords.push(withoutPlus);

        // Add progressive parts without the + prefix
        partialPhone = '';
        for (const char of withoutPlus) {
          partialPhone += char;
          phoneKeywords.push(partialPhone);
        }
      }

      // Add name to keywords
      const nameKeyword = updates.name?.toLowerCase() || currentData.displayName?.toLowerCase() || '';
      if (nameKeyword) {
        updateData.keywords = [nameKeyword, ...phoneKeywords];
      } else {
        updateData.keywords = phoneKeywords;
      }
    }
    if (updates.status !== undefined) updateData.isDeactivated = updates.status === 'inactive';

    // Handle address updates
    if (updates.addresses) {
      updateData.listOfAddress = updates.addresses.map((addr, index) => {
        const name = updates.name || currentData.displayName || currentData.display_name || '';
        return {
          addressID: parseInt(addr.id.replace('addr-', '')) || index + 1,
          addressName: addr.type === 'home' ? name : addr.type.charAt(0).toUpperCase() + addr.type.slice(1),
          addressLines: addr.line1,
          landmark: addr.line2,
          city: addr.city,
          // Note: state field is not saved to the database as it's not in the schema
          pincode: parseInt(addr.pincode) || 0,
          phoneNumber: addr.phoneNumber || updates.phone || currentData.phoneNumber || currentData.phone_number,
          phone_number: addr.phoneNumber || updates.phone || currentData.phoneNumber || currentData.phone_number
        };
      });
    }

    await updateDoc(docRef, updateData);
    return true;
  } catch (error) {
    console.error('Error updating customer:', error);
    return false;
  }
};

// Delete a customer
export const deleteCustomer = async (id: string): Promise<boolean> => {
  try {
    // Check if user is authenticated
    if (!checkAuth()) {
      return false;
    }

    const docRef = doc(db, COLLECTION_NAME, id);
    await deleteDoc(docRef);
    return true;
  } catch (error) {
    console.error('Error deleting customer:', error);
    return false;
  }
};

// Search customers with pagination
export const searchCustomersPage = async (
  searchTerm: string,
  pageSize: number = DEFAULT_PAGE_SIZE,
  lastDoc: QueryDocumentSnapshot | null = null
): Promise<{ customers: Customer[], lastDoc: QueryDocumentSnapshot | null }> => {
  try {
    // Check if user is authenticated
    const isAuthenticated = await checkAuth();
    if (!isAuthenticated) {
      return { customers: [], lastDoc: null };
    }

    console.log(`Searching customers with term "${searchTerm}", page size ${pageSize}`);
    const customersRef = collection(db, COLLECTION_NAME);

    // If search term is empty, just get customers with pagination
    if (!searchTerm.trim()) {
      return getCustomersPage(pageSize, lastDoc);
    }

    // For search, we need to use a different approach since Firestore doesn't support
    // full text search. We'll try multiple approaches with fallbacks.
    let querySnapshot;

    try {
      // First try with keywords array-contains and created_time ordering
      let q;
      if (lastDoc) {
        q = query(
          customersRef,
          where('role', '==', 'customer'),
          where('keywords', 'array-contains', searchTerm.toLowerCase()),
          orderBy('created_time', 'desc'),
          startAfter(lastDoc),
          limit(pageSize)
        );
      } else {
        q = query(
          customersRef,
          where('role', '==', 'customer'),
          where('keywords', 'array-contains', searchTerm.toLowerCase()),
          orderBy('created_time', 'desc'),
          limit(pageSize)
        );
      }

      querySnapshot = await getDocs(q);
      console.log(`Found ${querySnapshot.docs.length} customers with keywords search`);
    } catch (keywordsError) {
      console.error('Error with keywords search:', keywordsError);

      // Fallback to displayName field search without ordering
      console.log('Falling back to displayName field search');
      try {
        let q;
        if (lastDoc) {
          q = query(
            customersRef,
            where('role', '==', 'customer'),
            where('displayName', '>=', searchTerm.toLowerCase()),
            where('displayName', '<=', searchTerm.toLowerCase() + '\uf8ff'),
            startAfter(lastDoc),
            limit(pageSize)
          );
        } else {
          q = query(
            customersRef,
            where('role', '==', 'customer'),
            where('displayName', '>=', searchTerm.toLowerCase()),
            where('displayName', '<=', searchTerm.toLowerCase() + '\uf8ff'),
            limit(pageSize)
          );
        }

        querySnapshot = await getDocs(q);
        console.log(`Found ${querySnapshot.docs.length} customers with displayName search`);
      } catch (displayNameError) {
        console.error('Error with displayName search:', displayNameError);

        // Last resort: get all customers and filter client-side
        console.log('Using client-side filtering as last resort');
        const { customers } = await getCustomersPage(100, null);

        const filteredCustomers = customers.filter(customer =>
          customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (customer.email && customer.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
          customer.phone.includes(searchTerm)
        ).slice(0, pageSize);

        return {
          customers: filteredCustomers,
          lastDoc: null // Can't use server-side pagination with client filtering
        };
      }
    }

    console.log(`Found ${querySnapshot.docs.length} customers matching search term`);

    // Map documents to Customer type
    const customers = querySnapshot.docs
      .map(doc => mapDocToCustomer(doc))
      .filter(customer => customer && (customer.name || customer.email || customer.phone)) as Customer[];

    // Get the last document for pagination
    const newLastDoc = querySnapshot.docs.length > 0
      ? querySnapshot.docs[querySnapshot.docs.length - 1]
      : null;

    return {
      customers,
      lastDoc: newLastDoc
    };
  } catch (error) {
    console.error('Error searching customers:', error);

    // Final fallback if all else fails
    try {
      console.log('Using final fallback with simple query');
      const simpleQuery = query(
        collection(db, COLLECTION_NAME),
        limit(pageSize)
      );

      const querySnapshot = await getDocs(simpleQuery);

      // Filter client-side
      const customers = querySnapshot.docs
        .map(doc => {
          try {
            return mapDocToCustomer(doc);
          } catch (mapError) {
            console.error('Error mapping document to customer:', mapError);
            return null;
          }
        })
        .filter(customer =>
          customer &&
          (customer.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (customer.email && customer.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
          customer.phone?.includes(searchTerm))
        ) as Customer[];

      return {
        customers: customers.slice(0, pageSize),
        lastDoc: null
      };
    } catch (finalError) {
      console.error('Final fallback failed:', finalError);
      return { customers: [], lastDoc: null };
    }
  }
};

// Search all customers (no pagination, used for global search)
export const searchAllCustomers = async (searchTerm: string): Promise<Customer[]> => {
  try {
    // Check if user is authenticated
    const isAuthenticated = await checkAuth();
    if (!isAuthenticated) {
      return [];
    }

    console.log(`Searching ALL customers with term "${searchTerm}"`);
    const customersRef = collection(db, COLLECTION_NAME);

    // If search term is empty, return empty array
    if (!searchTerm.trim()) {
      return [];
    }

    // Store all found customers to combine results from different search methods
    let allFoundCustomers: Customer[] = [];
    const searchTermLower = searchTerm.toLowerCase();

    try {
      // First try with keywords array-contains
      console.log('Trying keywords search...');
      const keywordsQuery = query(
        customersRef,
        where('role', '==', 'customer'),
        where('keywords', 'array-contains', searchTermLower),
        // No limit - get all matching customers
      );

      const keywordsSnapshot = await getDocs(keywordsQuery);
      console.log(`Found ${keywordsSnapshot.docs.length} customers with keywords search`);

      if (keywordsSnapshot.docs.length > 0) {
        // Map documents to Customer type
        const keywordsCustomers = keywordsSnapshot.docs
          .map(doc => mapDocToCustomer(doc))
          .filter(customer => customer && (customer.name || customer.email || customer.phone)) as Customer[];

        allFoundCustomers = [...keywordsCustomers];
        console.log(`Added ${keywordsCustomers.length} customers from keywords search`);
      }
    } catch (keywordsError) {
      console.error('Error with keywords search:', keywordsError);
    }

    try {
      // Try with displayName field search
      console.log('Trying displayName search...');
      const displayNameQuery = query(
        customersRef,
        where('role', '==', 'customer'),
        where('displayName', '>=', searchTermLower),
        where('displayName', '<=', searchTermLower + '\uf8ff'),
        // No limit - get all matching customers
      );

      const displayNameSnapshot = await getDocs(displayNameQuery);
      console.log(`Found ${displayNameSnapshot.docs.length} customers with displayName search`);

      if (displayNameSnapshot.docs.length > 0) {
        // Map documents to Customer type
        const displayNameCustomers = displayNameSnapshot.docs
          .map(doc => mapDocToCustomer(doc))
          .filter(customer => customer && (customer.name || customer.email || customer.phone)) as Customer[];

        // Add any new customers not already found in keywords search
        const newCustomers = displayNameCustomers.filter(
          dnCustomer => !allFoundCustomers.some(kCustomer => kCustomer.id === dnCustomer.id)
        );

        allFoundCustomers = [...allFoundCustomers, ...newCustomers];
        console.log(`Added ${newCustomers.length} additional customers from displayName search`);
      }
    } catch (displayNameError) {
      console.error('Error with displayName search:', displayNameError);
    }

    // Always do client-side filtering as a final step to ensure we catch all matches
    console.log('Performing client-side filtering to ensure all matches are found...');

    try {
      // Get all customers (up to 1000)
      const allCustomersQuery = query(
        customersRef,
        where('role', '==', 'customer'),
        limit(1000) // Limit to 1000 to prevent excessive reads
      );

      const allCustomersSnapshot = await getDocs(allCustomersQuery);
      console.log(`Retrieved ${allCustomersSnapshot.docs.length} customers for client-side filtering`);

      // Map and filter client-side
      const clientFilteredCustomers = allCustomersSnapshot.docs
        .map(doc => {
          try {
            return mapDocToCustomer(doc);
          } catch (mapError) {
            console.error('Error mapping document to customer:', mapError);
            return null;
          }
        })
        .filter(customer => {
          if (!customer) return false;

          // Check name (full and partial matches)
          if (customer.name && customer.name.toLowerCase().includes(searchTermLower)) {
            return true;
          }

          // Check email
          if (customer.email && customer.email.toLowerCase().includes(searchTermLower)) {
            return true;
          }

          // Check phone
          if (customer.phone && customer.phone.includes(searchTerm)) {
            return true;
          }

          // Check individual words in name
          if (customer.name) {
            const nameParts = customer.name.toLowerCase().split(/\s+/);
            if (nameParts.some(part => part.includes(searchTermLower))) {
              return true;
            }
          }

          return false;
        }) as Customer[];

      // Add any new customers not already found in previous searches
      const newClientFilteredCustomers = clientFilteredCustomers.filter(
        cfCustomer => !allFoundCustomers.some(existingCustomer => existingCustomer.id === cfCustomer.id)
      );

      allFoundCustomers = [...allFoundCustomers, ...newClientFilteredCustomers];
      console.log(`Added ${newClientFilteredCustomers.length} additional customers from client-side filtering`);
    } catch (clientFilterError) {
      console.error('Error with client-side filtering:', clientFilterError);

      // If we have no results at all and client-side filtering failed, try one last approach
      if (allFoundCustomers.length === 0) {
        console.log('No customers found and client-side filtering failed. Trying final fallback...');

        try {
          // Get all customers without any filtering
          const finalFallbackQuery = query(
            customersRef,
            limit(1000)
          );

          const finalSnapshot = await getDocs(finalFallbackQuery);
          console.log(`Retrieved ${finalSnapshot.docs.length} customers for final fallback filtering`);

          // Do basic filtering
          allFoundCustomers = finalSnapshot.docs
            .map(doc => {
              try {
                return { id: doc.id, ...doc.data() } as Customer;
              } catch (e) {
                return null;
              }
            })
            .filter(customer => {
              if (!customer || !customer.name) return false;
              return customer.name.toLowerCase().includes(searchTermLower);
            }) as Customer[];

          console.log(`Found ${allFoundCustomers.length} customers with final fallback`);
        } catch (finalError) {
          console.error('Final fallback failed:', finalError);
        }
      }
    }

    console.log(`Total unique customers found across all search methods: ${allFoundCustomers.length}`);
    return allFoundCustomers;
  } catch (error) {
    console.error('Error searching all customers:', error);
    return [];
  }
};

// Legacy search function for backward compatibility
export const searchCustomers = async (searchTerm: string): Promise<Customer[]> => {
  try {
    // Check if user is authenticated
    if (!checkAuth()) {
      return [];
    }

    console.log('Using searchAllCustomers to search across all customers');

    // Use the new function that searches all customers
    return await searchAllCustomers(searchTerm);
  } catch (error) {
    console.error('Error searching customers:', error);
    return [];
  }
};
















