import { useState } from 'react';
import { BrowserRouter, Navigate, Route, Routes } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { ProductProvider } from './contexts/ProductContext';
import { CustomerProvider } from './contexts/CustomerContext';
import { AdminLayout } from './components/layout/AdminLayout';
import { Login } from './pages/auth/Login';
import { Dashboard } from './pages/dashboard/Dashboard';
import { ProductsList } from './pages/products/ProductsList';
import { ProductDetail } from './pages/products/ProductDetail';
import { Categories } from './pages/products/Categories';
import { VariationTypes } from './pages/products/VariationTypes';
import { OrderDetail } from './pages/orders/OrderDetail';
import { OrdersKanban } from './pages/orders/OrdersKanban';
import { CustomersList } from './pages/customers/CustomersList';
import { CustomerDetail } from './pages/customers/CustomerDetail';
import { ReturnsList } from './pages/returns/ReturnsList';
import { ReturnDetail } from './pages/returns/ReturnDetail';
import { FarmersList } from './pages/farmers/FarmersList';
import { FarmerDetail } from './pages/farmers/FarmerDetail';

import { Settings } from './pages/settings/Settings';
import { POS } from './pages/pos/POS';
import { NotFound } from './pages/NotFound';
import { ProtectedRoute } from './components/auth/ProtectedRoute';

function App() {
  const [isDarkMode, setIsDarkMode] = useState(false);

  const toggleDarkMode = () => {
    setIsDarkMode(!isDarkMode);
  };

  return (
    <div className={isDarkMode ? 'dark' : ''}>
      <AuthProvider>
        <ProductProvider>
          <CustomerProvider>
            <BrowserRouter>
            <Routes>
              <Route path="/" element={<Navigate to="/dashboard" replace />} />
              <Route path="/login" element={<Login />} />

              <Route path="/" element={
                <ProtectedRoute>
                  <AdminLayout toggleDarkMode={toggleDarkMode} isDarkMode={isDarkMode} />
                </ProtectedRoute>
              }>
                <Route path="dashboard" element={<Dashboard />} />

                <Route path="products">
                  <Route index element={<ProductsList />} />
                  <Route path=":id" element={<ProductDetail />} />
                  <Route path="categories" element={<Categories />} />
                  <Route path="variation-types" element={<VariationTypes />} />
                </Route>

                <Route path="pos" element={<POS />} />

                <Route path="orders">
                  <Route index element={<OrdersKanban />} />
                  <Route path=":id" element={<OrderDetail />} />
                </Route>

                <Route path="customers">
                  <Route index element={<CustomersList />} />
                  <Route path=":id" element={<CustomerDetail />} />
                </Route>

                <Route path="returns">
                  <Route index element={<ReturnsList />} />
                  <Route path=":id" element={<ReturnDetail />} />
                </Route>

                <Route path="farmers">
                  <Route index element={<FarmersList />} />
                  <Route path=":id" element={<FarmerDetail />} />
                </Route>

                <Route path="settings" element={<Settings />} />
              </Route>

              <Route path="*" element={<NotFound />} />
            </Routes>
            </BrowserRouter>
          </CustomerProvider>
        </ProductProvider>
      </AuthProvider>
    </div>
  );
}

export default App;
