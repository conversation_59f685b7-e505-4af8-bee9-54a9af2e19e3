@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /* Improved input styling */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  input[type="tel"],
  input[type="url"],
  input[type="search"],
  input[type="date"],
  textarea,
  select {
    @apply border-2 border-gray-300 rounded-md shadow-sm;
  }

  /* Focus states */
  input:focus,
  textarea:focus,
  select:focus {
    @apply outline-none ring-2 ring-primary-500 border-primary-500;
  }

  /* Placeholder styling */
  ::placeholder {
    @apply text-gray-400;
  }

  /* Label styling */
  label {
    @apply font-medium text-gray-700;
  }

  /* Hide number input spinners and ensure proper spacing */
  input[type="number"]::-webkit-outer-spin-button,
  input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
    margin: 0 !important;
    display: none !important;
    width: 0 !important;
    height: 0 !important;
  }

  /* Firefox */
  input[type="number"] {
    -moz-appearance: textfield !important;
  }

  /* Ensure number inputs have proper padding and no overflow */
  input[type="number"] {
    padding-right: 24px !important;
    box-sizing: border-box !important;
    width: 100% !important;
    text-overflow: clip !important;
    overflow: visible !important;
    min-width: 0 !important;
  }

  /* Special handling for number inputs with icons */
  .relative input[type="number"] {
    padding-right: 30px !important;
  }

  /* Additional fix for webkit browsers */
  input[type="number"]::-webkit-textfield-decoration-container {
    visibility: hidden !important;
  }

  /* Force text to be visible in number inputs */
  input[type="number"] {
    text-align: left !important;
    direction: ltr !important;
  }
}

@layer utilities {
  /* Hide scrollbar for Chrome, Safari and Opera */
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }

  /* Text truncation */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  /* Custom animations */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
}
