import { useState, useEffect, useRef, useCallback, memo } from 'react';
import { useLocation } from 'react-router-dom';
import {
  Search,
  ShoppingCart,
  Plus,
  Minus,
  Trash2,
  CreditCard,
  Wallet,
  User,
  MapPin,
  X,
  Tag,
  ChevronLeft,
  ChevronRight,
  Layers,
  ChevronsLeft,
  ChevronsRight,
  RefreshCw,
  Package
} from 'lucide-react';
import { Card } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { Badge } from '../../components/ui/Badge';
import { AddCustomerForm } from '../../components/customers/AddCustomerForm';
import { OrderConfirmation } from '../../components/pos/OrderConfirmation';
import { ProductVariantSelector } from '../../components/pos/ProductVariantSelector';
import { useProducts } from '../../contexts/ProductContext';
import { useCustomers } from '../../contexts/CustomerContext';
import { Product, Customer, Address, CartItem, ProductVariant } from '../../types';
import { addOrder } from '../../services/orderService';
import { getAllCategories } from '../../services/productService';
import { updateVariantStock } from '../../services/productVariantService';

interface CustomerModalContentProps {
  customerSearchQuery: string;
  onSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onSearchKeyDown: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  onSearch: () => void;
  onClearSearch: () => void;
  onClose: () => void;
  onAddNewCustomer: () => void;
  onSelectCustomer: (customer: Customer) => void;
  isLoadingCustomers: boolean;
  isSearchingCustomers: boolean;
  filteredCustomers: Customer[];
  formatPhoneNumber: (phone: string) => string;
}

export function POS() {
  const location = useLocation();
  const { products, updateStock } = useProducts();
  const { customers, refreshCustomers, isInitialized: customersInitialized } = useCustomers();

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [productsPerPage] = useState(12); // Show 12 products per page for POS

  const [searchQuery, setSearchQuery] = useState('');
  const [showClearSearch, setShowClearSearch] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [categories, setCategories] = useState<string[]>(['All']);
  const [categoryMap, setCategoryMap] = useState<Record<string, string>>({});
  const [subcategoryMap, setSubcategoryMap] = useState<Record<string, string>>({});
  const [subcategoryParentMap, setSubcategoryParentMap] = useState<Record<string, string>>({});
  const [cart, setCart] = useState<CartItem[]>([]);
  const [paymentMethod, setPaymentMethod] = useState<'cash' | 'card' | 'upi'>('cash');
  const [isCheckingOut, setIsCheckingOut] = useState(false);
  const [showOrderConfirmation, setShowOrderConfirmation] = useState(false);
  const [currentOrderId, setCurrentOrderId] = useState('');
  const [showCustomerModal, setShowCustomerModal] = useState(false);
  const [showAddCustomerForm, setShowAddCustomerForm] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [selectedAddress, setSelectedAddress] = useState<Address | null>(null);
  const [customerSearchQuery, setCustomerSearchQuery] = useState('');
  const [isLoadingCustomers, setIsLoadingCustomers] = useState(false);
  const [isSearchingCustomers, setIsSearchingCustomers] = useState(false);
  const [filteredCustomers, setFilteredCustomers] = useState<Customer[]>([]);
  const [isSearchMode, setIsSearchMode] = useState(false);
  const categoriesRef = useRef<HTMLDivElement>(null);

  // Variant selector state
  const [showVariantSelector, setShowVariantSelector] = useState(false);
  const [selectedProductForVariant, setSelectedProductForVariant] = useState<Product | null>(null);

  // Extract customer ID from URL if present
  const searchParams = new URLSearchParams(location.search);
  const customerId = searchParams.get('customerId');

  // Fetch categories on component mount
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const categoriesData = await getAllCategories();

        // Create maps for categories and subcategories
        const catMap: Record<string, string> = {};
        const subCatMap: Record<string, string> = {};
        const parentMap: Record<string, string> = {};

        // First, map all main categories
        categoriesData.forEach(cat => {
          if (cat.id && cat.name) {
            catMap[cat.id] = cat.name;
            console.log(`Mapped main category: ${cat.name} (${cat.id})`);

            // Then map all subcategories and their parents
            if (cat.subcategories && cat.subcategories.length > 0) {
              console.log(`Category ${cat.name} has ${cat.subcategories.length} subcategories`);

              cat.subcategories.forEach(subcat => {
                if (subcat.id && subcat.name) {
                  subCatMap[subcat.id] = subcat.name;
                  parentMap[subcat.id] = cat.id;
                  console.log(`  Mapped subcategory: ${subcat.name} (${subcat.id}) -> parent: ${cat.name} (${cat.id})`);
                }
              });
            } else {
              console.log(`Category ${cat.name} has no subcategories`);
            }
          }
        });

        // Add 'All' category
        const allCategories = ['All', ...categoriesData.map(cat => cat.id)];

        setCategoryMap(catMap);
        setSubcategoryMap(subCatMap);
        setSubcategoryParentMap(parentMap);
        setCategories(allCategories);
      } catch (error) {
        console.error('Error fetching categories:', error);
      }
    };

    fetchCategories();
  }, []);

  // Pre-select customer if ID is provided in URL
  useEffect(() => {
    if (!customersInitialized) {
      refreshCustomers();
    }

    if (customerId && customers && customers.length > 0) {
      // Find the customer by ID
      const customer = customers.find(c => c.id === customerId);

      console.log('Found customer:', customer);

      if (customer) {
        console.log('Pre-selecting customer:', customer.name);
        setSelectedCustomer(customer);

        // Find default address or use the first one
        const defaultAddress = customer.addresses?.find(addr => addr.isDefault) || customer.addresses?.[0];
        console.log('Setting default address:', defaultAddress);
        setSelectedAddress(defaultAddress || null);
      } else {
        console.error('Customer not found with ID:', customerId);
        console.log('This might be an ID format issue. Expected format from mockCustomers:', customers[0]?.id);
      }
    }
  }, [location, customers, customerId, customersInitialized, refreshCustomers]);

  // Fetch customers from Firebase
  useEffect(() => {
    const fetchCustomers = async () => {
      try {
        setIsLoadingCustomers(true);
        await refreshCustomers(); // Use the context method instead of direct API call
        setIsLoadingCustomers(false);
      } catch (error) {
        console.error('Error fetching customers:', error);
        setIsLoadingCustomers(false);
      }
    };

    // Only fetch if we need to select a customer or if we have a customerId in the URL
    if ((customerId || showCustomerModal) && !customersInitialized) {
      fetchCustomers();
    }
  }, [customerId, showCustomerModal, refreshCustomers, customersInitialized]);

  // Initialize filtered customers when component mounts or customers list changes
  useEffect(() => {
    if (!isSearchMode) {
      setFilteredCustomers(customers);
    }

    // Debug log to see what customers are loaded
    console.log(`POS has ${customers.length} customers loaded`);
    if (customers.length > 0) {
      console.log('First few customers:', customers.slice(0, 3).map(c => ({
        id: c.id,
        name: c.name,
        email: c.email,
        phone: c.phone
      })));
    }
  }, [customers, isSearchMode]);

  // Create a memoized local search function that doesn't update the global state
  const searchCustomersLocally = useCallback((searchTerm: string) => {
    if (!searchTerm.trim()) {
      return customers;
    }

    const query = searchTerm.toLowerCase();
    return customers.filter(customer =>
      (customer.name && customer.name.toLowerCase().includes(query)) ||
      (customer.email && customer.email.toLowerCase().includes(query)) ||
      (customer.phone && customer.phone.includes(searchTerm))
    );
  }, [customers]);

  // Log cart changes
  useEffect(() => {
    console.log('Cart updated:', cart);
  }, [cart]);

  // Filter products based on search query and selected category
  const filteredProducts = products.filter(product => {
    const matchesSearch = searchQuery === '' ||
      product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (product.tamilName && product.tamilName.toLowerCase().includes(searchQuery.toLowerCase()));

    // Check if this product's category is a subcategory
    const isSubcategory = !!subcategoryParentMap[product.category];
    const parentCategoryId = subcategoryParentMap[product.category];

    // Check if this product has a subcategory field that matches a subcategory of the selected category
    const hasMatchingSubcategory = product.subcategory &&
      subcategoryParentMap[product.subcategory] === selectedCategory;

    // A product matches the selected category if:
    // 1. "All" is selected, OR
    // 2. The product's category matches the selected category directly, OR
    // 3. The product's category is a subcategory of the selected category, OR
    // 4. The product's subcategory field is a subcategory of the selected category
    const matchesCategory =
      selectedCategory === 'All' ||
      product.category === selectedCategory ||
      (isSubcategory && parentCategoryId === selectedCategory) ||
      hasMatchingSubcategory;

    // Debug category filtering
    if (selectedCategory !== 'All' && product.name.includes('Sample')) {
      console.log(`Filtering product ${product.name}:`, {
        productCategory: product.category,
        productSubcategory: product.subcategory,
        isSubcategory,
        parentCategoryId,
        hasMatchingSubcategory,
        selectedCategory,
        matchesCategory,
        categoryName: categoryMap[product.category] || 'Unknown',
        subcategoryName: product.subcategory ? subcategoryMap[product.subcategory] || 'Unknown subcategory' : 'No subcategory'
      });
    }

    return matchesSearch && matchesCategory;
  });

  // Calculate pagination
  const indexOfLastProduct = currentPage * productsPerPage;
  const indexOfFirstProduct = indexOfLastProduct - productsPerPage;
  const currentProducts = filteredProducts.slice(indexOfFirstProduct, indexOfLastProduct);
  const totalPages = Math.ceil(filteredProducts.length / productsPerPage);

  // Change page
  const goToPage = (pageNumber: number) => {
    setCurrentPage(Math.max(1, Math.min(pageNumber, totalPages)));
  };

  // Reset to first page when search or category changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery, selectedCategory]);

  // Handle customer search input change - memoized to prevent re-renders
  const handleCustomerSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setCustomerSearchQuery(newValue);

    // If clearing the search
    if (!newValue.trim() && isSearchMode) {
      console.log('Customer search cleared, showing all customers');
      setIsSearchMode(false);
      setFilteredCustomers(customers);
    }
  }, [customers, isSearchMode]);

  // Perform the actual search - memoized
  const performCustomerSearch = useCallback(() => {
    const searchTerm = customerSearchQuery.trim();

    // Skip if empty
    if (!searchTerm) {
      setFilteredCustomers(customers);
      setIsSearchMode(false);
      return;
    }

    console.log(`Searching for customer: "${searchTerm}"`);
    setIsSearchMode(true);
    setIsSearchingCustomers(true);

    // Use the context's searchCustomers function to get better results
    try {
      // Import the searchAllCustomers function directly
      import('../../services/customerService').then(async ({ searchAllCustomers }) => {
        try {
          // Search ALL customers in the database
          const searchResults = await searchAllCustomers(searchTerm);
          console.log(`Found ${searchResults.length} customers matching "${searchTerm}" from database search`);

          if (searchResults.length > 0) {
            setFilteredCustomers(searchResults);
          } else {
            // Fallback to local search with more flexible matching
            console.log('No results from database search, trying local search');
            const query = searchTerm.toLowerCase();

            // More flexible local search
            const results = customers.filter(customer => {
              const nameMatch = customer.name && customer.name.toLowerCase().includes(query);
              const emailMatch = customer.email && customer.email.toLowerCase().includes(query);
              const phoneMatch = customer.phone && customer.phone.includes(query);

              // Log for debugging
              if (nameMatch || emailMatch || phoneMatch) {
                console.log('Local match found:', customer.name, customer.email, customer.phone);
              }

              return nameMatch || emailMatch || phoneMatch;
            });

            console.log(`Found ${results.length} customers matching "${searchTerm}" from local search`);
            setFilteredCustomers(results);
          }
        } catch (error) {
          console.error('Error in database search:', error);

          // Fallback to local search
          const query = searchTerm.toLowerCase();
          const results = customers.filter(customer =>
            (customer.name && customer.name.toLowerCase().includes(query)) ||
            (customer.email && customer.email.toLowerCase().includes(query)) ||
            (customer.phone && customer.phone.includes(query))
          );

          console.log(`Found ${results.length} customers matching "${searchTerm}" from fallback local search`);
          setFilteredCustomers(results);
        } finally {
          setIsSearchingCustomers(false);
        }
      }).catch(error => {
        console.error('Error importing searchAllCustomers:', error);
        setIsSearchingCustomers(false);
      });
    } catch (error) {
      console.error('Error in search process:', error);
      setIsSearchingCustomers(false);
    }
  }, [customerSearchQuery, customers]);

  // Handle customer search on Enter key press - memoized
  const handleCustomerSearchKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      performCustomerSearch();
    }
  }, [performCustomerSearch]);

  // Handle clear customer search - memoized
  const handleClearCustomerSearch = useCallback(() => {
    console.log('Clear customer search button clicked');

    // Clear search and refresh
    setCustomerSearchQuery('');
    setIsSearchMode(false);
    setFilteredCustomers(customers);
  }, [customers]);

  // Handle selecting a customer - memoized
  const handleSelectCustomer = useCallback((customer: Customer) => {
    setSelectedCustomer(customer);
    setSelectedAddress(customer.addresses?.find(addr => addr.isDefault) || customer.addresses?.[0] || null);
    setShowCustomerModal(false);
  }, []);

  // Handle adding a new customer - memoized
  const handleAddNewCustomer = useCallback(() => {
    setShowCustomerModal(false);
    setShowAddCustomerForm(true);
  }, []);

  // Get category name from ID
  const getCategoryName = (categoryId: string): string => {
    // Check if this is a subcategory
    if (subcategoryMap[categoryId]) {
      // It's a subcategory, return the subcategory name
      return subcategoryMap[categoryId];
    }
    // If it's a main category, return the category name
    return categoryMap[categoryId] || categoryId;
  };

  // Scroll categories horizontally
  const scrollCategories = (direction: 'left' | 'right') => {
    if (categoriesRef.current) {
      const scrollAmount = direction === 'left' ? -200 : 200;
      categoriesRef.current.scrollBy({ left: scrollAmount, behavior: 'smooth' });
    }
  };

  // Handle product click - check if it has variants
  const handleProductClick = (product: Product) => {
    if (product.hasVariants) {
      setSelectedProductForVariant(product);
      setShowVariantSelector(true);
    } else {
      addToCart(product);
    }
  };

  // Add product to cart (with optional variant)
  const addToCart = (product: Product, variant?: ProductVariant, quantity: number = 1) => {
    console.log('Adding product to cart:', product, variant);
    setCart(currentCart => {
      console.log('Current cart:', currentCart);

      // Create unique identifier for cart item (product + variant combination)
      const cartItemKey = variant ? `${product.id}-${variant.id}` : product.id;
      const existingItem = currentCart.find(item =>
        variant
          ? (item.productId === product.id && item.variantID === variant.id)
          : (item.productId === product.id && !item.variantID)
      );

      // Get available stock
      const availableStock = variant ? variant.stock : (product.stock || 0);
      const currentPrice = variant ? variant.price : product.price;

      if (existingItem) {
        console.log('Product/variant already in cart, updating quantity');
        if (existingItem.quantity + quantity > availableStock) {
          alert('Cannot add more items than available in stock');
          return currentCart;
        }
        return currentCart.map(item =>
          (variant
            ? (item.productId === product.id && item.variantID === variant.id)
            : (item.productId === product.id && !item.variantID))
            ? {
                ...item,
                quantity: item.quantity + quantity,
                subtotal: (item.quantity + quantity) * item.price
              }
            : item
        );
      }

      // Add new item to cart
      const newItem: CartItem = {
        id: `cart-item-${Date.now()}-${Math.random()}`,
        productId: product.id,
        productName: product.name,
        tamilName: product.tamilName || '',
        price: currentPrice,
        quantity: quantity,
        unit: product.unit,
        subtotal: currentPrice * quantity,
        product: product,
        // Variant fields
        variantID: variant?.id || null,
        variationValues: variant?.variationValues || null,
        variant: variant
      };

      console.log('Adding new item to cart:', newItem);
      return [...currentCart, newItem];
    });
  };

  // Update item quantity in cart
  const updateQuantity = (item: CartItem, delta: number) => {
    const newQuantity = item.quantity + delta;
    if (newQuantity > (item.product?.stock || 0)) {
      alert('Cannot add more items than available in stock');
      return;
    }
    if (newQuantity <= 0) {
      removeFromCart(item.id);
      return;
    }
    setCart(currentCart =>
      currentCart.map(cartItem =>
        cartItem.id === item.id
          ? { ...cartItem, quantity: newQuantity, subtotal: newQuantity * cartItem.price }
          : cartItem
      )
    );
  };

  // Remove item from cart
  const removeFromCart = (id: string) => {
    setCart(currentCart => currentCart.filter(item => item.id !== id));
  };

  // Calculate subtotal
  const calculateSubtotal = () => {
    return cart.reduce((total, item) => total + item.subtotal, 0);
  };

  // Calculate tax
  const calculateTax = () => {
    return calculateSubtotal() * 0.05;
  };

  // Calculate total
  const calculateTotal = () => {
    return calculateSubtotal() + calculateTax();
  };

  // Handle checkout
  const handleCheckout = async () => {
    if (cart.length === 0) {
      alert('Please add items to cart before checkout');
      return;
    }

    // Validate that a customer is selected
    if (!selectedCustomer) {
      alert('Please select a customer before checkout');
      return;
    }

    setIsCheckingOut(true);
    try {
      // Update stock levels
      cart.forEach(item => {
        updateStock(item.productId, item.quantity);
      });

      // Create order object with required fields
      const order: any = {
        customerId: selectedCustomer?.id,
        customerName: selectedCustomer?.name || 'Walk-in Customer',
        customerPhone: selectedCustomer?.phone || '',
        items: cart.map(({ id, productId, productName, tamilName, price, quantity, unit, subtotal, variantID, variationValues }) => ({
          id,
          productId,
          productName,
          tamilName,
          price,
          quantity,
          unit,
          subtotal,
          // Include variant information
          variantID: variantID || null,
          variationValues: variationValues || null
        })),
        subtotal: calculateSubtotal(),
        tax: calculateTax(),
        total: calculateTotal(),
        status: 'placed' as const,
        // Set payment status based on payment method
        paymentStatus: paymentMethod === 'cash' ? 'unpaid' : 'paid',
        paymentMethod: paymentMethod,
      };

      // Only add email if it exists
      if (selectedCustomer?.email) {
        order.customerEmail = selectedCustomer.email;
      }

      // Add delivery address if selected
      if (selectedAddress) {
        // Create a clean address object WITHOUT the state field
        // Only include fields that are not undefined or null
        const addressObj: any = {};

        // Add required fields with default values if needed
        addressObj.type = selectedAddress.type || 'home';
        addressObj.line1 = selectedAddress.line1 || '';

        // Only add optional fields if they exist and are not undefined/null
        if (selectedAddress.line2) addressObj.line2 = selectedAddress.line2;
        if (selectedAddress.city) addressObj.city = selectedAddress.city;
        if (selectedAddress.pincode) addressObj.pincode = selectedAddress.pincode;

        // Add phone number from address or customer
        addressObj.phoneNumber = selectedAddress.phoneNumber || selectedCustomer?.phone || '';

        // IMPORTANT: Do NOT include the state field at all

        console.log('Created delivery address object:', addressObj);
        order.deliveryAddress = addressObj;
      }

      console.log('Creating order:', order);

      // Save the order to Firebase and update customer data
      const orderId = await addOrder(order);

      if (orderId) {
        console.log('Order created successfully with ID:', orderId);

        // Get the order details to get the orderID
        const { getOrderById } = await import('../../services/orderService');
        const orderDetails = await getOrderById(orderId);

        if (orderDetails && orderDetails.orderID) {
          console.log('Order details fetched with orderID:', orderDetails.orderID);
          // Use the orderID as the current order ID if available
          setCurrentOrderId(orderDetails.orderID.toString());
        } else {
          setCurrentOrderId(orderId);
        }

        setShowOrderConfirmation(true);

        // If we have a customer, refresh the customers list to get updated totalOrders
        if (selectedCustomer) {
          refreshCustomers();
        }
      } else {
        throw new Error('Failed to create order');
      }
    } catch (error) {
      console.error('Error processing order:', error);
      if (error instanceof Error) {
        console.error('Error message:', error.message);
        console.error('Error stack:', error.stack);
      }
      alert('Failed to process order. Please try again.');
    } finally {
      setIsCheckingOut(false);
    }
  };

  // Pagination component
  const Pagination = () => {
    if (totalPages <= 1) return null;

    return (
      <div className="flex justify-center items-center mt-6 mb-4 gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => goToPage(1)}
          disabled={currentPage === 1}
        >
          <ChevronsLeft className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => goToPage(currentPage - 1)}
          disabled={currentPage === 1}
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>

        <div className="flex items-center gap-1 mx-2">
          <span className="text-sm font-medium">
            Page {currentPage} of {totalPages}
          </span>
        </div>

        <Button
          variant="outline"
          size="sm"
          onClick={() => goToPage(currentPage + 1)}
          disabled={currentPage === totalPages}
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => goToPage(totalPages)}
          disabled={currentPage === totalPages}
        >
          <ChevronsRight className="h-4 w-4" />
        </Button>
      </div>
    );
  };

// Memoized Customer Modal Component
const CustomerModalContent = memo(({
  customerSearchQuery,
  onSearchChange,
  onSearchKeyDown,
  onSearch,
  onClearSearch,
  onClose,
  onAddNewCustomer,
  onSelectCustomer,
  isLoadingCustomers,
  isSearchingCustomers,
  filteredCustomers,
  formatPhoneNumber
}: CustomerModalContentProps) => {
  const searchInputRef = useRef(null);

  // Focus the search input when the modal opens
  useEffect(() => {
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, []);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] flex flex-col">
        <div className="flex justify-between items-center p-4 border-b">
          <h2 className="text-xl font-semibold">Select Customer</h2>
          <button
            className="text-gray-500 hover:text-gray-700"
            onClick={onClose}
          >
            <X className="w-5 h-5" />
          </button>
        </div>
        <div className="p-4 border-b">
          <div className="flex gap-2">
            <div className="relative flex-grow">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <Search className="w-4 h-4 text-gray-400" />
              </div>
              <input
                ref={searchInputRef}
                type="search"
                className="block w-full p-2.5 pl-10 text-sm text-gray-900 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                placeholder="Search by name, email or phone..."
                value={customerSearchQuery}
                onChange={onSearchChange}
                onKeyDown={onSearchKeyDown}
              />
              {customerSearchQuery && (
                <div className="absolute right-2 top-1/2 -translate-y-1/2 flex">
                  <button
                    className="text-gray-400 hover:text-gray-600 mr-1"
                    onClick={onSearch}
                    type="button"
                    aria-label="Search"
                  >
                    <Search className="w-5 h-5" />
                  </button>
                  <button
                    className="text-gray-400 hover:text-gray-600"
                    onClick={onClearSearch}
                    type="button"
                    aria-label="Clear search"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>
              )}
            </div>
            <Button
              variant="primary"
              onClick={onAddNewCustomer}
            >
              <Plus className="w-4 h-4 mr-1" />
              New Customer
            </Button>
          </div>
        </div>
        <div className="p-4 overflow-y-auto max-h-[60vh]">
          {isLoadingCustomers || isSearchingCustomers ? (
            <div className="text-center py-8">
              <RefreshCw className="w-8 h-8 text-primary-500 animate-spin mx-auto mb-4" />
              <p className="text-gray-500 mb-4">{isSearchingCustomers ? 'Searching customers...' : 'Loading customers...'}</p>
            </div>
          ) : filteredCustomers.length > 0 ? (
            filteredCustomers.map(customer => (
              <div
                key={customer.id}
                className="p-4 border border-gray-200 rounded-lg mb-4 cursor-pointer hover:bg-gray-50"
                onClick={() => onSelectCustomer(customer)}
              >
                <div className="flex justify-between">
                  <div>
                    <h3 className="font-medium text-gray-900">
                      {customer.name && customer.name !== 'Unknown Customer'
                        ? customer.name
                        : (customer.phone
                            ? formatPhoneNumber(customer.phone)
                            : 'Unnamed Customer')}
                    </h3>
                    {customer.phone && (
                      <p className="text-sm text-gray-600">{formatPhoneNumber(customer.phone)}</p>
                    )}
                    {customer.email && (
                      <p className="text-sm text-gray-600">{customer.email}</p>
                    )}
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-600">{customer.totalOrders || 0} orders</p>
                    <p className="text-lg font-semibold text-gray-900">₹{(customer.totalSpent || 0).toLocaleString()}</p>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500 mb-4">No customers found matching your search.</p>
              <Button
                variant="outline"
                onClick={onAddNewCustomer}
              >
                <Plus className="w-4 h-4 mr-2" />
                Add New Customer
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
});

  // Helper function to format phone numbers
  const formatPhoneNumber = (phone: string) => {
    if (!phone) return 'N/A';

    // If phone number is already formatted, return it
    if (phone.includes(' ') || phone.includes('-')) return phone;

    // If it's an international number starting with +
    if (phone.startsWith('+')) {
      // For Indian numbers (+91XXXXXXXXXX)
      if (phone.startsWith('+91') && phone.length === 13) {
        return `+91 ${phone.substring(3, 8)} ${phone.substring(8)}`;
      }
      // Generic international format
      return phone.replace(/(\+\d{2})(\d{5})(\d+)/, '$1 $2 $3');
    }

    // For 10-digit numbers (most common case)
    if (phone.length === 10) {
      return phone.replace(/(\d{5})(\d{5})/, '$1 $2');
    }

    return phone;
  };

  return (
    <div className="flex h-[calc(100vh-4rem)]">
      {/* Products Section */}
      <div className="flex-1 p-6 overflow-y-auto">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">Point of Sale</h1>
          <p className="text-gray-600 mt-1">Process orders quickly and efficiently</p>
        </div>

        {/* Search Bar */}
        <div className="mb-6 relative">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Search className="w-5 h-5 text-gray-400" />
          </div>
          <input
            type="search"
            className="block w-full p-4 pl-12 text-sm text-gray-900 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
            placeholder="Search products by name..."
            value={searchQuery}
            onChange={(e) => {
              setSearchQuery(e.target.value);
              setShowClearSearch(e.target.value !== '');
            }}
          />
          {showClearSearch && (
            <button
              className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
              onClick={() => {
                setSearchQuery('');
                setShowClearSearch(false);
              }}
            >
              <X className="w-5 h-5" />
            </button>
          )}
        </div>

        {/* Categories */}
        <div className="mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-3">Categories</h2>
          <div className="relative">
            <div className="absolute left-0 top-1/2 -translate-y-1/2 z-10">
              <button
                className="flex items-center justify-center h-8 w-8 rounded-full bg-white shadow-md text-gray-600 hover:text-primary-500 hover:bg-primary-50 transition-colors"
                onClick={() => scrollCategories('left')}
              >
                <ChevronLeft className="h-5 w-5" />
              </button>
            </div>

            <div
              ref={categoriesRef}
              className="flex space-x-2 overflow-x-auto pb-2 px-10 scrollbar-hide"
            >
              {categories.map(category => (
                <button
                  key={category}
                  className={`px-4 py-2 rounded-full whitespace-nowrap ${
                    selectedCategory === category
                      ? 'bg-primary-500 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                  onClick={() => setSelectedCategory(category)}
                >
                  {category === 'All' ? 'All Products' : getCategoryName(category)}
                </button>
              ))}
            </div>

            <div className="absolute right-0 top-1/2 -translate-y-1/2 z-10">
              <button
                className="flex items-center justify-center h-8 w-8 rounded-full bg-white shadow-md text-gray-600 hover:text-primary-500 hover:bg-primary-50 transition-colors"
                onClick={() => scrollCategories('right')}
              >
                <ChevronRight className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>

        {/* Products Grid */}
        {filteredProducts.length === 0 ? (
          <div className="flex flex-col items-center justify-center p-8 bg-gray-50 rounded-lg border border-gray-200 mt-4">
            <div className="bg-gray-100 p-4 rounded-full mb-4">
              <Package className="w-10 h-10 text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">No products found</h3>
            <p className="text-gray-600 text-center mb-4">
              Try adjusting your search or category filter to find what you're looking for.
            </p>
            <Button
              variant="outline"
              onClick={() => {
                setSearchQuery('');
                setSelectedCategory('All');
              }}
            >
              Clear filters
            </Button>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-5">
              {currentProducts.map(product => (
                <div
                  key={product.id}
                  className={`cursor-pointer hover:shadow-lg transition-shadow ${(product.stock || 0) <= 0 ? 'opacity-75' : ''}`}
                  onClick={() => {
                    if ((product.stock || 0) > 0 || product.hasVariants) {
                      console.log('Product card clicked:', product);
                      handleProductClick(product);
                    } else {
                      alert('This product is out of stock');
                    }
                  }}
                >
                  <Card className="h-full overflow-hidden">
                    <div className="aspect-square overflow-hidden relative">
                      <img
                        src={product.images && product.images.length > 0 ? product.images[0] : '/placeholder-image.png'}
                        alt={product.name}
                        className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                        onError={(e) => {
                          // Use a data URI as fallback to ensure it always works
                          e.currentTarget.src = 'data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%22300%22%20height%3D%22300%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Crect%20width%3D%22300%22%20height%3D%22300%22%20fill%3D%22%23f0f0f0%22%2F%3E%3Ctext%20x%3D%22150%22%20y%3D%22150%22%20font-size%3D%2220%22%20text-anchor%3D%22middle%22%20alignment-baseline%3D%22middle%22%20fill%3D%22%23999%22%3ENo%20Image%3C%2Ftext%3E%3C%2Fsvg%3E';
                        }}
                      />

                      {/* Out of stock indicator */}
                      {(product.stock || 0) <= 0 && (
                        <div className="absolute bottom-0 left-0 right-0 bg-red-600 bg-opacity-90 py-1 px-2 text-center">
                          <span className="text-white text-xs font-semibold">OUT OF STOCK</span>
                        </div>
                      )}
                    </div>
                    <div className="p-3">
                      <div className="mb-2">
                        <Badge variant="secondary" className="mb-1 text-xs">
                          {/* Show subcategory if available, otherwise show main category */}
                          {(() => {
                            // Check if the product has a subcategory field
                            if (product.subcategory && subcategoryMap[product.subcategory]) {
                              // If the product has a subcategory field and it's in our subcategory map, use that
                              return subcategoryMap[product.subcategory];
                            }

                            // Otherwise, check if the category is actually a subcategory
                            const isSubcategory = !!subcategoryParentMap[product.category];
                            const subcategoryName = subcategoryMap[product.category];
                            const categoryName = categoryMap[product.category] || 'Uncategorized';

                            // Log for debugging
                            if (product.name.includes('Sample')) {
                              console.log(`Product card category display for ${product.name}:`, {
                                productCategory: product.category,
                                productSubcategory: product.subcategory,
                                isSubcategory,
                                subcategoryName,
                                categoryName,
                                inSubcategoryMap: product.category in subcategoryMap,
                                inCategoryMap: product.category in categoryMap,
                                willDisplay: subcategoryName || categoryName
                              });
                            }

                            return subcategoryName || categoryName;
                          })()}
                        </Badge>
                        <h3 className="font-semibold text-gray-900 line-clamp-1 text-sm md:text-base">{product.name}</h3>
                        {product.tamilName && (
                          <p className="text-xs text-gray-600 line-clamp-1">{product.tamilName}</p>
                        )}
                      </div>
                      <div className="flex justify-between items-center mt-2">
                        <div className="flex flex-col">
                          <div className="flex items-center gap-2">
                            <span className="text-base font-bold text-primary-600">
                              ₹{product.price}/{product.unit}
                            </span>
                            {product.mrp && product.mrp > product.price && (
                              <span className="text-xs text-gray-500 line-through">
                                ₹{product.mrp}
                              </span>
                            )}
                          </div>
                        </div>
                        {(product.stock || 0) > 0 && (
                          <span className={`text-xs font-medium px-2 py-1 rounded-full
                            ${(product.stock || 0) > 10 ? 'bg-green-100 text-green-700' : 'bg-amber-100 text-amber-700'}`}>
                            {product.stock} {product.unit}
                          </span>
                        )}
                      </div>
                    </div>
                  </Card>
                </div>
              ))}
            </div>

            {/* Pagination */}
            <Pagination />
          </>
        )}      </div>

      {/* Cart Section */}
      <div className="w-96 bg-gray-50 border-l border-gray-200 flex flex-col">
        {/* Cart Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Shopping Cart</h2>
            <Badge variant="primary">{cart.length} items</Badge>
          </div>

          {/* Customer Selection */}
          <div className="mb-2">
            <p className="text-sm text-gray-600 mb-1">Customer</p>
            {selectedCustomer ? (
              <div className="flex justify-between items-center p-2 bg-white rounded-lg border border-gray-200">
                <div>
                  <p className="font-medium text-gray-900">
                    {selectedCustomer.name && selectedCustomer.name !== 'Unknown Customer'
                      ? selectedCustomer.name
                      : (selectedCustomer.phone
                          ? formatPhoneNumber(selectedCustomer.phone)
                          : 'Unnamed Customer')}
                  </p>
                  <p className="text-sm text-gray-600">{formatPhoneNumber(selectedCustomer.phone)}</p>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowCustomerModal(true)}
                >
                  Change
                </Button>
              </div>
            ) : (
              <Button
                variant="outline"
                className="w-full"
                onClick={() => setShowCustomerModal(true)}
              >
                <User className="w-4 h-4 mr-2" />
                Select Customer
              </Button>
            )}
          </div>

          {/* Address Selection */}
          {selectedCustomer && selectedCustomer.addresses && selectedCustomer.addresses.length > 0 && (
            <div>
              <p className="text-sm text-gray-600 mb-1">Delivery Address</p>
              <div className="p-2 bg-white rounded-lg border border-gray-200">
                <p className="font-medium text-gray-900 text-sm">{selectedAddress?.line1}</p>
                <p className="text-sm text-gray-600">{selectedAddress?.city}, {selectedAddress?.state}</p>
              </div>
            </div>
          )}
        </div>

        {/* Cart Items */}
        <div className="flex-1 overflow-y-auto p-4">
          {cart.length === 0 ? (
            <div className="text-center text-gray-500 mt-8">
              <ShoppingCart className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>Your cart is empty</p>
            </div>
          ) : (
            <div className="space-y-4">
              {cart.map(item => (
                <div key={item.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">{item.productName}</h4>
                    {item.tamilName && (
                      <p className="text-sm text-gray-600">{item.tamilName}</p>
                    )}
                    {/* Show variant information if available */}
                    {item.variationValues && Object.keys(item.variationValues).length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-1">
                        {Object.entries(item.variationValues).map(([type, value]) => (
                          <span key={type} className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
                            {type}: {value}
                          </span>
                        ))}
                      </div>
                    )}
                    <p className="text-sm font-medium text-gray-900">₹{item.price}/{item.unit}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => updateQuantity(item, -1)}
                    >
                      <Minus className="w-3 h-3" />
                    </Button>
                    <span className="w-8 text-center font-medium">{item.quantity}</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => updateQuantity(item, 1)}
                    >
                      <Plus className="w-3 h-3" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-red-500 hover:text-red-700 hover:bg-red-50"
                      onClick={() => removeFromCart(item.id)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Cart Footer */}
        <div className="p-4 border-t border-gray-200">
          {/* Order Summary */}
          <div className="mb-4 space-y-2">
            <div className="flex justify-between text-gray-600">
              <span>Subtotal</span>
              <span>₹{calculateSubtotal().toFixed(2)}</span>
            </div>
            <div className="flex justify-between text-gray-600">
              <span>Tax (5%)</span>
              <span>₹{calculateTax().toFixed(2)}</span>
            </div>
            <div className="flex justify-between font-semibold text-gray-900 text-lg">
              <span>Total</span>
              <span>₹{calculateTotal().toFixed(2)}</span>
            </div>
          </div>

          {/* Payment Method */}
          <div className="mb-4">
            <p className="text-sm text-gray-600 mb-2">Payment Method</p>
            <div className="grid grid-cols-3 gap-2">
              <Button
                variant={paymentMethod === 'cash' ? 'primary' : 'outline'}
                className="flex items-center justify-center"
                onClick={() => setPaymentMethod('cash')}
              >
                <Wallet className="w-4 h-4 mr-1" />
                Cash
              </Button>
              <Button
                variant={paymentMethod === 'card' ? 'primary' : 'outline'}
                className="flex items-center justify-center"
                onClick={() => setPaymentMethod('card')}
              >
                <CreditCard className="w-4 h-4 mr-1" />
                Card
              </Button>
              <Button
                variant={paymentMethod === 'upi' ? 'primary' : 'outline'}
                className="flex items-center justify-center"
                onClick={() => setPaymentMethod('upi')}
              >
                <Tag className="w-4 h-4 mr-1" />
                UPI
              </Button>
            </div>
          </div>

          {/* Checkout Button */}
          <Button
            variant="primary"
            className="w-full"
            disabled={cart.length === 0 || !selectedCustomer || isCheckingOut}
            onClick={handleCheckout}
          >
            {isCheckingOut ? (
              <>Processing...</>
            ) : !selectedCustomer ? (
              <>
                <CreditCard className="w-4 h-4 mr-2" />
                Select Customer to Checkout
              </>
            ) : (
              <>
                <CreditCard className="w-4 h-4 mr-2" />
                Checkout (₹{calculateTotal().toFixed(2)})
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Customer Selection Modal */}
      {showCustomerModal && (
        <CustomerModalContent
          customerSearchQuery={customerSearchQuery}
          onSearchChange={handleCustomerSearchChange}
          onSearchKeyDown={handleCustomerSearchKeyDown}
          onSearch={performCustomerSearch}
          onClearSearch={handleClearCustomerSearch}
          onClose={() => setShowCustomerModal(false)}
          onAddNewCustomer={handleAddNewCustomer}
          onSelectCustomer={handleSelectCustomer}
          isLoadingCustomers={isLoadingCustomers}
          isSearchingCustomers={isSearchingCustomers}
          filteredCustomers={filteredCustomers}
          formatPhoneNumber={formatPhoneNumber}
        />
      )}

      {/* Add Customer Form Modal */}
      <AddCustomerForm
        isOpen={showAddCustomerForm}
        onClose={() => setShowAddCustomerForm(false)}
        onSave={async (newCustomer) => {
          try {
            // Import the addCustomer function from customerService
            const { addCustomer } = await import('../../services/customerService');

            // Add the customer to Firebase
            const customerId = await addCustomer(newCustomer);

            if (customerId) {
              // Refresh the customers list
              await refreshCustomers();

              // Find the newly added customer in the updated list
              const addedCustomer = customers.find(c => c.id === customerId);

              if (addedCustomer) {
                setSelectedCustomer(addedCustomer);
                setSelectedAddress(addedCustomer.addresses?.find(addr => addr.isDefault) || addedCustomer.addresses?.[0] || null);
              }
            }
          } catch (error) {
            console.error('Error adding customer:', error);
            alert('Failed to add customer. Please try again.');
          }
        }}
        isFromPOS={true}
      />

      {/* Product Variant Selector Modal */}
      {selectedProductForVariant && (
        <ProductVariantSelector
          product={selectedProductForVariant}
          isOpen={showVariantSelector}
          onClose={() => {
            setShowVariantSelector(false);
            setSelectedProductForVariant(null);
          }}
          onAddToCart={addToCart}
        />
      )}

      {/* Order Confirmation Modal */}
      <OrderConfirmation
        isOpen={showOrderConfirmation}
        onClose={() => {
          setShowOrderConfirmation(false);
          // Clear cart and selected customer after closing the confirmation
          setCart([]);
          setSelectedCustomer(null);
          setSelectedAddress(null);
        }}
        orderItems={cart}
        customer={selectedCustomer}
        address={selectedAddress}
        subtotal={calculateSubtotal()}
        tax={calculateTax()}
        total={calculateTotal()}
        paymentMethod={paymentMethod}
        paymentStatus={paymentMethod === 'cash' ? 'unpaid' : 'paid'}
        orderId={currentOrderId}
        orderDate={new Date().toISOString()}
      />
    </div>
  );
}
