import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { ArrowLeft, User, Leaf, MapPin, Award, Tag, Plus, X, RefreshCw, AlertCircle, Trash2, Save } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { Input } from '../../components/ui/Input';
import { Textarea } from '../../components/ui/Textarea';
import { doc, getDoc, setDoc, updateDoc, deleteDoc, serverTimestamp, collection } from 'firebase/firestore';
import { db } from '../../firebase/config';
import { Farmer } from '../../firebase/farmers';

export function FarmerDetail() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);

  const [formData, setFormData] = useState<Partial<Farmer>>({
    farmerName: '',
    farmName: '',
    farmLocation: '',
    experience: '',
    philosophy: '',
    certifications: [''],
    tags: [''],
  });

  useEffect(() => {
    if (id && id !== 'new') {
      fetchFarmer(id);
    }
  }, [id]);

  const fetchFarmer = async (farmerId: string) => {
    setLoading(true);
    try {
      const farmerDoc = await getDoc(doc(db, 'Farmers', farmerId));
      if (farmerDoc.exists()) {
        const data = farmerDoc.data();
        setFormData({
          id: farmerId,
          farmerName: data.farmerName || '',
          farmName: data.farmName || '',
          farmLocation: data.farmLocation || '',
          experience: data.experience || '',
          philosophy: data.philosophy || '',
          certifications: data.certifications || [],
          tags: data.tags || [],
          createdAt: data.createdAt?.toDate?.()
            ? data.createdAt.toDate().toISOString()
            : (data.createdAt || new Date().toISOString()),
          updatedAt: data.updatedAt?.toDate?.()
            ? data.updatedAt.toDate().toISOString()
            : (data.updatedAt || new Date().toISOString())
        });
      } else {
        setError('Farmer not found');
        navigate('/farmers');
      }
    } catch (err) {
      console.error('Error fetching farmer:', err);
      setError('Failed to load farmer details');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleArrayInputChange = (index: number, value: string, field: 'certifications' | 'tags') => {
    const newArray = [...(formData[field] || [])];
    newArray[index] = value;
    setFormData(prev => ({ ...prev, [field]: newArray }));
  };

  const addArrayItem = (field: 'certifications' | 'tags') => {
    setFormData(prev => ({
      ...prev,
      [field]: [...(prev[field] || []), '']
    }));
  };

  const removeArrayItem = (index: number, field: 'certifications' | 'tags') => {
    const newArray = [...(formData[field] || [])];
    newArray.splice(index, 1);
    setFormData(prev => ({ ...prev, [field]: newArray }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Filter out empty values from arrays
      const cleanedData = {
        ...formData,
        certifications: formData.certifications?.filter(cert => cert.trim() !== '') || [],
        tags: formData.tags?.filter(tag => tag.trim() !== '') || []
      };

      // Generate a farmerId (numeric)
      const timestamp = Date.now();
      const generatedFarmerId = `FARM-${timestamp.toString().slice(-6)}`;

      if (id && id !== 'new') {
        // Update existing farmer
        await updateDoc(doc(db, 'Farmers', id), {
          ...cleanedData,
          farmerId: cleanedData.farmerId || generatedFarmerId, // Keep existing or set new
          updatedAt: serverTimestamp()
        });
      } else {
        // Create new farmer
        const newFarmerRef = doc(collection(db, 'Farmers'));
        await setDoc(newFarmerRef, {
          ...cleanedData,
          farmerId: generatedFarmerId,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        });
      }

      navigate('/farmers');
    } catch (err) {
      console.error('Error saving farmer:', err);
      setError('Failed to save farmer. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!id || id === 'new') return;

    setLoading(true);
    try {
      await deleteDoc(doc(db, 'Farmers', id));
      navigate('/farmers');
    } catch (err) {
      console.error('Error deleting farmer:', err);
      setError('Failed to delete farmer');
      setLoading(false);
    }
  };

  return (
    <div className="p-6">
      <div className="flex items-center gap-4 mb-6">
        <Link to="/farmers">
          <Button variant="outline" size="sm">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Farmers
          </Button>
        </Link>
        <h1 className="text-2xl font-bold text-gray-900">
          {id && id !== 'new' ? 'Edit Farmer' : 'New Farmer'}
        </h1>
      </div>

      {/* Loading state */}
      {loading && (
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg flex items-center text-blue-700">
          <RefreshCw className="w-5 h-5 mr-2 animate-spin" />
          <span>
            {id && id !== 'new' ? 'Loading farmer data...' : 'Preparing form...'}
          </span>
        </div>
      )}

      {/* Error message */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center text-red-700">
          <AlertCircle className="w-5 h-5 mr-2" />
          <span>{error}</span>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <div className="p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Farmer Information</h2>
              <div className="space-y-4">
                <Input
                  id="farmerName"
                  name="farmerName"
                  label="Farmer Name"
                  type="text"
                  placeholder="Enter farmer's name"
                  required
                  startIcon={<User className="h-4 w-4 text-gray-400" />}
                  value={formData.farmerName}
                  onChange={handleInputChange}
                />

                <Input
                  id="experience"
                  name="experience"
                  label="Experience"
                  type="text"
                  placeholder="e.g. 10+ Years Experience"
                  startIcon={<Award className="h-4 w-4 text-gray-400" />}
                  value={formData.experience}
                  onChange={handleInputChange}
                />

                <Textarea
                  id="philosophy"
                  name="philosophy"
                  label="Farming Philosophy"
                  placeholder="Share the farmer's approach to farming..."
                  value={formData.philosophy}
                  onChange={handleInputChange}
                  rows={4}
                />
              </div>
            </div>
          </Card>

          <Card>
            <div className="p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Farm Information</h2>
              <div className="space-y-4">
                <Input
                  id="farmName"
                  name="farmName"
                  label="Farm Name"
                  type="text"
                  placeholder="Enter farm name"
                  required
                  startIcon={<Leaf className="h-4 w-4 text-gray-400" />}
                  value={formData.farmName}
                  onChange={handleInputChange}
                />

                <Input
                  id="farmLocation"
                  name="farmLocation"
                  label="Farm Location"
                  type="text"
                  placeholder="Enter farm location"
                  required
                  startIcon={<MapPin className="h-4 w-4 text-gray-400" />}
                  value={formData.farmLocation}
                  onChange={handleInputChange}
                />
              </div>
            </div>
          </Card>
        </div>

        <Card>
          <div className="p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Certifications</h2>
            <div className="space-y-3">
              {formData.certifications?.map((cert, index) => (
                <div key={index} className="flex items-center gap-2">
                  <Input
                    type="text"
                    placeholder="e.g. Certified Organic Farm"
                    value={cert}
                    onChange={(e) => handleArrayInputChange(index, e.target.value, 'certifications')}
                    startIcon={<Award className="h-4 w-4 text-gray-400" />}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => removeArrayItem(index, 'certifications')}
                    className="flex-shrink-0 w-8 h-8 p-0"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
              <Button
                type="button"
                variant="outline"
                onClick={() => addArrayItem('certifications')}
                className="mt-2"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Certification
              </Button>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Tags</h2>
            <div className="space-y-3">
              {formData.tags?.map((tag, index) => (
                <div key={index} className="flex items-center gap-2">
                  <Input
                    type="text"
                    placeholder="e.g. 100% Organic"
                    value={tag}
                    onChange={(e) => handleArrayInputChange(index, e.target.value, 'tags')}
                    startIcon={<Tag className="h-4 w-4 text-gray-400" />}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => removeArrayItem(index, 'tags')}
                    className="flex-shrink-0 w-8 h-8 p-0"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
              <Button
                type="button"
                variant="outline"
                onClick={() => addArrayItem('tags')}
                className="mt-2"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Tag
              </Button>
            </div>
          </div>
        </Card>

        <div className="flex justify-between">
          <div>
            {id && id !== 'new' && (
              <Button
                type="button"
                variant="destructive"
                onClick={() => setShowDeleteConfirmation(true)}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Farmer
              </Button>
            )}
          </div>
          <div className="flex gap-3">
            <Link to="/farmers">
              <Button type="button" variant="outline">
                Cancel
              </Button>
            </Link>
            <Button type="submit" variant="primary" disabled={loading}>
              {loading ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Farmer
                </>
              )}
            </Button>
          </div>
        </div>
      </form>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirmation && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <Card className="max-w-md w-full">
            <div className="p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
              <p className="text-gray-700 mb-6">
                Are you sure you want to delete this farmer? This action cannot be undone.
              </p>
              <div className="flex justify-end gap-3">
                <Button
                  variant="outline"
                  onClick={() => setShowDeleteConfirmation(false)}
                >
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleDelete}
                  disabled={loading}
                >
                  {loading ? 'Deleting...' : 'Delete'}
                </Button>
              </div>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
}

