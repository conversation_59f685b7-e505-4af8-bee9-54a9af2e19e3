import { Order } from '../../types';

// Mock orders data
export const mockOrders: Order[] = Array.from({ length: 20 }, (_, i) => {
  const id = `ORD-${4000 - i}`;
  const statuses: Order['status'][] = ['placed', 'processing', 'out_for_delivery', 'delivered', 'cancelled'];
  const paymentStatuses: Order['paymentStatus'][] = ['pending', 'paid', 'refunded', 'failed'];
  const paymentMethods: Order['paymentMethod'][] = ['cod', 'online', 'wallet'];
  
  const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
  
  // Make payment status appropriate for the order status
  let paymentStatus: Order['paymentStatus'];
  if (randomStatus === 'cancelled') {
    paymentStatus = Math.random() > 0.5 ? 'refunded' : 'failed';
  } else if (randomStatus === 'delivered') {
    paymentStatus = 'paid';
  } else {
    paymentStatus = Math.random() > 0.7 ? 'paid' : 'pending';
  }
  
  const date = new Date();
  date.setDate(date.getDate() - Math.floor(Math.random() * 14));
  
  const subtotal = Math.round(Math.random() * 2000 + 500);
  const tax = Math.round(subtotal * 0.05);
  const deliveryFee = 50;
  const total = subtotal + tax + deliveryFee;
  
  return {
    id,
    customerId: `CUST-${1000 + i}`,
    customerName: ['Rahul Sharma', 'Priya Patel', 'Amit Singh', 'Neha Reddy', 'Vijay Kumar'][Math.floor(Math.random() * 5)],
    customerPhone: `+91 98${Math.floor(Math.random() * 90000000 + 10000000)}`,
    customerEmail: `customer${i + 1}@example.com`,
    items: Array.from({ length: Math.floor(Math.random() * 5) + 1 }, (_, j) => ({
      id: `ITEM-${i}-${j}`,
      productId: `PROD-${Math.floor(Math.random() * 100)}`,
      productName: ['Organic Tomatoes', 'Fresh Spinach', 'Red Onions', 'Sweet Carrots', 'Organic Potatoes'][Math.floor(Math.random() * 5)],
      variantId: `VAR-${Math.floor(Math.random() * 10)}`,
      variantName: `${Math.floor(Math.random() * 500) + 100}g`,
      price: Math.floor(Math.random() * 90) + 10,
      quantity: Math.floor(Math.random() * 4) + 1,
      weight: `${(Math.random() * 0.9 + 0.1).toFixed(1)}kg`,
      subtotal: Math.floor(Math.random() * 400) + 100,
    })),
    subtotal,
    tax,
    deliveryFee,
    total,
    status: randomStatus,
    paymentStatus,
    paymentMethod: paymentMethods[Math.floor(Math.random() * paymentMethods.length)],
    shippingAddress: {
      id: `ADDR-${i}`,
      type: Math.random() > 0.7 ? 'office' : 'home',
      line1: `${Math.floor(Math.random() * 100) + 1}, ${['Green Park', 'Lakeside Apartments', 'Sunset Homes', 'Riverdale'][Math.floor(Math.random() * 4)]}`,
      line2: `${['Sector', 'Block', 'Phase'][Math.floor(Math.random() * 3)]} ${Math.floor(Math.random() * 20) + 1}`,
      city: ['Mumbai', 'Delhi', 'Bangalore', 'Chennai', 'Hyderabad'][Math.floor(Math.random() * 5)],
      state: ['Maharashtra', 'Delhi', 'Karnataka', 'Tamil Nadu', 'Telangana'][Math.floor(Math.random() * 5)],
      pincode: `${Math.floor(Math.random() * 900000) + 100000}`,
      isDefault: Math.random() > 0.7,
    },
    notes: Math.random() > 0.7 ? 'Please deliver in the evening after 6pm.' : undefined,
    createdAt: date.toISOString(),
    updatedAt: date.toISOString(),
    cancellationReason: randomStatus === 'cancelled' ? ['Customer requested cancellation', 'Items out of stock', 'Delivery address issue', 'Payment failure'][Math.floor(Math.random() * 4)] : undefined,
    refundStatus: randomStatus === 'cancelled' && paymentStatus === 'refunded' ? ['pending', 'processed', 'completed'][Math.floor(Math.random() * 3)] as any : undefined,
    refundTransactionId: randomStatus === 'cancelled' && paymentStatus === 'refunded' ? `REF-${Math.floor(Math.random() * 1000000)}` : undefined,
  };
});