import { Customer } from '../../types';

export const mockCustomers: Customer[] = [
  {
    id: 'CUST-1',
    name: '<PERSON><PERSON>',
    email: 'rahu<PERSON>@example.com',
    phone: '+91 9876543210',
    addresses: [
      {
        id: 'ADDR-1',
        type: 'home',
        line1: '123, Green Park',
        line2: 'Anna Nagar',
        city: 'Chennai',
        state: 'Tamil Nadu',
        pincode: '600001',
        isDefault: true,
      },
      {
        id: 'ADDR-2',
        type: 'office',
        line1: '45, Tech Park',
        line2: 'Guindy',
        city: 'Chennai',
        state: 'Tamil Nadu',
        pincode: '600002',
        isDefault: false,
      }
    ],
    totalOrders: 15,
    totalSpent: 12500,
    lastOrderDate: '2025-02-14',
    createdAt: '2025-01-01',
    status: 'active',
  },
  {
    id: 'CUST-2',
    name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    phone: '+91 8765432109',
    addresses: [
      {
        id: 'ADDR-3',
        type: 'home',
        line1: '78, Lake View Apartments',
        line2: 'T Nagar',
        city: 'Chennai',
        state: 'Tamil Nadu',
        pincode: '600017',
        isDefault: true,
      }
    ],
    totalOrders: 8,
    totalSpent: 6800,
    lastOrderDate: '2025-02-10',
    createdAt: '2025-01-15',
    status: 'active',
  },
  {
    id: 'CUST-3',
    name: 'Vikram Singh',
    email: '<EMAIL>',
    phone: '+91 7654321098',
    addresses: [
      {
        id: 'ADDR-4',
        type: 'home',
        line1: '12, Seaward Road',
        line2: 'Besant Nagar',
        city: 'Chennai',
        state: 'Tamil Nadu',
        pincode: '600090',
        isDefault: true,
      }
    ],
    totalOrders: 5,
    totalSpent: 3200,
    lastOrderDate: '2025-02-05',
    createdAt: '2025-01-20',
    status: 'active',
  },
  {
    id: 'CUST-4',
    name: 'Ananya Reddy',
    email: '<EMAIL>',
    phone: '+91 6543210987',
    addresses: [
      {
        id: 'ADDR-5',
        type: 'home',
        line1: '56, Palm Grove',
        line2: 'Adyar',
        city: 'Chennai',
        state: 'Tamil Nadu',
        pincode: '600020',
        isDefault: true,
      }
    ],
    totalOrders: 12,
    totalSpent: 9500,
    lastOrderDate: '2025-02-12',
    createdAt: '2025-01-10',
    status: 'active',
  },
  {
    id: 'CUST-5',
    name: 'Karthik Menon',
    email: '<EMAIL>',
    phone: '+91 5432109876',
    addresses: [
      {
        id: 'ADDR-6',
        type: 'office',
        line1: '89, Business Bay',
        line2: 'Velachery',
        city: 'Chennai',
        state: 'Tamil Nadu',
        pincode: '600042',
        isDefault: true,
      }
    ],
    totalOrders: 3,
    totalSpent: 1800,
    lastOrderDate: '2025-01-25',
    createdAt: '2025-01-05',
    status: 'inactive',
  }
];

// Log the first few customer IDs to verify the format
console.log('Mock customer IDs:', mockCustomers.map(c => c.id));

