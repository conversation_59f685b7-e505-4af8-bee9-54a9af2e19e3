import { Loader2 } from 'lucide-react';

interface LoaderProps {
  size?: 'small' | 'medium' | 'large';
  color?: string;
}

export const Loader = ({ size = 'medium', color = 'primary' }: LoaderProps) => {
  const sizeClasses = {
    small: 'w-5 h-5',
    medium: 'w-8 h-8',
    large: 'w-12 h-12',
  };

  const colorClasses = {
    primary: 'text-primary-500',
    secondary: 'text-secondary-500',
    white: 'text-white',
  };

  return (
    <div className="flex justify-center items-center">
      <Loader2 
        className={`animate-spin ${sizeClasses[size]} ${colorClasses[color as keyof typeof colorClasses] || 'text-primary-500'}`} 
      />
    </div>
  );
};