import { createUserWithEmailAndPassword, getAuth, updateProfile } from 'firebase/auth';
import { doc, getDoc, getFirestore, setDoc } from 'firebase/firestore';
import { initializeApp } from 'firebase/app';

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyDuFqIFNTnydJciJ97oOYbB4Rmsj58ABc4",
  authDomain: "vrisham-cad24.firebaseapp.com",
  projectId: "vrisham-cad24",
  storageBucket: "vrisham-cad24.appspot.com",
  messagingSenderId: "404878904416",
  appId: "1:404878904416:web:ea282cffe2e6fe3ade8705",
  measurementId: "G-BQGD3Y99HH"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

/**
 * Creates an admin user in Firebase Authentication and Firestore
 */
async function createAdminUser() {
  const email = '<EMAIL>';
  const password = 'password';
  const displayName = 'admin';
  
  try {
    // Check if user already exists in Firestore
    const userDocRef = doc(db, 'users', 'admin');
    const userDoc = await getDoc(userDocRef);
    
    if (userDoc.exists()) {
      console.log('Admin user already exists in Firestore');
      return;
    }
    
    // Create user in Firebase Authentication
    try {
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;
      
      // Update profile with display name
      await updateProfile(user, { displayName });
      
      // Create user document in Firestore
      await setDoc(doc(db, 'Users', user.uid), {
        id: user.uid,
        username: displayName,
        email: user.email,
        role: 'admin',
        createdAt: new Date().toISOString(),
      });
      
      // Also create a customer document as requested
      await setDoc(doc(db, 'Users', 'customer'), {
        id: 'customer',
        username: 'admin',
        role: 'customer',
        createdAt: new Date().toISOString(),
      });
      
      console.log('Admin user created successfully');
    } catch (error: any) {
      // If user already exists in Authentication but not in Firestore
      if (error.code === 'auth/email-already-in-use') {
        console.log('Admin user already exists in Authentication, creating Firestore document');
        
        // Sign in to get the user
        // Note: In a real application, you would need to handle this differently
        // This is just for setup purposes
        
        // Create user document in Firestore with a fixed ID for simplicity
        await setDoc(doc(db, 'Users', 'admin'), {
          id: 'admin',
          username: displayName,
          email: email,
          role: 'admin',
          createdAt: new Date().toISOString(),
        });
        
        // Also create a customer document as requested
        await setDoc(doc(db, 'Users', 'customer'), {
          id: 'customer',
          username: 'admin',
          role: 'customer',
          createdAt: new Date().toISOString(),
        });
        
        console.log('Admin user document created in Firestore');
      } else {
        throw error;
      }
    }
  } catch (error) {
    console.error('Error creating admin user:', error);
  }
}

// Run the function
createAdminUser().then(() => {
  console.log('Setup complete');
  process.exit(0);
}).catch((error) => {
  console.error('Setup failed:', error);
  process.exit(1);
});
