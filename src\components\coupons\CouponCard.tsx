import React from 'react';
import { Edit2, Trash2, Copy, Calendar, Users, Target, IndianRupee, Percent, Clock } from 'lucide-react';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';
import { Coupon } from '../../types';

interface CouponCardProps {
  coupon: Coupon;
  onEdit: (coupon: Coupon) => void;
  onDelete: (coupon: Coupon) => void;
}

export const CouponCard = ({ coupon, onEdit, onDelete }: CouponCardProps) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      // You could add a toast notification here
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  const getStatusBadge = () => {
    const now = new Date();
    const expiryDate = new Date(coupon.expiresAt);
    const startDate = coupon.startAt ? new Date(coupon.startAt) : null;

    if (!coupon.isActive) {
      return <Badge variant="danger">Inactive</Badge>;
    }

    if (expiryDate < now) {
      return <Badge variant="danger">Expired</Badge>;
    }

    if (startDate && startDate > now) {
      return <Badge variant="warning">Scheduled</Badge>;
    }

    if (coupon.usageLimitGlobal && coupon.usedCount >= coupon.usageLimitGlobal) {
      return <Badge variant="danger">Limit Reached</Badge>;
    }

    return <Badge variant="success">Active</Badge>;
  };

  const getDiscountDisplay = () => {
    if (coupon.type === 'FLAT') {
      return (
        <div className="flex items-center text-lg font-bold text-green-600">
          <IndianRupee className="w-4 h-4 mr-1" />
          {coupon.flatAmount}
        </div>
      );
    } else {
      return (
        <div className="flex items-center text-lg font-bold text-green-600">
          {coupon.percent}
          <Percent className="w-4 h-4 ml-1" />
        </div>
      );
    }
  };

  const getUsageDisplay = () => {
    if (!coupon.usageLimitGlobal) {
      return `${coupon.usedCount} used`;
    }
    return `${coupon.usedCount}/${coupon.usageLimitGlobal} used`;
  };

  return (
    <Card className="p-6 hover:shadow-md transition-shadow">
      <div className="flex justify-between items-start mb-4">
        <div className="flex-1">
          <div className="flex items-center gap-3 mb-2">
            <div className="flex items-center gap-2">
              <code className="bg-gray-100 px-3 py-1 rounded-md font-mono text-sm font-bold">
                {coupon.code}
              </code>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => copyToClipboard(coupon.code)}
                className="p-1 h-auto"
              >
                <Copy className="w-3 h-3" />
              </Button>
            </div>
            {getStatusBadge()}
          </div>
          
          {coupon.description && (
            <p className="text-sm text-gray-600 mb-3">{coupon.description}</p>
          )}
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onEdit(coupon)}
          >
            <Edit2 className="w-4 h-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onDelete(coupon)}
            className="text-red-600 hover:text-red-700 hover:border-red-300"
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-4">
        <div>
          <div className="text-xs text-gray-500 mb-1">Discount</div>
          {getDiscountDisplay()}
          {coupon.type === 'PERCENT' && coupon.maxDiscount && (
            <div className="text-xs text-gray-500">
              Max: ₹{coupon.maxDiscount}
            </div>
          )}
        </div>

        <div>
          <div className="text-xs text-gray-500 mb-1">Usage</div>
          <div className="flex items-center text-sm font-medium">
            <Users className="w-4 h-4 mr-1 text-gray-400" />
            {getUsageDisplay()}
          </div>
        </div>
      </div>

      <div className="space-y-2 text-xs text-gray-600">
        {coupon.minCartTotal && (
          <div className="flex items-center">
            <Target className="w-3 h-3 mr-2 text-gray-400" />
            Min order: ₹{coupon.minCartTotal}
          </div>
        )}

        {coupon.startAt && (
          <div className="flex items-center">
            <Clock className="w-3 h-3 mr-2 text-gray-400" />
            Starts: {formatDate(coupon.startAt)}
          </div>
        )}

        <div className="flex items-center">
          <Calendar className="w-3 h-3 mr-2 text-gray-400" />
          Expires: {formatDate(coupon.expiresAt)}
        </div>

        {coupon.usageLimitPerUser && (
          <div className="flex items-center">
            <Users className="w-3 h-3 mr-2 text-gray-400" />
            {coupon.usageLimitPerUser} use{coupon.usageLimitPerUser > 1 ? 's' : ''} per user
          </div>
        )}
      </div>
    </Card>
  );
};
