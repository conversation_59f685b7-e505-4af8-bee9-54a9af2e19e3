import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link, useNavigate } from 'react-router-dom';
import { ArrowLeft, Upload, X, Type, FileText, Tag, BarChart2, Package, Layers, IndianRupee, AlertCircle, RefreshCw, Trash2, Image, User, Link as LinkIcon, Settings } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { Input } from '../../components/ui/Input';
import { Textarea } from '../../components/ui/Textarea';
import { Select } from '../../components/ui/Select';
import { useProducts } from '../../contexts/ProductContext';
import { Product, Category, ProductVariant } from '../../types';
import { getAllCategories } from '../../services/productService';
import { collection, getDocs, query, orderBy } from 'firebase/firestore';
import { db } from '../../firebase/config';
import { Farmer } from '../../firebase/farmers';
import { MultiCategorySelector } from '../../components/products/MultiCategorySelector';
import { VariantManager } from '../../components/products/VariantManager';

// Helper function to safely convert dates for datetime-local inputs
const formatDateForInput = (date: any): string => {
  if (!date) return '';

  try {
    // Handle Firestore timestamp
    if (date.toDate && typeof date.toDate === 'function') {
      return date.toDate().toISOString().slice(0, 16);
    }

    // Handle regular Date object or string
    return new Date(date).toISOString().slice(0, 16);
  } catch (error) {
    console.error('Error formatting date for input:', error);
    return '';
  }
};

export function ProductDetail() {
  const { id } = useParams();
  const navigate = useNavigate();
  const { products, loading, error, addProduct, updateProduct, deleteProduct, refreshProducts, uploadImage } = useProducts();
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);

  // Form state
  const [formData, setFormData] = useState<Partial<Product>>({
    name: '',
    description: '',
    category: '',
    subcategory: '',
    // New multi-category fields
    categoryIDs: [],
    primaryCategoryID: '',
    price: 0,
    mrp: 0, // Add MRP field
    stock: 0,
    unit: 'kg',
    barcode: '',
    status: 'available',
    isVisible: true,
    farmerId: '',
    nutrition: '',
    storageInstruction: '',
    // New variant fields
    hasVariants: false,
    defaultVariantID: '',
    // New harvest day and pre-order fields
    isPreOrder: false,
    preOrderStartAt: null,
    preOrderEndAt: null,
    harvestOffsetDays: 0,
  });

  const [images, setImages] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadingImage, setUploadingImage] = useState(false);
  const [localError, setLocalError] = useState<string | null>(null);
  const [imageUrlInput, setImageUrlInput] = useState('');
  const [imageInputMode, setImageInputMode] = useState<'upload' | 'url'>('upload');
  const [categories, setCategories] = useState<Category[]>([]);
  const [loadingCategories, setLoadingCategories] = useState(false);
  const [categoriesLoaded, setCategoriesLoaded] = useState(false); // Track if categories are loaded
  const [availableSubcategories, setAvailableSubcategories] = useState([]);
  const [productLoaded, setProductLoaded] = useState(false); // Track if product data has been loaded
  const [farmers, setFarmers] = useState<Farmer[]>([]);
  const [loadingFarmers, setLoadingFarmers] = useState(false);
  // New state for variant management
  const [variants, setVariants] = useState<ProductVariant[]>([]);
  const [showVariantManager, setShowVariantManager] = useState(false);

  // Improved fetchFarmers function with better error handling
  const fetchFarmers = async () => {
    console.log('Starting to fetch farmers...');
    setLoadingFarmers(true);

    try {
      // Use a query to get all farmers, ordered by name
      const farmersQuery = query(collection(db, 'Farmers'), orderBy('farmerName'));
      console.log('Executing farmers query...');

      const querySnapshot = await getDocs(farmersQuery);
      console.log(`Farmers query returned ${querySnapshot.docs.length} documents`);

      const farmersList: Farmer[] = [];

      querySnapshot.docs.forEach((doc) => {
        const data = doc.data();
        console.log(`Processing farmer document: ${doc.id}`, data);

        farmersList.push({
          id: doc.id,
          farmerName: data.farmerName || '',
          farmName: data.farmName || '',
          farmLocation: data.farmLocation || '',
          experience: data.experience || '',
          philosophy: data.philosophy || '',
          certifications: data.certifications || [],
          tags: data.tags || [],
          ...data
        } as Farmer);
      });

      console.log('Processed farmers list:', farmersList);
      setFarmers(farmersList);
    } catch (err) {
      console.error('Error fetching farmers:', err);
    } finally {
      setLoadingFarmers(false);
    }
  };

  // Fetch categories when component mounts
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoadingCategories(true);
        const fetchedCategories = await getAllCategories();
        console.log('Fetched categories with subcategories:', fetchedCategories);

        // Check if subcategories are properly structured
        fetchedCategories.forEach(cat => {
          if (cat.subcategories && cat.subcategories.length > 0) {
            console.log(`Category ${cat.name} (${cat.id}) has ${cat.subcategories.length} subcategories:`,
              cat.subcategories.map(sub => `${sub.name} (${sub.id})`).join(', '));
          } else {
            console.log(`Category ${cat.name} (${cat.id}) has no subcategories`);
          }
        });

        setCategories(fetchedCategories);

        // Mark categories as loaded
        setCategoriesLoaded(true);
        console.log('Categories loaded successfully:', fetchedCategories.length);

        // If we're editing a product and have a category set, check if subcategories should be loaded
        if (formData.category) {
          console.log('Product being edited already has category:', formData.category);
          const selectedCategory = fetchedCategories.find(cat => cat.id === formData.category);

          if (selectedCategory) {
            console.log(`Found category ${selectedCategory.name} for product`);

            if (selectedCategory.subcategories && selectedCategory.subcategories.length > 0) {
              console.log(`Setting ${selectedCategory.subcategories.length} subcategories for initial category`);
              setAvailableSubcategories(selectedCategory.subcategories);
            } else {
              console.log('Selected category has no subcategories');
            }
          } else {
            console.log('Category not found in fetched categories');
          }
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
        setLocalError('Failed to load categories. Please try again.');
        setCategoriesLoaded(false); // Mark categories as not loaded on error
      } finally {
        setLoadingCategories(false);
      }
    };

    fetchCategories();
  }, []);

  // Update available subcategories when category changes
  useEffect(() => {
    if (formData.category && categories.length > 0) {
      console.log('Category changed to:', formData.category);
      console.log('Available categories:', categories.map(cat => ({ id: cat.id, name: cat.name })));

      // Check if the selected category is a main category
      const selectedCategory = categories.find(cat => cat.id === formData.category);

      // Check if the selected category is a subcategory
      let isSubcategory = false;
      let parentCategory = null;

      if (!selectedCategory) {
        // If not found as a main category, check if it's a subcategory
        for (const category of categories) {
          if (category.subcategories) {
            const foundSubcategory = category.subcategories.find(sub => sub.id === formData.category);
            if (foundSubcategory) {
              isSubcategory = true;
              parentCategory = category;
              console.log(`Selected category (${formData.category}) is actually a subcategory of ${category.name} (${category.id})`);
              break;
            }
          }
        }
      }

      console.log('Selected category:', selectedCategory);
      console.log('Is subcategory:', isSubcategory);
      console.log('Parent category:', parentCategory);

      if (selectedCategory && selectedCategory.subcategories && selectedCategory.subcategories.length > 0) {
        // If it's a main category with subcategories
        console.log(`Found ${selectedCategory.subcategories.length} subcategories for category ${selectedCategory.name}:`,
          selectedCategory.subcategories.map(sub => ({ id: sub.id, name: sub.name })));
        setAvailableSubcategories(selectedCategory.subcategories);
      } else if (isSubcategory && parentCategory) {
        // If it's a subcategory, show sibling subcategories
        console.log(`Showing sibling subcategories for subcategory under parent ${parentCategory.name}`);
        setAvailableSubcategories(parentCategory.subcategories);
      } else {
        console.log('No subcategories found for selected category. Reasons could be:');
        console.log('- Selected category is null:', !selectedCategory);
        console.log('- Subcategories array is missing:', selectedCategory && !selectedCategory.subcategories);
        console.log('- Subcategories array is empty:', selectedCategory && selectedCategory.subcategories && selectedCategory.subcategories.length === 0);
        setAvailableSubcategories([]);
      }
    } else {
      console.log('No category selected or categories not loaded yet:', {
        categorySelected: !!formData.category,
        categoriesLoaded: categories.length > 0
      });
      setAvailableSubcategories([]);
    }
  }, [formData.category, categories]);

  // Load product data if editing - only run once when component mounts
  useEffect(() => {
    // Refresh products to ensure we have the latest data
    if (id) {
      console.log('Refreshing products for ID:', id);
      refreshProducts();
    }
  }, [id]); // Remove refreshProducts from dependencies

  // Update form when products and categories are loaded
  useEffect(() => {
    console.log('Product data loading check:', {
      hasId: !!id,
      productsLoaded: products.length > 0,
      categoriesLoaded: categoriesLoaded,
      alreadyLoaded: productLoaded
    });

    if (id && products.length > 0 && categoriesLoaded && !productLoaded) {
      console.log('Products and categories loaded, finding product with ID:', id);
      console.log('Categories count:', categories.length);

      const productToEdit = products.find(p => p.id === id);
      if (productToEdit) {
        console.log('Found product to edit:', productToEdit.name, {
          category: productToEdit.category,
          subcategory: productToEdit.subcategory,
          farmerId: productToEdit.farmerId // Log farmerId for debugging
        });

        // Debug: Log all categories to check structure
        console.log('Available categories:', categories.map(c => ({
          id: c.id,
          name: c.name,
          subcategories: c.subcategories?.map(s => ({ id: s.id, name: s.name }))
        })));

        // Log the exact category ID from the product
        console.log('Product category ID (exact value):', JSON.stringify(productToEdit.category));

        // Log all category IDs for comparison
        console.log('All category IDs:', JSON.stringify(categories.map(c => c.id)));

        // Check if the product has a valid category
        const hasValidCategory = categories.some(c => c.id === productToEdit.category);
        console.log('Product has valid main category:', hasValidCategory);

        // Check if the product's category might actually be a subcategory
        let isSubcategory = false;
        let parentCategoryId = '';

        for (const category of categories) {
          if (category.subcategories) {
            const foundSubcategory = category.subcategories.find(sub => sub.id === productToEdit.category);
            if (foundSubcategory) {
              isSubcategory = true;
              parentCategoryId = category.id;
              console.log(`Found that product's category (${productToEdit.category}) is actually a subcategory of ${category.name} (${category.id})`);
              break;
            }
          }
        }

        // Set the correct category and subcategory based on our findings
        let categoryId = productToEdit.category || '';
        let subcategoryId = productToEdit.subcategory || '';

        if (isSubcategory) {
          // If the product's "category" is actually a subcategory
          subcategoryId = categoryId;
          categoryId = parentCategoryId;
        }

        console.log('Setting form data with:', {
          categoryId,
          subcategoryId,
          isSubcategory
        });

        // Always update form data when editing a product
        const updatedFormData = {
          name: productToEdit.name,
          description: productToEdit.description,
          category: categoryId,
          subcategory: subcategoryId,
          price: productToEdit.price,
          mrp: productToEdit.mrp || 0, // Include MRP when loading product data
          stock: productToEdit.stock,
          unit: productToEdit.unit || 'kg',
          barcode: productToEdit.barcode,
          status: productToEdit.status || 'available',
          isVisible: productToEdit.isVisible,
          farmerId: productToEdit.farmerId || '', // Include farmerId when loading product data
          nutrition: productToEdit.nutrition || '', // Include nutrition data
          storageInstruction: productToEdit.storageInstruction || '', // Include storage instruction
          // New multi-category fields
          categoryIDs: productToEdit.categoryIDs || (productToEdit.category ? [productToEdit.category] : []),
          primaryCategoryID: productToEdit.primaryCategoryID || productToEdit.category || '',
          // New variant fields
          hasVariants: productToEdit.hasVariants || false,
          defaultVariantID: productToEdit.defaultVariantID || '',
          // New harvest day and pre-order fields
          isPreOrder: productToEdit.isPreOrder || false,
          preOrderStartAt: productToEdit.preOrderStartAt || null,
          preOrderEndAt: productToEdit.preOrderEndAt || null,
          harvestOffsetDays: productToEdit.harvestOffsetDays !== undefined ? productToEdit.harvestOffsetDays : 0,
        };

        console.log('Updated form data:', updatedFormData);
        setFormData(updatedFormData);

        // Force a re-render to ensure the form fields are updated
        setTimeout(() => {
          console.log('Current form data after update:', {
            category: formData.category,
            subcategory: formData.subcategory
          });
        }, 100);

        // Mark product as loaded to prevent duplicate loading
        setProductLoaded(true);

        // Load images if available
        if (productToEdit.images) {
          setImages(productToEdit.images);
        }
      }
    }
  }, [id, products, productLoaded, categories, categoriesLoaded]);

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    // Handle different input types
    if (type === 'number') {
      setFormData({
        ...formData,
        [name]: parseFloat(value) || 0
      });
    } else if (type === 'checkbox') {
      setFormData({
        ...formData,
        [name]: (e.target as HTMLInputElement).checked
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  // Handle multi-category changes
  const handleCategoriesChange = (categoryIDs: string[], primaryCategoryID?: string) => {
    setFormData({
      ...formData,
      categoryIDs,
      primaryCategoryID,
      // Update legacy category field for backward compatibility
      category: primaryCategoryID || (categoryIDs.length > 0 ? categoryIDs[0] : '')
    });
  };

  // Handle variant changes
  const handleVariantsChange = (updatedVariants: ProductVariant[]) => {
    setVariants(updatedVariants);

    // If there are variants and no default is set, set the first one as default
    if (updatedVariants.length > 0 && !formData.defaultVariantID) {
      setFormData({
        ...formData,
        defaultVariantID: updatedVariants[0].id
      });
    }
  };

  // Toggle variant system
  const handleToggleVariants = (hasVariants: boolean) => {
    setFormData({
      ...formData,
      hasVariants
    });

    if (hasVariants) {
      setShowVariantManager(true);
    } else {
      setShowVariantManager(false);
      setVariants([]);
      setFormData(prev => ({
        ...prev,
        defaultVariantID: ''
      }));
    }
  };

  // Handle image upload
  const handleImageUpload = async (file: File) => {
    if (!file) return;

    try {
      setUploadingImage(true);
      setLocalError(null);

      // Create a temporary URL for preview
      const previewUrl = URL.createObjectURL(file);
      setImages([...images, previewUrl]);

      // If we're editing an existing product, upload the image to Firebase
      if (id) {
        const imageUrl = await uploadImage(file, id);
        if (imageUrl) {
          // Replace the temporary URL with the actual Firebase URL
          setImages(prev => prev.map(url => url === previewUrl ? imageUrl : url));
        } else {
          // Remove the temporary URL if upload failed
          setImages(prev => prev.filter(url => url !== previewUrl));
          setLocalError('Failed to upload image. Please try again.');
        }
      }
    } catch (err) {
      console.error('Error uploading image:', err);
      setLocalError('Failed to upload image. Please try again.');
    } finally {
      setUploadingImage(false);
    }
  };

  // Handle adding image URL
  const handleAddImageUrl = () => {
    if (!imageUrlInput) return;

    try {
      setUploadingImage(true);
      setLocalError(null);

      // Basic validation - ensure URL starts with http:// or https://
      if (!imageUrlInput.startsWith('http://') && !imageUrlInput.startsWith('https://')) {
        setLocalError('Please enter a valid URL (must start with http:// or https://)');
        setUploadingImage(false);
        return;
      }

      // Add the URL to images array
      setImages([...images, imageUrlInput]);

      // Clear the input field
      setImageUrlInput('');
      setLocalError(null);

    } catch (err) {
      console.error('Error adding image URL:', err);
      setLocalError('Failed to add image URL. Please try again.');
    } finally {
      setUploadingImage(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setLocalError(null);

    try {
      // Log the farmerId before submission
      console.log('Submitting product with farmerId:', formData.farmerId);

      const productData = {
        ...formData,
        images,
        farmerId: formData.farmerId || null,
        // Ensure new fields are included
        categoryIDs: formData.categoryIDs || [],
        primaryCategoryID: formData.primaryCategoryID || '',
        hasVariants: formData.hasVariants || false,
        defaultVariantID: formData.defaultVariantID || '',
      } as Omit<Product, 'id' | 'createdAt' | 'updatedAt'>;

      console.log('Product data being submitted:', productData);

      let success = false;

      if (id) {
        // Update existing product
        console.log('Updating existing product with ID:', id);
        success = await updateProduct(id, productData);
      } else {
        // Add new product
        console.log('Adding new product');
        const newId = await addProduct(productData);
        success = !!newId;
      }

      if (success) {
        // Redirect to products list
        navigate('/products');
      } else {
        setLocalError('Failed to save product. Please try again.');
      }
    } catch (err) {
      console.error('Error saving product:', err);
      setLocalError('An unexpected error occurred. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle delete product
  const handleDeleteProduct = async () => {
    if (!id) return;

    try {
      setIsSubmitting(true);
      setLocalError(null);
      const success = await deleteProduct(id);

      if (success) {
        // Redirect to products list
        navigate('/products');
      } else {
        setLocalError('Failed to delete product. Please try again.');
        setShowDeleteConfirmation(false);
      }
    } catch (err) {
      console.error('Error deleting product:', err);
      setLocalError('An unexpected error occurred. Please try again.');
      setShowDeleteConfirmation(false);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Make sure this useEffect runs when the component mounts
  useEffect(() => {
    console.log('ProductDetail component mounted, fetching farmers...');
    fetchFarmers();

    // Keep any other initialization code that was already here
  }, []);

  return (
    <div className="p-6">
      <div className="flex items-center gap-4 mb-6">
        <Link to="/products">
          <Button variant="outline" size="sm">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Products
          </Button>
        </Link>
        <h1 className="text-2xl font-bold text-gray-900">
          {id ? 'Edit Product' : 'New Product'}
        </h1>
      </div>

      {/* Loading state */}
      {(loading || loadingCategories || (id && !categoriesLoaded)) && (
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg flex items-center text-blue-700">
          <RefreshCw className="w-5 h-5 mr-2 animate-spin" />
          <span>
            {loading ? 'Loading product data...' :
             loadingCategories ? 'Loading categories...' :
             'Preparing form data...'}
          </span>
        </div>
      )}

      {/* Error message */}
      {(error || localError) && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center text-red-700">
          <AlertCircle className="w-5 h-5 mr-2" />
          <span>{error || localError}</span>
        </div>
      )}

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <div className="p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Product Information</h2>
            <form className="space-y-4" onSubmit={handleSubmit}>
              <Input
                id="name"
                name="name"
                label="Product Name"
                type="text"
                placeholder="Enter product name"
                required
                startIcon={<Type className="h-4 w-4 text-gray-400" />}
                value={formData.name}
                onChange={handleInputChange}
              />

              {/* Multi-Category Selector */}
              <MultiCategorySelector
                selectedCategoryIDs={formData.categoryIDs || []}
                primaryCategoryID={formData.primaryCategoryID}
                onCategoriesChange={handleCategoriesChange}
                label="Categories"
                required
              />

              <Textarea
                id="description"
                name="description"
                label="Description"
                rows={4}
                placeholder="Enter product description"
                startIcon={<FileText className="h-4 w-4 text-gray-400" />}
                value={formData.description}
                onChange={handleInputChange}
              />

              <div className="space-y-2">
                <label htmlFor="farmerId" className="block text-sm font-medium text-gray-700">
                  Farmer
                </label>
                <div className="relative">
                  {loadingFarmers && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      <RefreshCw className="h-4 w-4 text-gray-400 animate-spin" />
                    </div>
                  )}
                  <div className="flex items-center">
                    <select
                      id="farmerId"
                      name="farmerId"
                      className="block w-full py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                      value={formData.farmerId || ""}
                      onChange={handleInputChange}
                      disabled={loadingFarmers}
                    >
                      <option value="">Select a farmer (optional)</option>
                      {farmers.map(farmer => (
                        <option key={farmer.id} value={farmer.id}>
                          {farmer.farmerName} ({farmer.farmName})
                        </option>
                      ))}
                    </select>
                  </div>
                  <p className="mt-1 text-sm text-gray-500">
                    {loadingFarmers
                      ? "Loading farmers..."
                      : farmers.length === 0
                        ? "No farmers available. Add farmers first."
                        : "Select the farmer who produces this product"}
                  </p>
                </div>
              </div>

              {/* For debugging - remove this in production */}
              <div className="text-xs text-gray-400 mt-1">
                {farmers.length > 0
                  ? `${farmers.length} farmers loaded`
                  : "No farmers loaded"}
              </div>
            </form>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Pricing & Inventory</h2>
            <form className="space-y-4">
              <div className="grid grid-cols-3 gap-4">
                <Input
                  id="price"
                  name="price"
                  label="Selling Price (₹)"
                  type="number"
                  placeholder="0.00"
                  step="0.01"
                  required
                  value={formData.price?.toString()}
                  onChange={handleInputChange}
                />

                <Input
                  id="mrp"
                  name="mrp"
                  label="MRP (₹)"
                  type="number"
                  placeholder="0.00"
                  step="0.01"
                  value={formData.mrp?.toString()}
                  onChange={handleInputChange}
                />

                <Select
                  id="unit"
                  name="unit"
                  label="Unit"
                  required
                  options={[
                    { value: "kg", label: "Kilogram (kg)" },
                    { value: "g", label: "Gram (g)" },
                    { value: "piece", label: "Piece" },
                    { value: "bundle", label: "Bundle" }
                  ]}
                  value={formData.unit}
                  onChange={handleInputChange}
                />
              </div>

              <Input
                id="barcode"
                name="barcode"
                label="Barcode"
                type="text"
                placeholder="Enter Barcode"
                required
                startIcon={<Tag className="h-4 w-4 text-gray-400" />}
                value={formData.barcode}
                onChange={handleInputChange}
              />

              <Input
                id="stock"
                name="stock"
                label="Stock Quantity"
                type="number"
                min="0"
                placeholder="Enter stock quantity"
                required
                startIcon={<Package className="h-4 w-4 text-gray-400" />}
                value={formData.stock?.toString()}
                onChange={handleInputChange}
              />

              <Select
                id="status"
                name="status"
                label="Status"
                required
                options={[
                  { value: "available", label: "Available" },
                  { value: "out_of_stock", label: "Out of Stock" },
                  { value: "coming_soon", label: "Coming Soon" }
                ]}
                value={formData.status}
                onChange={handleInputChange}
              />

              <Textarea
                id="nutrition"
                name="nutrition"
                label="Nutrition Details"
                rows={3}
                placeholder="Enter nutrition information"
                value={formData.nutrition || ''}
                onChange={handleInputChange}
              />

              <Textarea
                id="storageInstruction"
                name="storageInstruction"
                label="Storage Instructions (Optional)"
                rows={3}
                placeholder="Enter storage instructions"
                value={formData.storageInstruction || ''}
                onChange={handleInputChange}
              />

              {/* Variant System Toggle */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Product Variants</label>
                    <p className="text-sm text-gray-500">Enable variants for different sizes, colors, etc.</p>
                  </div>
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="hasVariants"
                      name="hasVariants"
                      checked={formData.hasVariants || false}
                      onChange={(e) => handleToggleVariants(e.target.checked)}
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    />
                    <label htmlFor="hasVariants" className="ml-2 text-sm text-gray-700">
                      Enable Variants
                    </label>
                  </div>
                </div>
              </div>

              {/* Pre-Order System */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Pre-Order</label>
                    <p className="text-sm text-gray-500">Enable pre-order for this product</p>
                  </div>
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="isPreOrder"
                      name="isPreOrder"
                      checked={formData.isPreOrder || false}
                      onChange={(e) => setFormData(prev => ({ ...prev, isPreOrder: e.target.checked }))}
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    />
                    <label htmlFor="isPreOrder" className="ml-2 text-sm text-gray-700">
                      Enable Pre-Order
                    </label>
                  </div>
                </div>

                {/* Pre-Order Date Range (optional) */}
                {formData.isPreOrder && (
                  <div className="grid grid-cols-2 gap-4 mt-4">
                    <Input
                      id="preOrderStartAt"
                      name="preOrderStartAt"
                      label="Pre-Order Start Date (Optional)"
                      type="datetime-local"
                      value={formatDateForInput(formData.preOrderStartAt)}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        preOrderStartAt: e.target.value ? new Date(e.target.value) : null
                      }))}
                    />
                    <Input
                      id="preOrderEndAt"
                      name="preOrderEndAt"
                      label="Pre-Order End Date (Optional)"
                      type="datetime-local"
                      value={formatDateForInput(formData.preOrderEndAt)}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        preOrderEndAt: e.target.value ? new Date(e.target.value) : null
                      }))}
                    />
                  </div>
                )}
              </div>

              {/* Harvest Day Setting */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Harvest Day</label>
                  <p className="text-sm text-gray-500 mb-3">Set when this product is harvested relative to today</p>
                  <div className="grid grid-cols-3 gap-3">
                    <button
                      type="button"
                      onClick={() => setFormData(prev => ({ ...prev, harvestOffsetDays: -1 }))}
                      className={`p-3 border rounded-lg text-center transition-colors ${
                        formData.harvestOffsetDays === -1
                          ? 'border-primary-500 bg-primary-50 text-primary-700'
                          : 'border-gray-300 hover:border-gray-400'
                      }`}
                    >
                      <div className="font-medium">Yesterday</div>
                      <div className="text-xs text-gray-500">Harvested yesterday</div>
                    </button>
                    <button
                      type="button"
                      onClick={() => setFormData(prev => ({ ...prev, harvestOffsetDays: 0 }))}
                      className={`p-3 border rounded-lg text-center transition-colors ${
                        formData.harvestOffsetDays === 0
                          ? 'border-primary-500 bg-primary-50 text-primary-700'
                          : 'border-gray-300 hover:border-gray-400'
                      }`}
                    >
                      <div className="font-medium">Today</div>
                      <div className="text-xs text-gray-500">Harvested today</div>
                    </button>
                    <button
                      type="button"
                      onClick={() => setFormData(prev => ({ ...prev, harvestOffsetDays: 1 }))}
                      className={`p-3 border rounded-lg text-center transition-colors ${
                        formData.harvestOffsetDays === 1
                          ? 'border-primary-500 bg-primary-50 text-primary-700'
                          : 'border-gray-300 hover:border-gray-400'
                      }`}
                    >
                      <div className="font-medium">Tomorrow</div>
                      <div className="text-xs text-gray-500">Will be harvested tomorrow</div>
                    </button>
                  </div>
                  <div className="mt-2 text-sm text-gray-600">
                    Current selection: <span className="font-medium">
                      {formData.harvestOffsetDays === -1 && 'Harvested Yesterday'}
                      {formData.harvestOffsetDays === 0 && 'Harvested Today'}
                      {formData.harvestOffsetDays === 1 && 'Will be Harvested Tomorrow'}
                    </span>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </Card>

        <div className="md:col-span-2">
          <Card>
            <div className="p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Product Images</h2>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {images.map((image, index) => (
                  <div key={index} className="relative aspect-square rounded-lg border-2 border-gray-300 overflow-hidden">
                    <img src={image} alt={`Product ${index + 1}`} className="w-full h-full object-cover" />
                    <button
                      onClick={() => setImages(images.filter((_, i) => i !== index))}
                      className="absolute top-2 right-2 p-1 bg-white rounded-full shadow-md hover:bg-gray-100"
                    >
                      <X className="w-4 h-4 text-gray-600" />
                    </button>
                  </div>
                ))}
                <div className="aspect-square rounded-lg border-2 border-dashed border-gray-300 flex flex-col items-center justify-center hover:border-primary-500">
                  <div className="flex flex-col gap-4 w-full p-4">
                    {/* Toggle between URL and file upload */}
                    <div className="flex gap-2 mb-2">
                      <Button
                        variant={imageInputMode === 'url' ? 'primary' : 'outline'}
                        size="sm"
                        onClick={() => setImageInputMode('url')}
                      >
                        URL
                      </Button>
                      <Button
                        variant={imageInputMode === 'upload' ? 'primary' : 'outline'}
                        size="sm"
                        onClick={() => setImageInputMode('upload')}
                      >
                        Upload
                      </Button>
                    </div>

                    {imageInputMode === 'url' ? (
                      <>
                        <Input
                          label="Image URL"
                          type="text"
                          value={imageUrlInput}
                          onChange={(e) => setImageUrlInput(e.target.value)}
                          startIcon={<Image className="h-4 w-4 text-gray-400" />}
                          placeholder="Enter image URL"
                          disabled={uploadingImage}
                          helperText="Paste a direct link to an image (must start with http:// or https://)"
                        />
                        <div className="flex justify-end space-x-2 mt-2 w-full">
                          <Button
                            variant="primary"
                            size="sm"
                            onClick={handleAddImageUrl}
                            disabled={uploadingImage || !imageUrlInput}
                          >
                            {uploadingImage ? (
                              <span className="flex items-center justify-center">
                                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                                Adding...
                              </span>
                            ) : (
                              'Add Image'
                            )}
                          </Button>
                        </div>
                      </>
                    ) : (
                      <>
                        <Input
                          label="Upload Image"
                          type="file"
                          accept="image/*"
                          onChange={(e) => e.target.files && e.target.files[0] && handleImageUpload(e.target.files[0])}
                          startIcon={<Upload className="h-4 w-4 text-gray-400" />}
                          disabled={uploadingImage}
                          helperText="Select an image file to upload"
                        />
                        {uploadingImage && (
                          <div className="flex items-center justify-center mt-2">
                            <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                            <span>Uploading...</span>
                          </div>
                        )}
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </div>

        {/* Variant Manager */}
        {formData.hasVariants && (
          <div className="md:col-span-2">
            <Card>
              <div className="p-6">
                {id ? (
                  <VariantManager
                    productId={id}
                    onVariantsChange={handleVariantsChange}
                  />
                ) : (
                  <div className="text-center py-8">
                    <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h4 className="text-lg font-medium text-gray-900 mb-2">Save Product First</h4>
                    <p className="text-gray-600">
                      Please save the product first to enable variant management.
                    </p>
                  </div>
                )}
              </div>
            </Card>
          </div>
        )}

        <div className="md:col-span-2 flex justify-between gap-4">
          {id && (
            <Button
              variant="danger"
              onClick={() => setShowDeleteConfirmation(true)}
              disabled={isSubmitting}
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Delete Product
            </Button>
          )}
          <div className="flex gap-4">
            <Button variant="outline" as={Link} to="/products">
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={handleSubmit}
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : (id ? 'Update Product' : 'Save Product')}
            </Button>
          </div>
        </div>

        {/* Delete Confirmation Dialog */}
        {showDeleteConfirmation && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl p-6 max-w-md w-full">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Confirm Delete</h3>
              <p className="text-gray-700 mb-6">
                Are you sure you want to delete this product? This action cannot be undone.
              </p>
              <div className="flex justify-end gap-4">
                <Button
                  variant="outline"
                  onClick={() => setShowDeleteConfirmation(false)}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button
                  variant="danger"
                  onClick={handleDeleteProduct}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Deleting...' : 'Delete Product'}
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
