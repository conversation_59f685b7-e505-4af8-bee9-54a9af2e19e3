import { 
  addDeliveryCharge, 
  getDeliveryCharges, 
  getDeliveryChargeByPincode,
  searchDeliveryCharges 
} from '../services/deliveryChargeService';
import { DeliveryCharge } from '../types';

const createTestDeliveryCharges = async () => {
  try {
    console.log('Creating test delivery charges...');

    // Test delivery charge 1: Free delivery area
    const freeDelivery: Omit<DeliveryCharge, 'id' | 'createdAt' | 'updatedAt'> = {
      pincode: '400001',
      areaName: 'Mumbai Central',
      branchCode: 'test',
      branchName: 'test default',
      deliveryCharge: 0
    };

    // Test delivery charge 2: Standard delivery
    const standardDelivery: Omit<DeliveryCharge, 'id' | 'createdAt' | 'updatedAt'> = {
      pincode: '400002',
      areaName: 'Andheri West',
      branchCode: 'test',
      branchName: 'test default',
      deliveryCharge: 50
    };

    // Test delivery charge 3: Premium delivery
    const premiumDelivery: Omit<DeliveryCharge, 'id' | 'createdAt' | 'updatedAt'> = {
      pincode: '400003',
      areaName: 'Bandra East',
      branchCode: 'test',
      branchName: 'test default',
      deliveryCharge: 100
    };

    // Test delivery charge 4: High cost area
    const highCostDelivery: Omit<DeliveryCharge, 'id' | 'createdAt' | 'updatedAt'> = {
      pincode: '400004',
      areaName: 'Juhu Beach',
      branchCode: 'test',
      branchName: 'test default',
      deliveryCharge: 150
    };

    // Test delivery charge 5: Different branch
    const differentBranch: Omit<DeliveryCharge, 'id' | 'createdAt' | 'updatedAt'> = {
      pincode: '110001',
      areaName: 'Connaught Place',
      branchCode: 'delhi',
      branchName: 'Delhi Branch',
      deliveryCharge: 75
    };

    // Add the delivery charges
    const result1 = await addDeliveryCharge(freeDelivery);
    console.log('Free delivery area created:', result1);

    const result2 = await addDeliveryCharge(standardDelivery);
    console.log('Standard delivery area created:', result2);

    const result3 = await addDeliveryCharge(premiumDelivery);
    console.log('Premium delivery area created:', result3);

    const result4 = await addDeliveryCharge(highCostDelivery);
    console.log('High cost delivery area created:', result4);

    const result5 = await addDeliveryCharge(differentBranch);
    console.log('Different branch area created:', result5);

    // Test getting delivery charge by pincode
    console.log('\nTesting delivery charge lookup...');
    
    const lookup1 = await getDeliveryChargeByPincode('400001');
    console.log('Lookup 400001:', lookup1 ? `₹${lookup1.deliveryCharge} for ${lookup1.areaName}` : 'Not found');

    const lookup2 = await getDeliveryChargeByPincode('400002');
    console.log('Lookup 400002:', lookup2 ? `₹${lookup2.deliveryCharge} for ${lookup2.areaName}` : 'Not found');

    const lookup3 = await getDeliveryChargeByPincode('999999');
    console.log('Lookup 999999 (non-existent):', lookup3 ? `₹${lookup3.deliveryCharge} for ${lookup3.areaName}` : 'Not found');

    // Test search functionality
    console.log('\nTesting search functionality...');
    
    const searchMumbai = await searchDeliveryCharges('mumbai');
    console.log('Search "mumbai":', searchMumbai.length, 'results');

    const searchBandra = await searchDeliveryCharges('bandra');
    console.log('Search "bandra":', searchBandra.length, 'results');

    const search400 = await searchDeliveryCharges('400');
    console.log('Search "400":', search400.length, 'results');

    // List all delivery charges
    console.log('\nListing all delivery charges...');
    const allDeliveryCharges = await getDeliveryCharges(10);
    console.log('Total delivery charges:', allDeliveryCharges.deliveryCharges.length);
    
    allDeliveryCharges.deliveryCharges.forEach(charge => {
      console.log(`- ${charge.pincode}: ${charge.areaName} - ₹${charge.deliveryCharge}`);
    });

    // Test delivery charge categories
    console.log('\nDelivery charge categories:');
    allDeliveryCharges.deliveryCharges.forEach(charge => {
      let category = '';
      if (charge.deliveryCharge === 0) {
        category = 'Free Delivery';
      } else if (charge.deliveryCharge <= 50) {
        category = 'Low Cost';
      } else if (charge.deliveryCharge <= 100) {
        category = 'Standard';
      } else {
        category = 'Premium';
      }
      console.log(`- ${charge.pincode} (${charge.areaName}): ${category}`);
    });

  } catch (error) {
    console.error('Error creating test delivery charges:', error);
  }
};

// Run the test if this file is executed directly
if (typeof window === 'undefined') {
  createTestDeliveryCharges();
}

export { createTestDeliveryCharges };
