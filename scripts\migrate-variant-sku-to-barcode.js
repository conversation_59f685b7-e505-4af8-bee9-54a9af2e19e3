/**
 * Migration Script: Rename 'sku' field to 'barcode' in Product Variants
 * 
 * This script migrates existing product variants in the Firebase Firestore database
 * by renaming the 'sku' field to 'barcode' in all variant documents.
 * 
 * Path: /Products/{productId}/Variants/{variantId}
 * 
 * Usage:
 * 1. Install Firebase Admin SDK: npm install firebase-admin
 * 2. Set up Firebase service account credentials
 * 3. Run: node scripts/migrate-variant-sku-to-barcode.js
 */

const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
// Replace with your service account key path
const serviceAccount = require('./path/to/your/serviceAccountKey.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  databaseURL: 'https://your-project-id.firebaseio.com'
});

const db = admin.firestore();

async function migrateVariantSkuToBarcode() {
  console.log('Starting migration: SKU → Barcode in Product Variants...');
  
  let totalProducts = 0;
  let totalVariants = 0;
  let migratedVariants = 0;
  let errors = 0;

  try {
    // Get all products
    const productsSnapshot = await db.collection('Products').get();
    totalProducts = productsSnapshot.size;
    
    console.log(`Found ${totalProducts} products to check for variants...`);

    // Process each product
    for (const productDoc of productsSnapshot.docs) {
      const productId = productDoc.id;
      console.log(`\nProcessing product: ${productId}`);

      try {
        // Get all variants for this product
        const variantsSnapshot = await db
          .collection('Products')
          .doc(productId)
          .collection('Variants')
          .get();

        if (variantsSnapshot.empty) {
          console.log(`  No variants found for product ${productId}`);
          continue;
        }

        totalVariants += variantsSnapshot.size;
        console.log(`  Found ${variantsSnapshot.size} variants`);

        // Process each variant
        for (const variantDoc of variantsSnapshot.docs) {
          const variantId = variantDoc.id;
          const variantData = variantDoc.data();

          try {
            // Check if 'sku' field exists and 'barcode' field doesn't exist
            if (variantData.sku && !variantData.barcode) {
              console.log(`    Migrating variant ${variantId}: ${variantData.sku}`);

              // Create update object
              const updateData = {
                barcode: variantData.sku,
                updatedAt: admin.firestore.FieldValue.serverTimestamp()
              };

              // Update the document with new field
              await variantDoc.ref.update(updateData);

              // Remove the old 'sku' field
              await variantDoc.ref.update({
                sku: admin.firestore.FieldValue.delete()
              });

              migratedVariants++;
              console.log(`    ✓ Successfully migrated variant ${variantId}`);

            } else if (variantData.barcode && variantData.sku) {
              // Both fields exist - remove sku field only
              console.log(`    Removing duplicate sku field from variant ${variantId}`);
              await variantDoc.ref.update({
                sku: admin.firestore.FieldValue.delete(),
                updatedAt: admin.firestore.FieldValue.serverTimestamp()
              });
              migratedVariants++;

            } else if (variantData.barcode && !variantData.sku) {
              console.log(`    Variant ${variantId} already has barcode field`);

            } else {
              console.log(`    ⚠️ Variant ${variantId} has no sku or barcode field`);
            }

          } catch (variantError) {
            console.error(`    ❌ Error migrating variant ${variantId}:`, variantError);
            errors++;
          }
        }

      } catch (productError) {
        console.error(`❌ Error processing product ${productId}:`, productError);
        errors++;
      }
    }

    // Migration summary
    console.log('\n' + '='.repeat(60));
    console.log('MIGRATION SUMMARY');
    console.log('='.repeat(60));
    console.log(`Total products checked: ${totalProducts}`);
    console.log(`Total variants found: ${totalVariants}`);
    console.log(`Variants migrated: ${migratedVariants}`);
    console.log(`Errors encountered: ${errors}`);
    console.log('='.repeat(60));

    if (errors === 0) {
      console.log('✅ Migration completed successfully!');
    } else {
      console.log(`⚠️ Migration completed with ${errors} errors. Please check the logs above.`);
    }

  } catch (error) {
    console.error('❌ Fatal error during migration:', error);
    process.exit(1);
  }
}

// Dry run function to preview changes without making them
async function dryRunMigration() {
  console.log('DRY RUN: Preview of SKU → Barcode migration...');
  
  let totalProducts = 0;
  let totalVariants = 0;
  let variantsToMigrate = 0;

  try {
    const productsSnapshot = await db.collection('Products').get();
    totalProducts = productsSnapshot.size;
    
    console.log(`Found ${totalProducts} products to check...`);

    for (const productDoc of productsSnapshot.docs) {
      const productId = productDoc.id;
      
      const variantsSnapshot = await db
        .collection('Products')
        .doc(productId)
        .collection('Variants')
        .get();

      if (!variantsSnapshot.empty) {
        totalVariants += variantsSnapshot.size;
        
        for (const variantDoc of variantsSnapshot.docs) {
          const variantData = variantDoc.data();
          
          if (variantData.sku && !variantData.barcode) {
            variantsToMigrate++;
            console.log(`  Would migrate: ${productId}/Variants/${variantDoc.id} (${variantData.sku})`);
          } else if (variantData.barcode && variantData.sku) {
            variantsToMigrate++;
            console.log(`  Would remove sku field: ${productId}/Variants/${variantDoc.id}`);
          }
        }
      }
    }

    console.log('\n' + '='.repeat(60));
    console.log('DRY RUN SUMMARY');
    console.log('='.repeat(60));
    console.log(`Total products: ${totalProducts}`);
    console.log(`Total variants: ${totalVariants}`);
    console.log(`Variants to migrate: ${variantsToMigrate}`);
    console.log('='.repeat(60));

  } catch (error) {
    console.error('❌ Error during dry run:', error);
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--dry-run')) {
    await dryRunMigration();
  } else if (args.includes('--execute')) {
    await migrateVariantSkuToBarcode();
  } else {
    console.log('Usage:');
    console.log('  node migrate-variant-sku-to-barcode.js --dry-run    # Preview changes');
    console.log('  node migrate-variant-sku-to-barcode.js --execute    # Execute migration');
    console.log('');
    console.log('⚠️  IMPORTANT: Always run --dry-run first to preview changes!');
  }
  
  process.exit(0);
}

main().catch(console.error);
