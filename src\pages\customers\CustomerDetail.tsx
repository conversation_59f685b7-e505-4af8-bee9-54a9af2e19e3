import { useState, useEffect, useCallback, useRef } from 'react';
import { useParams, Link, useNavigate } from 'react-router-dom';
import { ArrowLeft, Mail, Phone, MapPin, Edit2, Trash2, Package, Calendar, CreditCard, User, Plus, RefreshCw, AlertCircle } from 'lucide-react';
import { Card } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { Badge } from '../../components/ui/Badge';
import { OrderStatusBadge } from '../../components/ui/StatusBadge';
import { Customer } from '../../types';
import { Order } from '../../types/index';
import { AddCustomerForm } from '../../components/customers/AddCustomerForm';
import { Modal } from '../../components/ui/Modal';
import { useCustomers } from '../../contexts/CustomerContext';
import { getOrdersByCustomerId, getCustomerOrderStats } from '../../services/orderService';
import { doc, updateDoc } from 'firebase/firestore';
import { db } from '../../firebase';

// Helper function to format phone numbers
const formatPhoneNumber = (phone: string) => {
  if (!phone) return 'N/A';

  // If phone number is already formatted, return it
  if (phone.includes(' ') || phone.includes('-')) return phone;

  // If it's an international number starting with +
  if (phone.startsWith('+')) {
    // For Indian numbers (+91XXXXXXXXXX)
    if (phone.startsWith('+91') && phone.length === 13) {
      return `+91 ${phone.substring(3, 8)} ${phone.substring(8)}`;
    }
    // Generic international format
    return phone.replace(/(\+\d{2})(\d{5})(\d+)/, '$1 $2 $3');
  }

  // For 10-digit numbers (most common case)
  if (phone.length === 10) {
    return phone.replace(/(\d{5})(\d{5})/, '$1 $2');
  }

  return phone;
};

// Helper function to format dates
const formatDate = (dateInput: string | undefined | null | any) => {
  if (!dateInput) return 'N/A';

  try {
    let date;

    // Check if the date is a Firestore Timestamp object
    if (dateInput && typeof dateInput === 'object' && 'seconds' in dateInput && 'nanoseconds' in dateInput) {
      // Convert Firestore Timestamp to JavaScript Date
      date = new Date(dateInput.seconds * 1000);
    } else if (typeof dateInput === 'string') {
      // If it's a string, create a Date object
      date = new Date(dateInput);
    } else {
      // If it's already a Date object or something else
      date = new Date(dateInput);
    }

    return new Intl.DateTimeFormat('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  } catch (e) {
    console.error('Error formatting date:', e, dateInput);
    return 'Invalid Date';
  }
};

// Helper function to format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
};

export function CustomerDetail() {
  const { id } = useParams();
  const navigate = useNavigate();
  const { getCustomer, updateCustomer, deleteCustomer, loading, error } = useCustomers();

  // Add a ref to track if we're currently updating the customer
  const isUpdatingCustomer = useRef(false);

  const [customer, setCustomer] = useState<Customer | null>(null);
  const [orders, setOrders] = useState<Order[]>([]);
  const [orderStats, setOrderStats] = useState<{ totalOrders: number, totalSpent: number, lastOrderDate: string | null }>({
    totalOrders: 0,
    totalSpent: 0,
    lastOrderDate: null
  });
  const [activeTab, setActiveTab] = useState<'overview' | 'orders' | 'addresses'>('overview');
  const [showEditForm, setShowEditForm] = useState(false);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [localLoading, setLocalLoading] = useState(true);
  const [ordersLoading, setOrdersLoading] = useState(false);
  const [localError, setLocalError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  // Fetch customer data
  const fetchCustomer = useCallback(async () => {
    if (id) {
      try {
        setLocalLoading(true);
        setLocalError(null);

        // Get customer data
        const customerData = await getCustomer(id);
        if (customerData) {
          console.log('Fetched customer data:', JSON.stringify(customerData, null, 2));
          setCustomer(customerData);

          // Fetch customer orders
          setOrdersLoading(true); // Set orders loading state
          console.log(`Fetching orders for customer ID: ${id}`);

          // Get orders for this customer
          const customerOrders = await getOrdersByCustomerId(id);

          console.log(`Fetched ${customerOrders.length} orders for customer:`,
            customerOrders.map(o => ({
              id: o.id,
              total: o.total,
              status: o.status,
              items: o.items.length,
              createdAt: o.createdAt
            }))
          );

          setOrders(customerOrders);
          setOrdersLoading(false); // Clear orders loading state

          // Calculate order statistics from the fetched orders
          let stats;
          if (customerOrders.length > 0) {
            // Calculate stats directly from orders
            const totalOrders = customerOrders.length;
            const totalSpent = customerOrders.reduce((sum, order) => sum + order.total, 0);

            // Sort orders by date (newest first)
            const sortedOrders = [...customerOrders].sort(
              (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
            );

            const lastOrderDate = sortedOrders.length > 0 ? sortedOrders[0].createdAt : null;

            stats = { totalOrders, totalSpent, lastOrderDate };
          } else {
            // If no orders, use the API to get stats
            stats = await getCustomerOrderStats(id);
          }

          console.log('Calculated order stats:', stats);
          setOrderStats(stats);

          // Only update customer with calculated stats if they're missing AND there's a significant difference
          // This prevents unnecessary updates that could cause re-renders
          const shouldUpdateStats =
            (customerData.totalOrders === 0 && stats.totalOrders > 0) ||
            (Math.abs((customerData.totalSpent || 0) - (stats.totalSpent || 0)) > 10) ||
            (!customerData.lastOrderDate && stats.lastOrderDate);

          // Update the local state with the calculated stats regardless
          const updatedCustomer = {
            ...customerData,
            totalOrders: stats.totalOrders,
            totalSpent: stats.totalSpent,
            lastOrderDate: stats.lastOrderDate || undefined
          };
          setCustomer(updatedCustomer);

          // Only update the database if there's a significant difference
          if (shouldUpdateStats && !isUpdatingCustomer.current) {
            console.log('Updating customer with calculated stats in database');

            // Use a flag to prevent multiple updates
            isUpdatingCustomer.current = true;

            try {
              // Update in database without triggering a refresh
              const customerRef = doc(db, 'Users', id);
              await updateDoc(customerRef, {
                totalOrders: stats.totalOrders,
                totalSpent: stats.totalSpent,
                lastOrderDate: stats.lastOrderDate ? new Date(stats.lastOrderDate) : null
              });
              console.log('Successfully updated customer stats in database');
            } catch (updateError) {
              console.error('Error updating customer stats:', updateError);
            } finally {
              isUpdatingCustomer.current = false;
            }
          }
        } else {
          setLocalError('Customer not found');
          setTimeout(() => navigate('/customers'), 2000);
        }
      } catch (err) {
        console.error('Error fetching customer:', err);
        setLocalError('Failed to load customer data');
      } finally {
        setLocalLoading(false);
        setRefreshing(false);
      }
    }
  }, [id, getCustomer, updateCustomer, navigate]);

  useEffect(() => {
    // Only fetch if we're not already updating
    if (!isUpdatingCustomer.current) {
      fetchCustomer();
    }
  }, [fetchCustomer]);

  // Handle refresh
  const handleRefresh = () => {
    setRefreshing(true);
    fetchCustomer();
  };

  // Render customer stats section
  const renderCustomerStats = () => {
    if (!customer) return null;

    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <div className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-primary-100 rounded-full mr-3">
                <Package className="w-5 h-5 text-primary-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Total Orders</p>
                <p className="text-xl font-semibold text-gray-900">{customer.totalOrders || 0}</p>
              </div>
            </div>
          </div>
        </Card>
        <Card>
          <div className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-full mr-3">
                <CreditCard className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Total Spent</p>
                <p className="text-xl font-semibold text-gray-900">{formatCurrency(customer.totalSpent || 0)}</p>
              </div>
            </div>
          </div>
        </Card>
        <Card>
          <div className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-full mr-3">
                <Calendar className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Last Order</p>
                <p className="text-xl font-semibold text-gray-900">
                  {customer.lastOrderDate ? formatDate(customer.lastOrderDate) : 'Never'}
                </p>
              </div>
            </div>
          </div>
        </Card>
      </div>
    );
  };

  // Handle delete customer
  const handleDeleteCustomer = async () => {
    if (!customer || !id) return;

    try {
      setLocalLoading(true);
      setLocalError(null);
      const success = await deleteCustomer(id);

      if (success) {
        navigate('/customers');
      } else {
        setLocalError('Failed to delete customer. Please try again.');
        setShowDeleteConfirmation(false);
      }
    } catch (err) {
      console.error('Error deleting customer:', err);
      setLocalError('An unexpected error occurred. Please try again.');
      setShowDeleteConfirmation(false);
    } finally {
      setLocalLoading(false);
    }
  };

  // Handle create new order
  const handleCreateNewOrder = () => {
    // Navigate to POS page with customer ID
    navigate(`/pos?customerId=${id}`);
  };

  // Function to handle saving customer
  const handleSaveCustomer = async (updatedCustomer: Customer) => {
    if (!customer || !id) return;

    try {
      setLocalLoading(true);
      setLocalError(null);

      console.log('Saving updated customer data:', updatedCustomer);

      // Make sure we preserve the existing customer ID and createdAt
      const customerToUpdate = {
        ...updatedCustomer,
        id: customer.id,
        createdAt: customer.createdAt
      };

      // Ensure addresses have proper IDs
      if (customerToUpdate.addresses) {
        customerToUpdate.addresses = customerToUpdate.addresses.map((addr, index) => ({
          ...addr,
          id: addr.id || `addr-${index + 1}`
        }));
      }

      const success = await updateCustomer(id, customerToUpdate);

      if (success) {
        console.log('Customer updated successfully');
        // Fetch the updated customer data
        const refreshedCustomer = await getCustomer(id);
        if (refreshedCustomer) {
          console.log('Refreshed customer data:', refreshedCustomer);
          setCustomer(refreshedCustomer);
        }
        setShowEditForm(false);
        // Show success message
        alert('Customer information updated successfully');
      } else {
        console.error('Failed to update customer');
        setLocalError('Failed to update customer. Please try again.');
      }
    } catch (err) {
      console.error('Error updating customer:', err);
      setLocalError('An unexpected error occurred. Please try again.');
    } finally {
      setLocalLoading(false);
    }
  };

  // Always define all rendering conditions outside the return statement
  const renderContent = () => {
    // Show loading state
    if (localLoading || loading) {
      return (
        <div className="p-6">
          <div className="text-center">
            <RefreshCw className="w-12 h-12 text-primary-500 animate-spin mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900">Loading customer data...</h2>
          </div>
        </div>
      );
    }

    // Show error state
    if (localError || error) {
      return (
        <div className="p-6">
          <div className="text-center">
            <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900">Error loading customer</h2>
            <p className="text-gray-500 mt-2">{localError || error}</p>
            <Link to="/customers">
              <Button variant="primary" className="mt-4">
                Back to Customers
              </Button>
            </Link>
          </div>
        </div>
      );
    }

    // Show not found state
    if (!customer) {
      return (
        <div className="p-6">
          <div className="text-center">
            <User className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900">Customer not found</h2>
            <p className="text-gray-500 mt-2">The customer you're looking for doesn't exist.</p>
            <Link to="/customers">
              <Button variant="primary" className="mt-4">
                Back to Customers
              </Button>
            </Link>
          </div>
        </div>
      );
    }

    // Main content when customer is loaded
    return (
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link to="/customers">
              <Button variant="outline" size="sm">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{customer.name || 'Unnamed Customer'}</h1>
              <p className="text-gray-500">Customer Profile</p>
            </div>
          </div>
          <div className="flex space-x-2">
            <button
              className="flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              onClick={() => {
                console.log('Edit button clicked');
                setShowEditForm(true);
              }}
            >
              <Edit2 className="w-4 h-4 mr-2" />
              Edit
            </button>
            <button
              className="flex items-center px-4 py-2 border border-red-300 rounded-md text-sm font-medium text-red-700 bg-white hover:bg-red-50"
              onClick={() => setShowDeleteConfirmation(true)}
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Delete
            </button>
          </div>
        </div>

        {/* Customer details */}
        <Card>
          <div className="flex flex-col md:flex-row">
            <div className="flex-1 border-b md:border-b-0 md:border-r border-gray-200">
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Customer Information</h3>
                <div className="space-y-4">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Name</p>
                    <p className="mt-1 text-sm text-gray-900">{customer.name || 'N/A'}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Email</p>
                    <p className="mt-1 text-sm text-gray-900">{customer.email || 'N/A'}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Phone</p>
                    <p className="mt-1 text-sm text-gray-900">{formatPhoneNumber(customer.phone) || 'N/A'}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Status</p>
                    <div className="mt-1">
                      <Badge variant={customer.status === 'active' ? 'success' : 'gray'}>
                        {customer.status || 'N/A'}
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex-1">
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Summary</h3>
                <div className="space-y-4">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Total Orders</p>
                    <p className="mt-1 text-sm text-gray-900">{customer.totalOrders || 0}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Total Spent</p>
                    <p className="mt-1 text-sm text-gray-900">
                      ₹{typeof customer.totalSpent === 'number' ? customer.totalSpent.toFixed(2) : '0.00'}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Last Order</p>
                    <p className="mt-1 text-sm text-gray-900">{customer.lastOrderDate ? formatDate(customer.lastOrderDate) : 'No orders yet'}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Customer Since</p>
                    <p className="mt-1 text-sm text-gray-900">{formatDate(customer.createdAt)}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Card>

        {/* Customer Overview */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-6">
            {/* Tabs */}
            <Card>
              <div className="border-b border-gray-200">
                <div className="flex">
                  <button
                    className={`px-6 py-3 text-sm font-medium ${
                      activeTab === 'overview'
                        ? 'border-b-2 border-primary-500 text-primary-600'
                        : 'text-gray-500 hover:text-gray-700'
                    }`}
                    onClick={() => setActiveTab('overview')}
                  >
                    Overview
                  </button>
                  <button
                    className={`px-6 py-3 text-sm font-medium ${
                      activeTab === 'orders'
                        ? 'border-b-2 border-primary-500 text-primary-600'
                        : 'text-gray-500 hover:text-gray-700'
                    }`}
                    onClick={() => setActiveTab('orders')}
                  >
                    Orders
                  </button>
                  <button
                    className={`px-6 py-3 text-sm font-medium ${
                      activeTab === 'addresses'
                        ? 'border-b-2 border-primary-500 text-primary-600'
                        : 'text-gray-500 hover:text-gray-700'
                    }`}
                    onClick={() => setActiveTab('addresses')}
                  >
                    Addresses
                  </button>
                </div>
              </div>

              <div className="p-6">
                {activeTab === 'overview' && (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h3 className="text-sm font-medium text-gray-500 mb-4">Contact Information</h3>
                        <div className="space-y-3">
                          <div className="flex items-center">
                            <Mail className="w-4 h-4 text-gray-400 mr-2" />
                            <span>{customer.email}</span>
                          </div>
                          <div className="flex items-center">
                            <Phone className="w-4 h-4 text-gray-400 mr-2" />
                            <span>{formatPhoneNumber(customer.phone)}</span>
                          </div>
                        </div>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-500 mb-4">Account Details</h3>
                        <div className="space-y-3">
                          <div className="flex items-center">
                            <Calendar className="w-4 h-4 text-gray-400 mr-2" />
                            <span>Member since {formatDate(customer.createdAt)}</span>
                          </div>
                          <div className="flex items-center">
                            <User className="w-4 h-4 text-gray-400 mr-2" />
                            <Badge variant={customer.status === 'active' ? 'success' : 'gray'}>
                              {customer.status}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium text-gray-500 mb-4">Recent Activity</h3>
                      <div className="space-y-4">
                        {ordersLoading ? (
                          <div className="text-center py-2">
                            <RefreshCw className="w-5 h-5 text-primary-500 animate-spin mx-auto mb-2" />
                            <p className="text-sm text-gray-500">Loading orders...</p>
                          </div>
                        ) : orders.length > 0 ? (
                          orders.slice(0, 3).map(order => {
                            const hasReturns = order.items.some(item => item.returned || (item.returnedQuantity && item.returnedQuantity > 0));

                            return (
                              <div key={order.id} className="flex items-start">
                                <div className={`w-2 h-2 mt-2 rounded-full ${hasReturns ? 'bg-red-500' : 'bg-primary-500'} mr-3`}></div>
                                <div>
                                  <p className={`${hasReturns ? 'text-red-600' : 'text-gray-900'}`}>
                                    {hasReturns ? 'Returned items from order' : 'Placed order'} {order.orderID || order.id}
                                  </p>
                                  <p className="text-sm text-gray-500">
                                    {formatDate(order.createdAt)}
                                  </p>
                                </div>
                              </div>
                            );
                          })
                        ) : (
                          <p className="text-sm text-gray-500">No orders yet</p>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === 'orders' && (
                  <div className="space-y-4">
                    {ordersLoading ? (
                      <div className="text-center py-8">
                        <RefreshCw className="w-8 h-8 text-primary-500 animate-spin mx-auto mb-4" />
                        <p className="text-gray-500">Loading orders...</p>
                      </div>
                    ) : orders.length > 0 ? (
                      orders.map(order => (
                        <Card key={order.id}>
                          <div className="p-4">
                            <div className="flex items-center justify-between mb-4">
                              <div>
                                <Link
                                  to={`/orders/${order.id}`}
                                  className="text-lg font-medium text-primary-600 hover:underline"
                                >
                                  {order.orderID ? `#${order.orderID}` : order.id}
                                </Link>
                                <p className="text-sm text-gray-500">
                                  {formatDate(order.createdAt)}
                                </p>
                              </div>
                              <OrderStatusBadge status={order.status} />
                            </div>

                            <div className="space-y-4">
                              <div>
                                <h4 className="text-sm font-medium text-gray-500 mb-2">Items</h4>
                                {order.items.map(item => {
                                  const isReturned = item.returned || (item.returnedQuantity && item.returnedQuantity > 0);
                                  const returnedQty = item.returnedQuantity || 0;
                                  const effectiveQty = item.quantity - returnedQty;

                                  return (
                                    <div key={item.id} className={`flex justify-between text-sm ${isReturned ? 'text-red-600' : ''}`}>
                                      <div className="flex items-center">
                                        <div className="flex flex-col">
                                          <span className="font-medium">
                                            {item.productName || `Product ${item.productId || item.id}`}
                                          </span>
                                          <span>
                                            {isReturned
                                              ? `${effectiveQty}/${item.quantity}`
                                              : item.quantity} {item.unit || 'pc'}
                                          </span>
                                        </div>
                                        {isReturned && (
                                          <span className="ml-2 px-1.5 py-0.5 bg-red-100 text-red-800 text-xs rounded-full">
                                            {returnedQty === item.quantity ? 'Returned' : `${returnedQty} Returned`}
                                          </span>
                                        )}
                                      </div>
                                      <span>₹{(isReturned ? item.price * effectiveQty : item.subtotal).toFixed(2)}</span>
                                    </div>
                                  );
                                })}
                              </div>

                              <div className="border-t border-gray-200 pt-4">
                                <div className="flex justify-between text-sm">
                                  <span className="text-gray-500">Subtotal</span>
                                  <span>₹{order.subtotal.toFixed(2)}</span>
                                </div>
                                <div className="flex justify-between text-sm">
                                  <span className="text-gray-500">Tax</span>
                                  <span>₹{order.tax.toFixed(2)}</span>
                                </div>

                                {/* Show returns/refunds if any items have been returned */}
                                {order.items.some(item => item.returned || (item.returnedQuantity && item.returnedQuantity > 0)) && (
                                  <div className="flex justify-between text-sm text-red-600">
                                    <span>Returns/Refunds</span>
                                    <span>-₹{order.items.reduce((sum, item) => {
                                      const returnedQty = item.returnedQuantity || 0;
                                      return sum + (item.price * returnedQty);
                                    }, 0).toFixed(2)}</span>
                                  </div>
                                )}

                                <div className="flex justify-between font-medium mt-2">
                                  <span>Total</span>
                                  <span>₹{order.total.toFixed(2)}</span>
                                </div>
                              </div>

                              <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                                <div className="flex items-center">
                                  <CreditCard className="w-4 h-4 text-gray-400 mr-2" />
                                  <span className="text-sm text-gray-600 capitalize">
                                    {order.paymentMethod}
                                  </span>
                                </div>
                                <Link to={`/orders/${order.id}`}>
                                  <Button variant="outline" size="sm">
                                    View Details
                                  </Button>
                                </Link>
                              </div>
                            </div>
                          </div>
                        </Card>
                      ))
                    ) : (
                      <div className="text-center py-8">
                        <Package className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                        <p className="text-gray-500 mb-4">No orders found for this customer.</p>
                        <Button
                          variant="primary"
                          onClick={handleCreateNewOrder}
                        >
                          <Plus className="w-4 h-4 mr-2" />
                          Create New Order
                        </Button>
                      </div>
                    )}
                  </div>
                )}

                {activeTab === 'addresses' && (
                  <div className="space-y-4">
                    {customer.addresses.map(address => (
                      <Card key={address.id}>
                        <div className="p-4">
                          <div className="flex items-start justify-between">
                            <div className="flex items-start">
                              <MapPin className="w-5 h-5 text-gray-400 mr-3 mt-1" />
                              <div>
                                <div className="flex items-center">
                                  <h4 className="font-medium text-gray-900 capitalize">
                                    {address.type}
                                  </h4>
                                  {address.isDefault && (
                                    <Badge variant="secondary" className="ml-2">
                                      Default
                                    </Badge>
                                  )}
                                </div>
                                <p className="text-gray-600 mt-1">
                                  {address.line1}<br />
                                  {address.line2 && <>{address.line2}<br /></>}
                                  {address.city}, {address.state}<br />
                                  {address.pincode}
                                </p>
                              </div>
                            </div>
                            <div className="flex space-x-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  console.log('Edit address button clicked');
                                  setShowEditForm(true);
                                }}
                              >
                                <Edit2 size={16} />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  console.log('Delete address button clicked');
                                  // Implement address deletion if needed
                                }}
                              >
                                <Trash2 size={16} />
                              </Button>
                            </div>
                          </div>
                        </div>
                      </Card>
                    ))}
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => {
                        console.log('Add new address button clicked');
                        setShowEditForm(true);
                      }}
                    >
                      <Plus size={16} className="mr-2" />
                      Add New Address
                    </Button>
                  </div>
                )}
              </div>
            </Card>
          </div>

          {/* Customer Stats */}
          <div className="space-y-6">
            <Card>
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Customer Stats</h3>
                <div className="space-y-4">
                  <div>
                    <p className="text-sm text-gray-500">Total Orders</p>
                    <p className="text-2xl font-bold text-gray-900">{customer.totalOrders || 0}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Total Spent</p>
                    <p className="text-2xl font-bold text-gray-900">
                      ₹{typeof customer.totalSpent === 'number' ? customer.totalSpent.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) : '0.00'}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Average Order Value</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {customer.totalOrders > 0 && typeof customer.totalSpent === 'number'
                        ? `₹${(customer.totalSpent / customer.totalOrders).toFixed(2)}`
                        : 'N/A'}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Last Order</p>
                    <p className="text-lg font-medium text-gray-900">{formatDate(customer.lastOrderDate)}</p>
                  </div>
                </div>
              </div>
            </Card>

            <Card>
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                <div className="space-y-2">
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={handleCreateNewOrder}
                  >
                    <Package size={16} className="mr-2" />
                    Create New Order
                  </Button>
                </div>
              </div>
            </Card>
          </div>
        </div>
        {/* Add the EditCustomerForm */}
        {customer && (
          <AddCustomerForm
            isOpen={showEditForm}
            onClose={() => setShowEditForm(false)}
            onSave={handleSaveCustomer}
            initialData={customer}
            isEditing={true}
          />
        )}
        {/* Remove the bottom edit button if it doesn't exist in your UI */}
        {/* Delete Confirmation Modal */}
        <Modal
          isOpen={showDeleteConfirmation}
          onClose={() => setShowDeleteConfirmation(false)}
          title="Delete Customer"
          maxWidth="md"
        >
          <div className="p-6">
            <p className="text-gray-700 mb-6">
              Are you sure you want to delete this customer? This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={() => setShowDeleteConfirmation(false)}
              >
                Cancel
              </Button>
              <Button
                variant="danger"
                onClick={handleDeleteCustomer}
              >
                Delete Customer
              </Button>
            </div>
          </div>
        </Modal>
      </div>
    );
  };

  // Main return - always returns the same structure
  return renderContent();
}
