import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import {
  getAllCustomers,
  getCustomerById,
  addCustomer as addCustomerToFirebase,
  updateCustomer as updateCustomerInFirebase,
  deleteCustomer as deleteCustomerFromFirebase,
  searchCustomers as searchCustomersInFirebase,
  searchAllCustomers,
  getCustomersPage,
  searchCustomersPage,
  getCustomerCount,
  DEFAULT_PAGE_SIZE
} from '../services/customerService';
import { Customer } from '../types';
import { QueryDocumentSnapshot, doc, getDoc } from 'firebase/firestore';
import { db } from '../firebase/config';

interface CustomerContextType {
  customers: Customer[];
  loading: boolean;
  error: string | null;
  totalCustomers: number;
  activeCustomers: number;
  inactiveCustomers: number;
  totalRevenue: number;
  totalOrders: number;
  pageSize: number;
  currentPage: number;
  hasNextPage: boolean;
  addCustomer: (customer: Omit<Customer, 'id' | 'createdAt'>) => Promise<string | null>;
  updateCustomer: (id: string, customer: Partial<Customer>) => Promise<boolean>;
  deleteCustomer: (id: string) => Promise<boolean>;
  getCustomer: (id: string) => Promise<Customer | null>;
  refreshCustomers: () => Promise<void>;
  searchCustomers: (searchTerm: string) => Promise<Customer[]>;
  loadNextPage: () => Promise<boolean>;
  loadPreviousPage: () => Promise<boolean>;
  goToPage: (page: number) => Promise<boolean>;
  setPageSize: (size: number) => void;
  isInitialized: boolean;
}

const CustomerContext = createContext<CustomerContextType | undefined>(undefined);

export const useCustomers = () => {
  const context = useContext(CustomerContext);
  if (!context) {
    throw new Error('useCustomers must be used within a CustomerProvider');
  }
  return context;
};

interface CustomerProviderProps {
  children: ReactNode;
}

export const CustomerProvider = ({ children }: CustomerProviderProps) => {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState<boolean>(false);
  const [totalCustomers, setTotalCustomers] = useState<number>(0);
  const [activeCustomers, setActiveCustomers] = useState<number>(0);
  const [inactiveCustomers, setInactiveCustomers] = useState<number>(0);
  const [totalRevenue, setTotalRevenue] = useState<number>(0);
  const [totalOrders, setTotalOrders] = useState<number>(0);
  const [pageSize, setPageSize] = useState<number>(DEFAULT_PAGE_SIZE);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [lastDocMap, setLastDocMap] = useState<Map<number, QueryDocumentSnapshot>>(new Map());
  const [hasNextPage, setHasNextPage] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>('');

  // Fetch total customer counts and stats
  const fetchTotalCustomers = async (): Promise<void> => {
    try {
      // Get total customer count
      const count = await getCustomerCount();
      setTotalCustomers(count);
      console.log(`Total customers count: ${count}`);

      try {
        // Get active customers count (estimate as 90% of total if not available)
        const activeCount = Math.round(count * 0.9); // Most customers are active
        setActiveCustomers(activeCount);

        // Get inactive customers count
        const inactiveCount = count - activeCount;
        setInactiveCustomers(inactiveCount);

        console.log(`Active customers: ${activeCount}, Inactive customers: ${inactiveCount}`);

        // Estimate total revenue and orders based on average values
        // These are rough estimates that will be replaced with actual data when available
        const avgOrdersPerCustomer = 3;
        const avgRevenuePerOrder = 500;

        const estimatedTotalOrders = count * avgOrdersPerCustomer;
        setTotalOrders(estimatedTotalOrders);

        const estimatedTotalRevenue = estimatedTotalOrders * avgRevenuePerOrder;
        setTotalRevenue(estimatedTotalRevenue);

        console.log(`Estimated total orders: ${estimatedTotalOrders}, Estimated total revenue: ${estimatedTotalRevenue}`);
      } catch (statsError) {
        console.error('Error calculating customer stats:', statsError);
      }
    } catch (err) {
      console.error('Error fetching total customers count:', err);
    }
  };

  // Load a specific page of customers
  const loadPage = async (page: number, searchTerm: string = ''): Promise<boolean> => {
    if (loading) return false; // Prevent multiple simultaneous requests

    try {
      setLoading(true);
      setError(null);

      // Store the current search query
      setSearchQuery(searchTerm);

      // Get the last document for the previous page if we're not on page 1
      let lastDoc: QueryDocumentSnapshot | null = null;
      if (page > 1) {
        lastDoc = lastDocMap.get(page - 1) || null;

        // If we don't have the last doc for the previous page and we're trying to go forward,
        // try to load the previous page first
        if (!lastDoc) {
          console.warn(`Missing cursor for page ${page-1}, attempting to load previous pages first`);

          // If we're trying to go to page 2, we can just load page 1 first
          if (page === 2) {
            console.log('Loading page 1 to get cursor');
            const success = await loadPage(1, searchQuery);
            if (!success) {
              console.error('Failed to load page 1 to get cursor');
              setLoading(false);
              return false;
            }

            // Now get the cursor for page 1
            lastDoc = lastDocMap.get(1) || null;
            if (!lastDoc) {
              console.error('Still missing cursor after loading page 1');
              setLoading(false);
              return false;
            }
          } else {
            // For pages > 2, we need to load all previous pages
            console.error(`Cannot load page ${page} directly without having loaded previous pages`);
            setLoading(false);
            return false;
          }
        }
      }

      // Fetch customers for this page
      let result;
      if (searchTerm) {
        result = await searchCustomersPage(searchTerm, pageSize, lastDoc);
      } else {
        result = await getCustomersPage(pageSize, lastDoc);
      }

      const { customers: fetchedCustomers, lastDoc: newLastDoc } = result;

      console.log(`Loaded page ${page} with ${fetchedCustomers.length} customers`);

      // Store the last document for pagination
      if (newLastDoc) {
        setLastDocMap(prev => {
          const newMap = new Map(prev);
          newMap.set(page, newLastDoc);
          return newMap;
        });

        // Check if there might be a next page
        setHasNextPage(fetchedCustomers.length === pageSize);
      } else {
        setHasNextPage(false);
      }

      setCustomers(fetchedCustomers);
      setCurrentPage(page);
      setIsInitialized(true);

      // Update total count if we're on the first page
      if (page === 1) {
        fetchTotalCustomers();
      }

      return true;
    } catch (err) {
      console.error(`Error loading page ${page}:`, err);
      setError('Failed to load customers. Please try again later.');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Refresh customers from Firebase (load first page)
  const refreshCustomers = async (): Promise<void> => {
    console.log('Refreshing customers list');
    setLoading(true);
    
    try {
      // Reset search query
      setSearchQuery('');
      
      // Reset pagination state
      setLastDocMap(new Map());
      setCurrentPage(1);

      // Load the first page
      await loadPage(1, '');
      
      // Update total count
      fetchTotalCustomers();
      
      console.log('Customer list refreshed successfully');
    } catch (error) {
      console.error('Error refreshing customers:', error);
      setError('Failed to refresh customers');
    } finally {
      setLoading(false);
    }
  };

  // Load the next page of customers
  const loadNextPage = async (): Promise<boolean> => {
    if (!hasNextPage) return false;

    const nextPage = currentPage + 1;

    // Check if we have the last document from the current page to use as cursor
    if (!lastDocMap.has(currentPage)) {
      console.log(`Missing cursor for current page ${currentPage}, storing current customers as cursor`);

      // Get the last customer from the current page to use as cursor
      if (customers.length > 0) {
        try {
          // We need to get the actual Firestore document to use as cursor
          const lastCustomerId = customers[customers.length - 1].id;
          const docRef = doc(db, 'Users', lastCustomerId);
          const docSnap = await getDoc(docRef);

          if (docSnap.exists()) {
            // Store this document as the cursor for the current page
            setLastDocMap(prev => {
              const newMap = new Map(prev);
              newMap.set(currentPage, docSnap);
              return newMap;
            });
          } else {
            console.error(`Could not find document for customer ID: ${lastCustomerId}`);

            // Try a different approach - reload the current page to get proper cursors
            console.log(`Reloading page ${currentPage} to get proper cursors`);
            const success = await loadPage(currentPage, searchQuery);
            if (!success) return false;
          }
        } catch (err) {
          console.error('Error getting document for cursor:', err);
          return false;
        }
      } else {
        console.error('No customers in current page to use as cursor');
        return false;
      }
    }

    return loadPage(nextPage, searchQuery);
  };

  // Load the previous page of customers
  const loadPreviousPage = async (): Promise<boolean> => {
    if (currentPage <= 1) return false;
    return loadPage(currentPage - 1, searchQuery);
  };

  // Go to a specific page
  const goToPage = async (page: number): Promise<boolean> => {
    if (page < 1) return false;

    // If trying to go to a page we don't have the cursor for,
    // and it's not the first page, we need to load pages sequentially
    if (page > 1 && !lastDocMap.has(page - 1)) {
      // Start from the highest page we have a cursor for
      let highestPage = 0;
      for (const pageNum of lastDocMap.keys()) {
        if (pageNum < page && pageNum > highestPage) {
          highestPage = pageNum;
        }
      }

      // If we don't have any previous pages, start from page 1
      if (highestPage === 0) {
        highestPage = 1;
      }

      // Load pages sequentially until we reach the desired page
      for (let i = highestPage; i < page; i++) {
        const success = await loadPage(i, searchQuery);
        if (!success) return false;
      }
    }

    return loadPage(page, searchQuery);
  };

  // Update page size
  const handleSetPageSize = (size: number): void => {
    setPageSize(size);
    // Reset pagination when changing page size
    setLastDocMap(new Map());
    setCurrentPage(1);
    // Load the first page with the new page size
    loadPage(1, searchQuery);
  };

  // Get a specific customer by ID
  const getCustomer = async (id: string): Promise<Customer | null> => {
    try {
      setError(null);
      // First check if we already have the customer in state
      const existingCustomer = customers.find(c => c.id === id);
      if (existingCustomer) {
        return existingCustomer;
      }

      // If not, fetch from Firebase
      return await getCustomerById(id);
    } catch (err) {
      console.error('Error getting customer:', err);
      setError('Failed to get customer. Please try again.');
      return null;
    }
  };

  // Add a new customer
  const addCustomer = async (customer: Omit<Customer, 'id' | 'createdAt'>): Promise<string | null> => {
    try {
      setError(null);
      const newCustomerId = await addCustomerToFirebase(customer);
      if (newCustomerId) {
        // Only refresh if we've already loaded customers
        if (isInitialized) {
          await refreshCustomers();
        }
        return newCustomerId;
      }
      return null;
    } catch (err) {
      console.error('Error adding customer:', err);
      setError('Failed to add customer. Please try again.');
      return null;
    }
  };

  // Update an existing customer
  const updateCustomer = async (id: string, customer: Partial<Customer>): Promise<boolean> => {
    try {
      setError(null);
      const success = await updateCustomerInFirebase(id, customer);
      if (success && isInitialized) {
        // Only refresh if we've already loaded customers
        await refreshCustomers();
      }
      return success;
    } catch (err) {
      console.error('Error updating customer:', err);
      setError('Failed to update customer. Please try again.');
      return false;
    }
  };

  // Delete a customer
  const deleteCustomer = async (id: string): Promise<boolean> => {
    try {
      setError(null);
      const success = await deleteCustomerFromFirebase(id);
      if (success && isInitialized) {
        // Only refresh if we've already loaded customers
        await refreshCustomers();
      }
      return success;
    } catch (err) {
      console.error('Error deleting customer:', err);
      setError('Failed to delete customer. Please try again.');
      return false;
    }
  };

  // Search customers - searches ALL customers in the database
  const searchCustomers = async (searchTerm: string): Promise<Customer[]> => {
    try {
      setError(null);
      setLoading(true);

      // If search term is empty, refresh the customer list
      if (!searchTerm.trim()) {
        console.log('Search term is empty, refreshing customers');
        return refreshCustomers().then(() => customers);
      }

      // Update the search query
      setSearchQuery(searchTerm);

      // Reset pagination
      setLastDocMap(new Map());
      setCurrentPage(1);

      console.log(`Searching all customers for: "${searchTerm}"`);

      try {
        // Search ALL customers in the database
        const searchResults = await searchAllCustomers(searchTerm);
        console.log(`Found ${searchResults.length} customers matching "${searchTerm}" across all customers`);

        // Update the customers state with the search results
        setCustomers(searchResults || []);
        setIsInitialized(true);
        setHasNextPage(false); // No pagination for search results

        // Update total count for the search results
        if (searchResults.length > 0) {
          // This is just for the UI to show the correct count
          setTotalCustomers(searchResults.length);
        }

        return searchResults || [];
      } catch (searchError: any) {
        console.error('Error in searchAllCustomers:', searchError);

        // Check if this is a missing index error
        if (searchError.message && searchError.message.includes("requires an index")) {
          setError('The database requires an index for search. Please contact the administrator. Using a simplified search method in the meantime.');

          // Try a simpler approach - get all customers and filter client-side
          try {
            console.log('Falling back to client-side filtering for search');

            // Get a larger batch of customers without complex filtering
            const { customers: fallbackResults } = await getCustomersPage(500, null);
            console.log(`Retrieved ${fallbackResults.length} customers for client-side filtering`);

            // Filter client-side
            const filteredResults = fallbackResults.filter(customer =>
              customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
              (customer.email && customer.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
              (customer.phone && customer.phone.includes(searchTerm))
            );

            console.log(`Found ${filteredResults.length} customers after client-side filtering`);

            // Update state
            setCustomers(filteredResults);
            setHasNextPage(false); // No pagination for search results
            setTotalCustomers(filteredResults.length); // Update total count for UI

            return filteredResults;
          } catch (fallbackError) {
            console.error('Error with fallback search:', fallbackError);
            throw fallbackError; // Re-throw to be caught by the outer catch
          }
        } else {
          throw searchError; // Re-throw to be caught by the outer catch
        }
      }
    } catch (err: any) {
      console.error('Error searching customers:', err);
      setError(`Failed to search customers: ${err.message || 'Unknown error'}`);

      // Ensure customers is always an array
      setCustomers([]);
      setHasNextPage(false);

      return [];
    } finally {
      setLoading(false);
    }
  };

  return (
    <CustomerContext.Provider value={{
      customers,
      loading,
      error,
      totalCustomers,
      activeCustomers,
      inactiveCustomers,
      totalRevenue,
      totalOrders,
      pageSize,
      currentPage,
      hasNextPage,
      addCustomer,
      updateCustomer,
      deleteCustomer,
      getCustomer,
      refreshCustomers,
      searchCustomers,
      loadNextPage,
      loadPreviousPage,
      goToPage,
      setPageSize: handleSetPageSize,
      isInitialized,
    }}>
      {children}
    </CustomerContext.Provider>
  );
};


