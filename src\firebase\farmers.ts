import { collection, addDoc, serverTimestamp } from 'firebase/firestore';
import { db } from './config';

// Collection name
const COLLECTION_NAME = 'Farmers';

// Interface for Farmer document
export interface Farmer {
  id?: string;
  farmerId?: string;
  farmerName: string;
  farmName: string;
  farmLocation: string;
  experience: string;
  philosophy: string;
  certifications: string[];
  tags: string[];
  createdAt?: any;
  updatedAt?: any;
}

// Add a new farmer to Firestore
export const addFarmer = async (farmerData: Omit<Farmer, 'id' | 'createdAt' | 'updatedAt'>) => {
  try {
    const docRef = await addDoc(collection(db, COLLECTION_NAME), {
      ...farmerData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });
    return docRef.id;
  } catch (error) {
    console.error('Error adding farmer:', error);
    return null;
  }
};


