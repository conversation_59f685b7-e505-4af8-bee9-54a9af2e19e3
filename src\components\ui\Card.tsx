import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';

const cardVariants = cva(
  "rounded-lg bg-white shadow-sm",
  {
    variants: {
      variant: {
        default: "border border-gray-200",
        elevated: "shadow-md",
        outline: "border border-gray-300",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

export interface CardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardVariants> {}

export function Card({ className, variant, ...props }: CardProps) {
  return (
    <div className={cardVariants({ variant, className })} {...props} />
  );
}
