import { db, auth } from '../firebase/config';
import { collection, doc, getDoc, getDocs, query, where, orderBy, limit, updateDoc, addDoc, Timestamp } from 'firebase/firestore';
import { Order } from '../types';
import { cleanObject } from '../utils/dataUtils';

// Check if user is authenticated
const checkAuth = async (): Promise<boolean> => {
  return new Promise((resolve) => {
    const unsubscribe = auth.onAuthStateChanged((user) => {
      unsubscribe();
      if (user) {
        console.log('User is authenticated:', user.uid);
        resolve(true);
      } else {
        console.log('No authenticated user found');
        resolve(false);
      }
    });
  });
};

const ORDERS_COLLECTION = 'Orders';

// Helper function to extract product name from various possible fields
const extractProductName = (item: any): string => {
  // Check all possible fields where product name might be stored
  if (item.productName && typeof item.productName === 'string' && item.productName.trim() !== '') {
    return item.productName;
  }

  if (item.name && typeof item.name === 'string' && item.name.trim() !== '') {
    return item.name;
  }

  // If the item has a product object with a name
  if (item.product && item.product.name && typeof item.product.name === 'string') {
    return item.product.name;
  }

  // Extract name from the ID if it's in a format like "cart-item-ProductName"
  if (item.id && typeof item.id === 'string') {
    // Check if ID contains product information
    const idParts = item.id.split('-');
    if (idParts.length >= 3) {
      // Try to extract a meaningful name from the ID
      const potentialName = idParts.slice(2).join('-');
      if (potentialName && potentialName.length > 0) {
        // Convert potential name to a more readable format
        return potentialName
          .replace(/([A-Z])/g, ' $1') // Add spaces before capital letters
          .replace(/([0-9]+)/g, ' $1') // Add spaces before numbers
          .trim();
      }
    }
  }

  // If we have a product ID, use that as a fallback
  if (item.productId) {
    return `Product ${item.productId}`;
  }

  // Last resort fallback
  return item.id ? `Item ${item.id}` : 'Unknown Product';
};

// Map Firestore document to Order type
export const mapDocToOrder = (doc: any): Order => {
  const data = doc.data();

  console.log(`Mapping order document with ID: ${doc.id}`);
  console.log('Order document fields:', Object.keys(data));

  // Convert Firestore timestamp to ISO string
  let createdAt = new Date().toISOString();
  if (data.orderedTime) {
    if (typeof data.orderedTime.toDate === 'function') {
      createdAt = data.orderedTime.toDate().toISOString();
    } else if (data.orderedTime.seconds) {
      createdAt = new Date(data.orderedTime.seconds * 1000).toISOString();
    }
  } else if (data.createdAt) {
    if (typeof data.createdAt.toDate === 'function') {
      createdAt = data.createdAt.toDate().toISOString();
    } else if (data.createdAt.seconds) {
      createdAt = new Date(data.createdAt.seconds * 1000).toISOString();
    }
  }

  // Convert updatedAt if it exists
  let updatedAt = createdAt;
  if (data.updatedAt) {
    if (typeof data.updatedAt.toDate === 'function') {
      updatedAt = data.updatedAt.toDate().toISOString();
    } else if (data.updatedAt.seconds) {
      updatedAt = new Date(data.updatedAt.seconds * 1000).toISOString();
    }
  }

  // Map order items - handle both 'items' and 'orderedItem' fields
  let items = [];

  // Check if we have 'orderedItem' field (as seen in the example)
  if (Array.isArray(data.orderedItem)) {
    console.log(`Found ${data.orderedItem.length} items in orderedItem array`);
    items = data.orderedItem.map((item: any, index: number) => {
      // Extract product name from various possible fields
      const productName = extractProductName(item);

      return {
        id: item.id || `item-${index}`,
        productId: item.id || '',
        productName: productName,
        tamilName: item.tamilName || '',
        price: item.price || 0,
        quantity: item.quantity || 1,
        unit: item.unit || '',
        subtotal: (item.price * item.quantity) || 0,
        returned: item.returned || false,
        returnedQuantity: item.returnedQuantity || 0
      };
    });
  }
  // Fall back to 'items' field if 'orderedItem' doesn't exist
  else if (Array.isArray(data.items)) {
    console.log(`Found ${data.items.length} items in items array`);
    items = data.items.map((item: any, index: number) => {
      // Extract product name from various possible fields
      const productName = extractProductName(item);

      return {
        id: item.id || `item-${index}`,
        productId: item.productId || item.product_id || '',
        productName: productName,
        tamilName: item.tamilName || '',
        price: item.price || 0,
        quantity: item.quantity || 1,
        unit: item.unit || '',
        subtotal: item.subtotal || item.total || (item.price * item.quantity) || 0,
        returned: item.returned || false,
        returnedQuantity: item.returnedQuantity || 0
      };
    });
  } else {
    console.log('No items found in order document');
  }

  // Check if any items have been returned
  const hasReturns = items.some(item => item.returned || (item.returnedQuantity && item.returnedQuantity > 0));

  // Calculate total if not present
  let subtotal = 0;
  let tax = 0;
  let total = 0;

  // Try to get values from the document first
  if (typeof data.subTotal === 'number') {
    subtotal = data.subTotal;
  } else if (typeof data.subtotal === 'number') {
    subtotal = data.subtotal;
  } else {
    // Calculate from items
    subtotal = items.reduce((sum: number, item: any) => sum + (item.subtotal || 0), 0);
  }

  if (typeof data.tax === 'number') {
    tax = data.tax;
  }

  if (typeof data.total === 'number') {
    total = data.total;
  } else if (typeof data.grandTotal === 'number') {
    total = data.grandTotal;
  } else {
    // Calculate from subtotal and tax
    total = subtotal + tax;
  }

  // Map order status
  const status = data.status || 'placed';

  // Map payment status and method
  const paymentStatus = data.paymentStatus || data.payment_status || 'pending';
  const paymentMethod = data.paymentMethod || data.modeOfPayment || data.payment_method || 'cash';

  // Map customer information - check all possible field names
  const customerId = data.customerID || data.customerId || data.customer_id || data.uid || data.userId || data.userID || data.user_id || '';
  const customerName = data.customerName || data.customer_name || data.addressName || data.name || '';
  const customerPhone = data.phoneNumber || data.customerPhone || data.customer_phone || data.addressPhoneNumber || data.phone || '';
  const customerEmail = data.customerEmail || data.customer_email || data.email || '';

  // Map address information - handle both nested and flat address structures
  let deliveryAddress = null;

  // Check if we have address fields directly in the order document
  if (data.addressLines || data.addressID) {
    deliveryAddress = {
      id: data.addressID || '',
      type: 'delivery',
      line1: data.addressLines || '',
      line2: data.addressLandmark || '',
      city: data.branchName || '',
      state: '',
      pincode: data.addressPincode || '',
      phoneNumber: data.addressPhoneNumber || customerPhone
    };
  }
  // Fall back to deliveryAddress field if it exists
  else if (data.deliveryAddress) {
    deliveryAddress = data.deliveryAddress;
  }

  // Get delivery charge if present
  const deliveryCharge = typeof data.deliveryCharge === 'number' ? data.deliveryCharge : 0;

  // Get the orderID if it exists
  const orderID = typeof data.orderID === 'number' ? data.orderID : null;

  // Create the order object
  const order = {
    id: doc.id,
    orderID: orderID,
    customerId: customerId,
    customerName,
    customerPhone,
    customerEmail,
    items,
    subtotal,
    tax,
    total,
    status,
    paymentStatus,
    paymentMethod,
    createdAt,
    updatedAt,
    deliveryAddress,
    deliveryCharge,
    cancellationReason: data.cancellationReason || '',
    hasReturns: data.hasReturns || hasReturns || false
  };

  console.log('Mapped order:', {
    id: order.id,
    orderID: order.orderID,
    customerId: order.customerId,
    customerName: order.customerName,
    items: order.items.length,
    total: order.total,
    status: order.status
  });

  return order;
};

// Get all orders
export const getAllOrders = async (): Promise<Order[]> => {
  try {
    // Check if user is authenticated
    const isAuthenticated = await checkAuth();
    if (!isAuthenticated) {
      console.log('User not authenticated');
      return [];
    }

    console.log('Fetching all orders');
    const ordersRef = collection(db, ORDERS_COLLECTION);

    try {
      // First try with the compound query that requires an index
      const q = query(
        ordersRef,
        orderBy('orderedTime', 'desc')
      );

      const querySnapshot = await getDocs(q);
      console.log(`Found ${querySnapshot.docs.length} orders with compound query`);

      // Map documents to Order type
      return querySnapshot.docs.map(mapDocToOrder);
    } catch (indexError) {
      console.log('Compound query failed, falling back to simple query:', indexError.message);

      // If the compound query fails (likely due to missing index), fall back to a simple query
      const simpleQ = query(ordersRef);

      const querySnapshot = await getDocs(simpleQ);
      console.log(`Found ${querySnapshot.docs.length} orders with simple query`);

      // Map documents to Order type and sort manually
      const orders = querySnapshot.docs.map(mapDocToOrder);
      return orders.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    }
  } catch (error) {
    console.error('Error getting all orders:', error);
    throw error; // Propagate the error to be handled by the caller
  }
};

// Update order status
export const updateOrderStatus = async (
  orderId: string,
  newStatus: Order['status'],
  cancellationReason?: string,
  paymentStatus?: string
): Promise<boolean> => {
  try {
    // Check if user is authenticated
    const isAuthenticated = await checkAuth();
    if (!isAuthenticated) {
      console.log('User not authenticated');
      return false;
    }

    console.log(`Updating order ${orderId} status to ${newStatus}`);
    const orderRef = doc(db, ORDERS_COLLECTION, orderId);

    const updateData: any = {
      status: newStatus,
      updatedAt: Timestamp.now()
    };

    // Add cancellation reason if provided and status is cancelled
    if (newStatus === 'cancelled' && cancellationReason) {
      updateData.cancellationReason = cancellationReason;
    }

    // Update payment status if provided
    if (paymentStatus) {
      console.log(`Updating payment status to ${paymentStatus}`);
      updateData.paymentStatus = paymentStatus;
    }

    await updateDoc(orderRef, updateData);
    console.log(`Order ${orderId} status updated successfully`);

    return true;
  } catch (error) {
    console.error(`Error updating order ${orderId} status:`, error);
    return false;
  }
};

// Update order payment status
export const updateOrderPaymentStatus = async (
  orderId: string,
  newPaymentStatus: string
): Promise<boolean> => {
  try {
    // Check if user is authenticated
    const isAuthenticated = await checkAuth();
    if (!isAuthenticated) {
      console.log('User not authenticated');
      return false;
    }

    console.log(`Updating order ${orderId} payment status to ${newPaymentStatus}`);
    const orderRef = doc(db, ORDERS_COLLECTION, orderId);

    await updateDoc(orderRef, {
      paymentStatus: newPaymentStatus,
      updatedAt: Timestamp.now()
    });

    console.log(`Order ${orderId} payment status updated successfully`);
    return true;
  } catch (error) {
    console.error(`Error updating order ${orderId} payment status:`, error);
    return false;
  }
};

// Get orders for a specific customer
export const getOrdersByCustomerId = async (customerId: string): Promise<Order[]> => {
  try {
    // Check if user is authenticated
    const isAuthenticated = await checkAuth();
    if (!isAuthenticated) {
      console.log('User not authenticated');
      return [];
    }

    console.log(`Fetching orders for customer ID: ${customerId}`);

    // DIRECT APPROACH: Get all orders and filter by customer ID
    // This bypasses any index requirements and field name issues
    const ordersRef = collection(db, ORDERS_COLLECTION);
    const allOrdersQuery = query(ordersRef);
    const allOrdersSnapshot = await getDocs(allOrdersQuery);

    console.log(`Retrieved ${allOrdersSnapshot.docs.length} total orders from database`);

    // Log all order documents for debugging
    console.log('All order documents:');
    allOrdersSnapshot.docs.forEach((doc, index) => {
      if (index < 5) { // Only log the first 5 to avoid flooding the console
        const data = doc.data();
        console.log(`Order ${index + 1}:`, {
          id: doc.id,
          customerID: data.customerID,
          customerId: data.customerId,
          uid: data.uid
        });
      }
    });

    // Filter orders manually to find those matching the customer ID
    const matchingOrders = allOrdersSnapshot.docs.filter(doc => {
      const data = doc.data();

      // Check ALL possible customer ID field names based on the example document
      // The user ID is the customer ID in the orders collection
      const possibleIds = [
        data.customerID,
        data.customerId,
        data.customer_id,
        data.uid,
        data.userId,
        data.userID,
        data.user_id
      ];

      // Check if any of the possible IDs match the customer ID
      const matches = possibleIds.some(id => id === customerId);

      // Log for debugging
      if (matches) {
        console.log(`Found matching order: ${doc.id} with customer ID fields:`, {
          customerID: data.customerID,
          customerId: data.customerId,
          uid: data.uid
        });
      }

      return matches;
    });

    console.log(`Found ${matchingOrders.length} orders for customer ${customerId} after manual filtering`);

    if (matchingOrders.length > 0) {
      // Map the matching documents to Order objects
      const orders = matchingOrders.map(mapDocToOrder);

      // Sort by ordered time (newest first)
      orders.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

      return orders;
    }

    // If no orders found, return empty array
    console.log(`No orders found for customer ${customerId}`);
    return [];
  } catch (error) {
    console.error('Error getting customer orders:', error);
    return [];
  }
};

// Calculate customer order statistics
export const getCustomerOrderStats = async (customerId: string): Promise<{ totalOrders: number, totalSpent: number, lastOrderDate: string | null }> => {
  try {
    console.log(`Calculating order stats for customer: ${customerId}`);

    // Use our improved getOrdersByCustomerId function to get orders
    const orders = await getOrdersByCustomerId(customerId);

    console.log(`Found ${orders.length} orders for stats calculation`);

    if (orders.length === 0) {
      console.log(`No orders found for customer ${customerId}, returning zero stats`);
      return { totalOrders: 0, totalSpent: 0, lastOrderDate: null };
    }

    // Sort orders by date (newest first) to get the last order date
    const sortedOrders = [...orders].sort((a, b) =>
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );

    const totalOrders = orders.length;
    const totalSpent = orders.reduce((sum, order) => sum + order.total, 0);
    const lastOrderDate = sortedOrders[0].createdAt;

    console.log(`Calculated stats: totalOrders=${totalOrders}, totalSpent=${totalSpent}, lastOrderDate=${lastOrderDate}`);

    // Update the customer document with the new stats
    try {
      console.log(`Updating customer ${customerId} with calculated stats`);
      const customerRef = doc(db, 'Users', customerId);
      await updateDoc(customerRef, {
        totalOrders,
        totalSpent,
        lastOrderDate: lastOrderDate ? Timestamp.fromDate(new Date(lastOrderDate)) : null
      });
      console.log(`Successfully updated customer ${customerId} stats in database`);
    } catch (updateError) {
      console.error(`Error updating customer stats in database: ${updateError}`);
      // Continue even if update fails - we still want to return the calculated stats
    }

    return { totalOrders, totalSpent, lastOrderDate };
  } catch (error) {
    console.error('Error calculating customer order stats:', error);
    return { totalOrders: 0, totalSpent: 0, lastOrderDate: null };
  }
};

// Add a new order
export const addOrder = async (orderData: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>): Promise<string | null> => {
  try {
    // Check if user is authenticated
    const isAuthenticated = await checkAuth();
    if (!isAuthenticated) {
      console.log('User not authenticated');
      return null;
    }

    console.log('Adding new order with data:', orderData);

    // Clean the order data, removing undefined values and the 'state' field from all objects
    const cleanedOrderData = cleanObject(orderData, ['state']);

    // Generate a unique orderID (numeric)
    const timestamp = Date.now();
    const generatedOrderID = Number(timestamp.toString().slice(-8));
    console.log('Generated orderID:', generatedOrderID);

    // Ensure payment status is set correctly based on payment method
    let paymentStatus = cleanedOrderData.paymentStatus;
    if (cleanedOrderData.paymentMethod === 'cash' || cleanedOrderData.paymentMethod === 'cod') {
      paymentStatus = 'unpaid';
    } else if (!paymentStatus) {
      paymentStatus = 'paid';
    }

    // Create a completely new delivery address object if needed
    let deliveryAddress = null;
    if (cleanedOrderData.deliveryAddress) {
      // Create a new object with only the fields we want
      const addressObj: any = {};

      // Add required fields with default values
      addressObj.type = cleanedOrderData.deliveryAddress.type || 'home';
      addressObj.line1 = cleanedOrderData.deliveryAddress.line1 || '';

      // Only add optional fields if they exist and are not undefined/null
      if (cleanedOrderData.deliveryAddress.line2) addressObj.line2 = cleanedOrderData.deliveryAddress.line2;
      if (cleanedOrderData.deliveryAddress.city) addressObj.city = cleanedOrderData.deliveryAddress.city;
      if (cleanedOrderData.deliveryAddress.pincode) addressObj.pincode = cleanedOrderData.deliveryAddress.pincode;

      // Add phone number
      addressObj.phoneNumber = cleanedOrderData.deliveryAddress.phoneNumber || cleanedOrderData.customerPhone || '';

      // IMPORTANT: Do NOT include the state field at all

      console.log('Created clean delivery address object:', addressObj);
      deliveryAddress = addressObj;
    }

    // Create the final order document
    const orderDoc = {
      ...cleanedOrderData,
      // Override payment status based on payment method
      paymentStatus,
      // Replace the delivery address with our new object
      ...(deliveryAddress && { deliveryAddress }),
      // If customerID exists in orderData, add customerId as well (and vice versa)
      ...(cleanedOrderData.customerID && { customerId: cleanedOrderData.customerID }),
      ...(cleanedOrderData.customerId && { customerID: cleanedOrderData.customerId }),
      // Add orderID if not already present
      orderID: cleanedOrderData.orderID || generatedOrderID,
      orderedTime: Timestamp.now(),
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    };

    // Debug logs to verify the final document structure
    console.log('Final order document to be saved:', orderDoc);

    // Double-check for any undefined values in the document
    const stringified = JSON.stringify(orderDoc);
    if (stringified.includes('undefined')) {
      console.error('WARNING: Document contains undefined values after cleaning!');
      // Find and log the undefined values
      const undefinedProps = [];
      const checkForUndefined = (obj: any, path = '') => {
        if (typeof obj !== 'object' || obj === null) return;

        for (const [key, value] of Object.entries(obj)) {
          const currentPath = path ? `${path}.${key}` : key;
          if (value === undefined) {
            undefinedProps.push(currentPath);
          } else if (typeof value === 'object' && value !== null) {
            checkForUndefined(value, currentPath);
          }
        }
      };

      checkForUndefined(orderDoc);
      console.error('Undefined properties:', undefinedProps);

      // Fix any undefined values by removing them
      for (const prop of undefinedProps) {
        const parts = prop.split('.');
        let current = orderDoc;
        for (let i = 0; i < parts.length - 1; i++) {
          current = current[parts[i]];
        }
        delete current[parts[parts.length - 1]];
      }

      console.log('Fixed document:', orderDoc);
    }

    if (orderDoc.deliveryAddress) {
      console.log('Delivery address structure:', orderDoc.deliveryAddress);
      // Make absolutely sure there's no state field
      if ('state' in orderDoc.deliveryAddress) {
        console.log('Removing state field from delivery address');
        delete orderDoc.deliveryAddress.state;
      }
    } else {
      console.log('No delivery address in final document');
    }

    // Add the order to Firestore
    const docRef = await addDoc(collection(db, ORDERS_COLLECTION), orderDoc);
    console.log(`Order added with ID: ${docRef.id}`);

    // If the order has a customer ID, update the customer's order stats
    const customerIdToUpdate = orderData.customerId || orderData.customerID;
    if (customerIdToUpdate) {
      try {
        await updateCustomerOrderStats(customerIdToUpdate);
        console.log(`Updated order stats for customer ${customerIdToUpdate}`);
      } catch (statsError) {
        console.error(`Error updating customer order stats: ${statsError}`);
        // Continue even if updating stats fails
      }
    } else {
      console.log('No customer ID found in order data, skipping customer stats update');
    }

    return docRef.id;
  } catch (error) {
    console.error('Error adding order:', error);
    // Log more details about the error
    if (error instanceof Error) {
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
    }
    return null;
  }
};

// Update customer order stats
const updateCustomerOrderStats = async (customerId: string): Promise<void> => {
  try {
    console.log(`Updating order stats for customer: ${customerId}`);

    // Get all orders for this customer using our improved function
    const stats = await getCustomerOrderStats(customerId);

    // Stats calculation and database update is now handled inside getCustomerOrderStats
    // This function is kept for backward compatibility
    console.log(`Customer stats updated: totalOrders=${stats.totalOrders}, totalSpent=${stats.totalSpent}`);
  } catch (error) {
    console.error(`Error updating customer ${customerId} order stats:`, error);
    // Don't throw the error - just log it and continue
    // This prevents order creation from failing if stats update fails
  }
};

// Update order items (for returns)
export const updateOrderItems = async (
  orderId: string,
  updatedItems: any[]
): Promise<boolean> => {
  try {
    // Check if user is authenticated
    const isAuthenticated = await checkAuth();
    if (!isAuthenticated) {
      console.log('User not authenticated');
      return false;
    }

    console.log(`Updating items for order ${orderId}`);
    const orderRef = doc(db, ORDERS_COLLECTION, orderId);

    // Get the current order to calculate the new total
    const orderDoc = await getDoc(orderRef);
    if (!orderDoc.exists()) {
      console.error(`Order ${orderId} not found`);
      return false;
    }

    const orderData = orderDoc.data();

    // Calculate new subtotal based on returned items
    const newSubtotal = updatedItems.reduce((sum, item) => {
      const returnedQty = item.returnedQuantity || 0;
      const effectiveQty = item.quantity - returnedQty;
      return sum + (item.price * effectiveQty);
    }, 0);

    // Calculate new total (maintain the same tax rate)
    const oldSubtotal = orderData.subtotal || orderData.subTotal || 0;
    const oldTotal = orderData.total || orderData.grandTotal || 0;
    const taxRate = oldSubtotal > 0 ? (oldTotal - oldSubtotal) / oldSubtotal : 0;
    const newTax = newSubtotal * taxRate;
    const newTotal = newSubtotal + newTax;

    // Update the order with new items and totals
    const updateData = {
      items: updatedItems,
      orderedItem: updatedItems, // Update both field names for compatibility
      subtotal: newSubtotal,
      subTotal: newSubtotal, // Update both field names for compatibility
      total: newTotal,
      grandTotal: newTotal, // Update both field names for compatibility
      hasReturns: true,
      updatedAt: Timestamp.now()
    };

    await updateDoc(orderRef, updateData);
    console.log(`Order ${orderId} items updated successfully`);

    return true;
  } catch (error) {
    console.error(`Error updating order ${orderId} items:`, error);
    return false;
  }
};


// Get a single order by ID
export const getOrderById = async (orderId: string): Promise<Order | null> => {
  try {
    // Check if user is authenticated
    const isAuthenticated = await checkAuth();
    if (!isAuthenticated) {
      console.log('User not authenticated');
      return null;
    }

    console.log(`Fetching order with ID: ${orderId}`);
    const orderRef = doc(db, ORDERS_COLLECTION, orderId);
    const orderDoc = await getDoc(orderRef);

    if (orderDoc.exists()) {
      console.log(`Found order with ID: ${orderId}`);
      return mapDocToOrder(orderDoc);
    } else {
      console.log(`Order with ID ${orderId} not found`);
      return null;
    }
  } catch (error) {
    console.error(`Error getting order ${orderId}:`, error);
    return null;
  }
};

export default {
  getOrdersByCustomerId,
  getCustomerOrderStats,
  getOrderById
};




