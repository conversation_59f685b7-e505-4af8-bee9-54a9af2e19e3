import React, { useState, useEffect } from 'react';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Select } from '../ui/Select';
import { Badge } from '../ui/Badge';
import { Plus, Edit2, Trash2, Package, X } from 'lucide-react';
import { ProductVariant, VariationType } from '../../types';
import {
  getProductVariants,
  addProductVariant,
  updateProductVariant,
  deleteProductVariant
} from '../../services/productVariantService';
import { getAllVariationTypes } from '../../services/variationTypeService';

interface VariantManagerProps {
  productId: string;
  onVariantsChange?: (variants: ProductVariant[]) => void;
}

export function VariantManager({ productId, onVariantsChange }: VariantManagerProps) {
  const [variants, setVariants] = useState<ProductVariant[]>([]);
  const [variationTypes, setVariationTypes] = useState<VariationType[]>([]);
  const [showNewForm, setShowNewForm] = useState(false);
  const [editingVariant, setEditingVariant] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // New state for variant setup
  const [selectedVariationTypes, setSelectedVariationTypes] = useState<string[]>([]);
  const [showVariationTypeSelector, setShowVariationTypeSelector] = useState(false);
  const [generatedVariants, setGeneratedVariants] = useState<any[]>([]);

  const [newVariant, setNewVariant] = useState({
    sku: '',
    price: 0,
    mrp: 0,
    stock: 0,
    image: '',
    status: 'active' as 'active' | 'inActive',
    variationValues: {} as Record<string, string>
  });

  const [editVariantData, setEditVariantData] = useState({
    sku: '',
    price: 0,
    mrp: 0,
    stock: 0,
    image: '',
    status: 'active' as 'active' | 'inActive',
    variationValues: {} as Record<string, string>
  });

  // Fetch variation types and existing variants on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Always fetch variation types first
        const fetchedVariationTypes = await getAllVariationTypes();
        console.log('Fetched variation types:', fetchedVariationTypes);
        setVariationTypes(fetchedVariationTypes);

        // Fetch existing variants only if productId exists and is not 'new'
        let fetchedVariants: ProductVariant[] = [];
        if (productId && productId !== 'new') {
          fetchedVariants = await getProductVariants(productId);
          console.log('Fetched existing variants:', fetchedVariants);
        }

        setVariants(fetchedVariants);

        // Show variation type selector if:
        // 1. No existing variants AND
        // 2. Variation types are available
        if (fetchedVariants.length === 0 && fetchedVariationTypes.length > 0) {
          console.log('No variants exist, showing variation type selector');
          console.log('Setting showVariationTypeSelector to true');
          setShowVariationTypeSelector(true);
        } else {
          console.log('Not showing selector:', {
            variantsLength: fetchedVariants.length,
            variationTypesLength: fetchedVariationTypes.length
          });
        }
      } catch (err) {
        console.error('Error fetching variant data:', err);
        setError('Failed to load variation types. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [productId]); // Remove onVariantsChange to prevent infinite loop

  // Separate effect to call onVariantsChange when variants change
  useEffect(() => {
    onVariantsChange?.(variants);
  }, [variants, onVariantsChange]);

  // Generate all possible variant combinations
  const generateVariantCombinations = () => {
    if (selectedVariationTypes.length === 0) return [];

    const selectedTypes = variationTypes.filter(type =>
      selectedVariationTypes.includes(type.id)
    );

    // Generate all combinations
    const combinations: any[] = [];

    function generateCombos(typeIndex: number, currentCombo: Record<string, string>) {
      if (typeIndex === selectedTypes.length) {
        // Create a variant object for this combination
        const sku = Object.values(currentCombo).join('-').toUpperCase();
        combinations.push({
          id: `temp-${Date.now()}-${Math.random()}`,
          sku: sku,
          variationValues: { ...currentCombo },
          price: 0,
          mrp: 0,
          stock: 0,
          status: 'active'
        });
        return;
      }

      const currentType = selectedTypes[typeIndex];
      for (const option of currentType.options) {
        generateCombos(typeIndex + 1, {
          ...currentCombo,
          [currentType.name]: option
        });
      }
    }

    generateCombos(0, {});
    return combinations;
  };

  // Handle variation type selection
  const handleVariationTypeToggle = (typeId: string) => {
    setSelectedVariationTypes(prev => {
      if (prev.includes(typeId)) {
        return prev.filter(id => id !== typeId);
      } else {
        return [...prev, typeId];
      }
    });
  };

  // Generate variants based on selected types
  const handleGenerateVariants = () => {
    const combinations = generateVariantCombinations();
    setGeneratedVariants(combinations);
    setShowVariationTypeSelector(false);
  };

  // Save generated variants
  const handleSaveGeneratedVariants = async () => {
    if (!productId || productId === 'new') {
      setError('Please save the product first before adding variants.');
      return;
    }

    try {
      setLoading(true);
      const savedVariants: ProductVariant[] = [];

      for (const variant of generatedVariants) {
        if (variant.price > 0) { // Only save variants with price set
          const variantId = await addProductVariant(productId, {
            sku: variant.sku,
            variationValues: variant.variationValues,
            price: variant.price,
            mrp: variant.mrp,
            stock: variant.stock,
            status: variant.status,
            image: ''
          });

          if (variantId) {
            savedVariants.push({
              id: variantId,
              productId,
              ...variant,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            });
          }
        }
      }

      setVariants(savedVariants);
      onVariantsChange?.(savedVariants);
      setGeneratedVariants([]);
      setSelectedVariationTypes([]);
    } catch (err) {
      console.error('Error saving variants:', err);
      setError('Failed to save variants. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Update generated variant
  const updateGeneratedVariant = (index: number, field: string, value: any) => {
    setGeneratedVariants(prev => prev.map((variant, i) =>
      i === index ? { ...variant, [field]: value } : variant
    ));
  };

  // Add a new variant
  const handleAddVariant = async () => {
    if (newVariant.sku && newVariant.price > 0) {
      try {
        const variantId = await addProductVariant(productId, newVariant);

        if (variantId) {
          const newVariantWithId = {
            id: variantId,
            productId,
            ...newVariant,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          };

          const updatedVariants = [...variants, newVariantWithId];
          setVariants(updatedVariants);
          onVariantsChange?.(updatedVariants);

          // Reset form
          setNewVariant({
            sku: '',
            price: 0,
            mrp: 0,
            stock: 0,
            image: '',
            status: 'active',
            variationValues: {}
          });
          setShowNewForm(false);
        }
      } catch (err) {
        console.error('Error adding variant:', err);
        setError('Failed to add variant. Please try again.');
      }
    }
  };

  // Start editing a variant
  const startEditingVariant = (variantId: string) => {
    const variantToEdit = variants.find(variant => variant.id === variantId);
    if (variantToEdit) {
      setEditVariantData({
        sku: variantToEdit.sku,
        price: variantToEdit.price,
        mrp: variantToEdit.mrp,
        stock: variantToEdit.stock,
        image: variantToEdit.image || '',
        status: variantToEdit.status,
        variationValues: { ...variantToEdit.variationValues }
      });
      setEditingVariant(variantId);
    }
  };

  // Save edited variant
  const handleSaveEdit = async () => {
    if (editingVariant && editVariantData.sku && editVariantData.price > 0) {
      try {
        const success = await updateProductVariant(productId, editingVariant, editVariantData);

        if (success) {
          const updatedVariants = variants.map(variant =>
            variant.id === editingVariant
              ? { ...variant, ...editVariantData, updatedAt: new Date().toISOString() }
              : variant
          );
          setVariants(updatedVariants);
          onVariantsChange?.(updatedVariants);

          // Reset editing state
          setEditingVariant(null);
          setEditVariantData({
            sku: '',
            price: 0,
            mrp: 0,
            stock: 0,
            image: '',
            status: 'active',
            variationValues: {}
          });
        }
      } catch (err) {
        console.error('Error updating variant:', err);
        setError('Failed to update variant. Please try again.');
      }
    }
  };

  // Delete a variant
  const handleDeleteVariant = async (variantId: string) => {
    if (window.confirm('Are you sure you want to delete this variant?')) {
      try {
        const success = await deleteProductVariant(productId, variantId);

        if (success) {
          const updatedVariants = variants.filter(variant => variant.id !== variantId);
          setVariants(updatedVariants);
          onVariantsChange?.(updatedVariants);
        }
      } catch (err) {
        console.error('Error deleting variant:', err);
        setError('Failed to delete variant. Please try again.');
      }
    }
  };

  // Update variation value for new variant
  const updateNewVariationValue = (variationType: string, value: string) => {
    setNewVariant({
      ...newVariant,
      variationValues: {
        ...newVariant.variationValues,
        [variationType]: value
      }
    });
  };

  // Update variation value for edit variant
  const updateEditVariationValue = (variationType: string, value: string) => {
    setEditVariantData({
      ...editVariantData,
      variationValues: {
        ...editVariantData.variationValues,
        [variationType]: value
      }
    });
  };

  // Remove variation value from new variant
  const removeNewVariationValue = (variationType: string) => {
    const { [variationType]: removed, ...rest } = newVariant.variationValues;
    setNewVariant({
      ...newVariant,
      variationValues: rest
    });
  };

  // Remove variation value from edit variant
  const removeEditVariationValue = (variationType: string) => {
    const { [variationType]: removed, ...rest } = editVariantData.variationValues;
    setEditVariantData({
      ...editVariantData,
      variationValues: rest
    });
  };



  if (loading) {
    return (
      <Card>
        <div className="p-6 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-2"></div>
          <p>Loading variation types...</p>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <div className="p-6 text-center text-red-600">{error}</div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900">Product Variants</h3>
        <div className="flex gap-2">
          {variants.length === 0 && generatedVariants.length === 0 && (
            <Button
              variant="primary"
              size="sm"
              onClick={() => setShowVariationTypeSelector(true)}
            >
              <Plus className="w-4 h-4 mr-2" />
              Setup Variants
            </Button>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowNewForm(true)}
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Manual Variant
          </Button>
        </div>
      </div>

      {/* Variation Type Selector */}
      {showVariationTypeSelector && (
        <Card>
          <div className="p-6">
            <h4 className="text-md font-semibold text-gray-900 mb-4">Select Variation Types</h4>
            <p className="text-sm text-gray-600 mb-4">
              Choose the types of variations for this product (e.g., Size, Color, Weight).
              We'll generate all possible combinations for you to set prices and inventory.
            </p>

            {variationTypes.length === 0 ? (
              <div className="text-center py-4">
                <p className="text-gray-600 mb-4">No variation types available.</p>
                <Button
                  variant="outline"
                  onClick={() => window.open('/products/variation-types', '_blank')}
                >
                  Create Variation Types
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {variationTypes.map((type) => (
                    <div
                      key={type.id}
                      className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                        selectedVariationTypes.includes(type.id)
                          ? 'border-primary-500 bg-primary-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => handleVariationTypeToggle(type.id)}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <h5 className="font-medium text-gray-900">{type.name}</h5>
                          {type.description && (
                            <p className="text-sm text-gray-600">{type.description}</p>
                          )}
                          <div className="flex flex-wrap gap-1 mt-2">
                            {type.options.slice(0, 3).map((option, index) => (
                              <Badge key={index} variant="outline" size="sm">
                                {option}
                              </Badge>
                            ))}
                            {type.options.length > 3 && (
                              <Badge variant="outline" size="sm">
                                +{type.options.length - 3} more
                              </Badge>
                            )}
                          </div>
                        </div>
                        <div className={`w-5 h-5 rounded border-2 flex items-center justify-center ${
                          selectedVariationTypes.includes(type.id)
                            ? 'border-primary-500 bg-primary-500'
                            : 'border-gray-300'
                        }`}>
                          {selectedVariationTypes.includes(type.id) && (
                            <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {selectedVariationTypes.length > 0 && (
                  <div className="p-4 bg-blue-50 rounded-lg">
                    <p className="text-sm text-blue-700 mb-2">
                      Selected: {selectedVariationTypes.length} variation type(s)
                    </p>
                    <p className="text-sm text-blue-600">
                      This will generate {generateVariantCombinations().length} variant combinations.
                    </p>
                  </div>
                )}

                <div className="flex gap-3">
                  <Button
                    variant="primary"
                    onClick={handleGenerateVariants}
                    disabled={selectedVariationTypes.length === 0}
                  >
                    Generate Variants ({generateVariantCombinations().length})
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setShowVariationTypeSelector(false);
                      setSelectedVariationTypes([]);
                    }}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            )}
          </div>
        </Card>
      )}

      {/* Generated Variants Setup */}
      {generatedVariants.length > 0 && (
        <Card>
          <div className="p-6">
            <h4 className="text-md font-semibold text-gray-900 mb-4">
              Set Prices and Inventory for Variants
            </h4>
            <p className="text-sm text-gray-600 mb-4">
              Set the price, MRP, and stock for each variant combination. Only variants with prices will be saved.
            </p>

            <div className="space-y-4">
              {generatedVariants.map((variant, index) => (
                <div key={variant.id} className="p-4 border border-gray-200 rounded-lg">
                  <div className="flex flex-wrap gap-2 mb-3">
                    {Object.entries(variant.variationValues).map(([type, value]) => (
                      <Badge key={type} variant="secondary">
                        {type}: {value}
                      </Badge>
                    ))}
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <Input
                      label="SKU"
                      type="text"
                      value={variant.sku}
                      onChange={(e) => updateGeneratedVariant(index, 'sku', e.target.value)}
                      placeholder="Auto-generated"
                    />
                    <Input
                      label="Price (₹)"
                      type="number"
                      value={variant.price.toString()}
                      onChange={(e) => updateGeneratedVariant(index, 'price', parseFloat(e.target.value) || 0)}
                      placeholder="0.00"
                      step="0.01"
                      required
                    />
                    <Input
                      label="MRP (₹)"
                      type="number"
                      value={variant.mrp.toString()}
                      onChange={(e) => updateGeneratedVariant(index, 'mrp', parseFloat(e.target.value) || 0)}
                      placeholder="0.00"
                      step="0.01"
                    />
                    <Input
                      label="Stock"
                      type="number"
                      value={variant.stock.toString()}
                      onChange={(e) => updateGeneratedVariant(index, 'stock', parseInt(e.target.value) || 0)}
                      placeholder="0"
                    />
                  </div>
                </div>
              ))}
            </div>

            <div className="flex gap-3 mt-6">
              <Button
                variant="primary"
                onClick={handleSaveGeneratedVariants}
                disabled={!generatedVariants.some(v => v.price > 0)}
              >
                Save Variants
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setGeneratedVariants([]);
                  setSelectedVariationTypes([]);
                }}
              >
                Cancel
              </Button>
            </div>
          </div>
        </Card>
      )}

      {/* New Variant Form */}
      {showNewForm && (
        <Card>
          <div className="p-6">
            <h4 className="text-md font-semibold text-gray-900 mb-4">Add New Variant</h4>
            <div className="grid grid-cols-2 gap-4">
              <Input
                label="SKU"
                type="text"
                value={newVariant.sku}
                onChange={(e) => setNewVariant({ ...newVariant, sku: e.target.value })}
                required
                placeholder="Enter SKU"
              />
              <Input
                label="Price (₹)"
                type="number"
                value={newVariant.price.toString()}
                onChange={(e) => setNewVariant({ ...newVariant, price: parseFloat(e.target.value) || 0 })}
                required
                placeholder="0.00"
                step="0.01"
              />
              <Input
                label="MRP (₹)"
                type="number"
                value={newVariant.mrp.toString()}
                onChange={(e) => setNewVariant({ ...newVariant, mrp: parseFloat(e.target.value) || 0 })}
                required
                placeholder="0.00"
                step="0.01"
              />
              <Input
                label="Stock"
                type="number"
                value={newVariant.stock.toString()}
                onChange={(e) => setNewVariant({ ...newVariant, stock: parseInt(e.target.value) || 0 })}
                required
                placeholder="0"
              />
            </div>

            {/* Variation Values */}
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">Variation Values</label>
              <div className="space-y-3">
                {variationTypes.map((variationType) => (
                  <div key={variationType.id} className="flex gap-2">
                    <Select
                      label={variationType.name}
                      options={[
                        { value: "", label: `Select ${variationType.name}` },
                        ...variationType.options.map(option => ({ value: option, label: option }))
                      ]}
                      value={newVariant.variationValues[variationType.name] || ""}
                      onChange={(e) => updateNewVariationValue(variationType.name, e.target.value)}
                    />
                    {newVariant.variationValues[variationType.name] && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeNewVariationValue(variationType.name)}
                        className="mt-6"
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                ))}
              </div>

              {/* Display selected variation values */}
              <div className="flex flex-wrap gap-2 mt-3">
                {Object.entries(newVariant.variationValues).map(([type, value]) => (
                  <Badge key={type} variant="secondary" className="flex items-center gap-1">
                    {type}: {value}
                    <button
                      onClick={() => removeNewVariationValue(type)}
                      className="ml-1 hover:text-red-600"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            </div>

            <div className="flex gap-3 mt-6">
              <Button
                variant="primary"
                onClick={handleAddVariant}
                disabled={!newVariant.sku || newVariant.price <= 0}
              >
                Add Variant
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setShowNewForm(false);
                  setNewVariant({
                    sku: '',
                    price: 0,
                    mrp: 0,
                    stock: 0,
                    image: '',
                    status: 'active',
                    variationValues: {}
                  });
                }}
              >
                Cancel
              </Button>
            </div>
          </div>
        </Card>
      )}

      {/* Variants List */}
      <div className="grid gap-4">
        {variants.map((variant) => (
          <Card key={variant.id}>
            <div className="p-4">
              {editingVariant === variant.id ? (
                // Edit form (similar to new form but with edit data)
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <Input
                      label="SKU"
                      type="text"
                      value={editVariantData.sku}
                      onChange={(e) => setEditVariantData({ ...editVariantData, sku: e.target.value })}
                      required
                    />
                    <Input
                      label="Price (₹)"
                      type="number"
                      value={editVariantData.price.toString()}
                      onChange={(e) => setEditVariantData({ ...editVariantData, price: parseFloat(e.target.value) || 0 })}
                      required
                      step="0.01"
                    />
                    <Input
                      label="MRP (₹)"
                      type="number"
                      value={editVariantData.mrp.toString()}
                      onChange={(e) => setEditVariantData({ ...editVariantData, mrp: parseFloat(e.target.value) || 0 })}
                      required
                      step="0.01"
                    />
                    <Input
                      label="Stock"
                      type="number"
                      value={editVariantData.stock.toString()}
                      onChange={(e) => setEditVariantData({ ...editVariantData, stock: parseInt(e.target.value) || 0 })}
                      required
                    />
                  </div>

                  <div className="flex gap-3">
                    <Button variant="primary" onClick={handleSaveEdit}>
                      Save Changes
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setEditingVariant(null);
                        setEditVariantData({
                          sku: '',
                          price: 0,
                          mrp: 0,
                          stock: 0,
                          image: '',
                          status: 'active',
                          variationValues: {}
                        });
                      }}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              ) : (
                // Display mode
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <Package className="h-5 w-5 text-primary-600" />
                      <h4 className="text-md font-semibold text-gray-900">SKU: {variant.sku}</h4>
                    </div>
                    <div className="grid grid-cols-3 gap-4 mb-3">
                      <div>
                        <span className="text-sm text-gray-500">Price:</span>
                        <p className="font-medium">₹{variant.price}</p>
                      </div>
                      <div>
                        <span className="text-sm text-gray-500">MRP:</span>
                        <p className="font-medium">₹{variant.mrp}</p>
                      </div>
                      <div>
                        <span className="text-sm text-gray-500">Stock:</span>
                        <p className="font-medium">{variant.stock}</p>
                      </div>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {Object.entries(variant.variationValues).map(([type, value]) => (
                        <Badge key={type} variant="outline">
                          {type}: {value}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => startEditingVariant(variant.id)}
                    >
                      <Edit2 className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteVariant(variant.id)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </Card>
        ))}
      </div>

      {variants.length === 0 && generatedVariants.length === 0 && !showVariationTypeSelector && !showNewForm && (
        <Card>
          <div className="p-8 text-center">
            <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-900 mb-2">No variants yet</h4>
            {variationTypes.length > 0 ? (
              <div>
                <p className="text-gray-600 mb-4">
                  Create variants to offer different options for this product.
                </p>
                <div className="flex gap-3 justify-center">
                  <Button variant="primary" onClick={() => setShowVariationTypeSelector(true)}>
                    <Plus className="w-4 h-4 mr-2" />
                    Setup Variants
                  </Button>
                  <Button variant="outline" onClick={() => setShowNewForm(true)}>
                    Add Manual Variant
                  </Button>
                </div>
              </div>
            ) : (
              <div>
                <p className="text-gray-600 mb-4">
                  You need to create variation types first (like Size, Color, Weight) before you can add variants.
                </p>
                <Button
                  variant="primary"
                  onClick={() => window.open('/products/variation-types', '_blank')}
                >
                  Create Variation Types
                </Button>
              </div>
            )}
          </div>
        </Card>
      )}
    </div>
  );
}
