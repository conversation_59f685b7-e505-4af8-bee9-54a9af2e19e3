import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import {
  ArrowLeft,
  Printer,
  Download,
  Edit,
  ChevronRight,
  Calendar,
  MapPin,
  Phone,
  Mail,
  ShoppingBag,
  Package,
  Truck,
  CheckCircle,
  XCircle,
  RefreshCw,
  RotateCcw,
  DollarSign
} from 'lucide-react';
import { Card } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { OrderStatusBadge, PaymentStatusBadge } from '../../components/ui/StatusBadge';
import { Order } from '../../types';
import { getOrderById, updateOrderStatus as updateOrderStatusService, updateOrderPaymentStatus } from '../../services/orderService';
import { ReturnItemsModal } from '../../components/returns/ReturnItemsModal';
import { Modal } from '../../components/ui/Modal';

export const OrderDetail = () => {
  const { id } = useParams<{ id: string }>();
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [showReturnModal, setShowReturnModal] = useState<boolean>(false);
  const [showPaymentConfirmation, setShowPaymentConfirmation] = useState<boolean>(false);

  // Fetch order data
  useEffect(() => {
    const fetchOrder = async () => {
      if (!id) return;

      try {
        setLoading(true);
        setError(null);

        console.log(`Fetching order with ID: ${id}`);
        const orderData = await getOrderById(id);

        if (orderData) {
          console.log('Order data fetched successfully:', orderData);
          setOrder(orderData);
        } else {
          console.log(`Order with ID ${id} not found`);
          setError('Order not found');
        }
      } catch (err) {
        console.error('Error fetching order:', err);
        setError('Failed to load order data');
      } finally {
        setLoading(false);
      }
    };

    fetchOrder();
  }, [id]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-IN', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const updateOrderStatus = async (newStatus: Order['status']) => {
    if (order && id) {
      try {
        // If the order is being marked as delivered and payment method is cash and payment status is unpaid,
        // show the payment confirmation dialog
        if (newStatus === 'delivered' && order.paymentMethod === 'cash' && order.paymentStatus === 'unpaid') {
          setShowPaymentConfirmation(true);
          return;
        }

        // Call the service to update the order status
        const success = await updateOrderStatusService(id, newStatus);

        if (success) {
          // Update the local state
          setOrder({ ...order, status: newStatus });
          // Show a success message
          alert(`Order status updated to ${newStatus}`);
        } else {
          alert('Failed to update order status. Please try again.');
        }
      } catch (err) {
        console.error('Error updating order status:', err);
        alert('An error occurred while updating the order status.');
      }
    }
  };

  // Handle payment confirmation
  const handlePaymentConfirmation = async (isPaid: boolean) => {
    if (order && id) {
      try {
        // Update the order status to delivered
        const statusSuccess = await updateOrderStatusService(id, 'delivered');

        // Update the payment status based on the confirmation
        const paymentStatus = isPaid ? 'paid' : 'unpaid';
        const paymentSuccess = await updateOrderPaymentStatus(id, paymentStatus);

        if (statusSuccess && paymentSuccess) {
          // Update the local state
          setOrder({
            ...order,
            status: 'delivered',
            paymentStatus: paymentStatus
          });

          // Show a success message
          alert(`Order marked as delivered and payment status set to ${paymentStatus}`);
        } else {
          alert('Failed to update order. Please try again.');
        }
      } catch (err) {
        console.error('Error updating order:', err);
        alert('An error occurred while updating the order.');
      } finally {
        setShowPaymentConfirmation(false);
      }
    }
  };

  // Refresh order data after a return is processed
  const handleOrderRefresh = async () => {
    if (!id) return;

    try {
      setLoading(true);
      const refreshedOrder = await getOrderById(id);

      if (refreshedOrder) {
        setOrder(refreshedOrder);
        alert('Order updated with return information');
      } else {
        setError('Failed to refresh order data');
      }
    } catch (err) {
      console.error('Error refreshing order:', err);
      setError('Failed to refresh order data');
    } finally {
      setLoading(false);
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center h-full py-12 animate-fade-in">
        <div className="text-center">
          <RefreshCw className="h-12 w-12 text-primary-500 animate-spin mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Loading Order</h2>
          <p className="text-gray-500 mb-6">Please wait while we fetch the order details...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error || !order) {
    return (
      <div className="flex flex-col items-center justify-center h-full py-12 animate-fade-in">
        <div className="text-center">
          <XCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Order Not Found</h2>
          <p className="text-gray-500 mb-6">The order you're looking for doesn't exist or has been deleted.</p>
          <Link to="/orders">
            <Button variant="primary">Back to Orders</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Return Items Modal */}
      {order && (
        <ReturnItemsModal
          isOpen={showReturnModal}
          onClose={() => setShowReturnModal(false)}
          order={order}
          onSuccess={handleOrderRefresh}
        />
      )}

      {/* Payment Confirmation Modal */}
      <Modal
        isOpen={showPaymentConfirmation}
        onClose={() => setShowPaymentConfirmation(false)}
        title="Payment Confirmation"
        maxWidth="md"
      >
        <div className="p-6">
          <div className="flex items-center mb-4 text-amber-600">
            <DollarSign className="w-6 h-6 mr-2" />
            <h3 className="text-lg font-medium">Confirm Payment Status</h3>
          </div>

          <p className="text-gray-700 mb-6">
            This order has a cash payment method. Has the customer paid for this order?
          </p>

          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={() => handlePaymentConfirmation(false)}
            >
              Not Paid Yet
            </Button>
            <Button
              variant="primary"
              onClick={() => handlePaymentConfirmation(true)}
            >
              Mark as Paid
            </Button>
          </div>
        </div>
      </Modal>

      <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
        <div className="flex items-center">
          <Link to="/orders" className="mr-3">
            <Button variant="ghost" icon={<ArrowLeft size={16} />} size="sm">
              Back
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-display font-bold text-gray-900 flex items-center">
              Order {order.orderID ? `#${order.orderID}` : order.id}
              <span className="ml-3">
                <OrderStatusBadge status={order.status} />
              </span>
            </h1>
            <p className="text-gray-500">{formatDate(order.createdAt)}</p>
          </div>
        </div>
        <div className="flex flex-wrap gap-2">
          <Button variant="outline" size="sm" icon={<Printer size={16} />}>
            Print Invoice
          </Button>
          <Button variant="outline" size="sm" icon={<Download size={16} />}>
            Download
          </Button>
          <Button variant="primary" size="sm" icon={<Edit size={16} />}>
            Edit Order
          </Button>
        </div>
      </div>

      {/* Order Status Timeline */}
      <Card className="p-0 overflow-hidden">
        <div className="px-5 py-4 border-b border-gray-100">
          <h3 className="font-medium text-gray-900">Order Status</h3>
        </div>
        <div className="p-5">
          <div className="flex items-center overflow-x-auto">
            <StatusStep
              icon={<ShoppingBag size={20} />}
              label="Order Placed"
              date={formatDate(order.createdAt)}
              status={getStepStatus(order.status, 'placed')}
              onClick={() => updateOrderStatus('placed')}
            />
            <StatusConnector status={getStepStatus(order.status, 'processing')} />
            <StatusStep
              icon={<Package size={20} />}
              label="Processing"
              date={order.status === 'processing' ? 'In progress' : (order.status === 'placed' ? 'Pending' : formatDate(new Date().toISOString()))}
              status={getStepStatus(order.status, 'processing')}
              onClick={() => updateOrderStatus('processing')}
            />
            <StatusConnector status={getStepStatus(order.status, 'out_for_delivery')} />
            <StatusStep
              icon={<Truck size={20} />}
              label="Out for Delivery"
              date={order.status === 'out_for_delivery' ? 'In progress' : (order.status === 'placed' || order.status === 'processing' ? 'Pending' : formatDate(new Date().toISOString()))}
              status={getStepStatus(order.status, 'out_for_delivery')}
              onClick={() => updateOrderStatus('out_for_delivery')}
            />
            <StatusConnector status={getStepStatus(order.status, 'delivered')} />
            <StatusStep
              icon={<CheckCircle size={20} />}
              label="Delivered"
              date={order.status === 'delivered' ? formatDate(new Date().toISOString()) : 'Pending'}
              status={getStepStatus(order.status, 'delivered')}
              onClick={() => updateOrderStatus('delivered')}
            />
          </div>

          {order.status === 'cancelled' && (
            <div className="mt-4 p-4 bg-red-50 rounded-md border border-red-100">
              <div className="flex items-start">
                <XCircle className="text-red-500 h-5 w-5 mr-2 mt-0.5" />
                <div>
                  <h4 className="font-medium text-gray-900">Order Cancelled</h4>
                  <p className="text-sm text-gray-600 mt-1">
                    {order.cancellationReason || 'No reason provided'}
                  </p>
                  {order.paymentStatus === 'refunded' && (
                    <div className="mt-2">
                      <p className="text-sm font-medium text-gray-700">Refund Status:</p>
                      <div className="flex items-center mt-1">
                        <span className="text-sm text-gray-600 mr-2">
                          {order.refundStatus === 'completed' ? 'Refund completed' : order.refundStatus === 'processed' ? 'Refund processed' : 'Refund pending'}
                        </span>
                        {order.refundTransactionId && (
                          <span className="text-xs text-gray-500">
                            Ref: {order.refundTransactionId}
                          </span>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </Card>

      {/* Order Details Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Customer Info */}
        <Card>
          <h3 className="font-medium text-gray-900 mb-4">Customer Information</h3>
          <div className="space-y-3">
            <div className="flex items-start">
              <div className="bg-primary-50 p-2 rounded-full mr-3">
                <Phone size={16} className="text-primary-500" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Phone</p>
                <p className="font-medium">{order.customerPhone}</p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="bg-primary-50 p-2 rounded-full mr-3">
                <Mail size={16} className="text-primary-500" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Email</p>
                <p className="font-medium">{order.customerEmail}</p>
              </div>
            </div>

            <Link to={`/customers/${order.customerId}`} className="mt-4 inline-block text-primary-600 text-sm hover:underline">
              View Customer Profile
            </Link>
          </div>
        </Card>

        {/* Shipping Info */}
        <Card>
          <h3 className="font-medium text-gray-900 mb-4">Shipping Address</h3>
          <div className="space-y-3">
            <div className="flex items-start">
              <div className="bg-primary-50 p-2 rounded-full mr-3">
                <MapPin size={16} className="text-primary-500" />
              </div>
              <div>
                {order.deliveryAddress ? (
                  <>
                    <p className="text-sm text-gray-500 capitalize">{order.deliveryAddress.type || 'Delivery'}</p>
                    <p className="font-medium">{order.customerName}</p>
                    <p className="text-gray-600 text-sm mt-1">
                      {order.deliveryAddress.line1},
                      <br />
                      {order.deliveryAddress.line2 && <>{order.deliveryAddress.line2},<br /></>}
                      {order.deliveryAddress.city && `${order.deliveryAddress.city}, `}
                      {order.deliveryAddress.state && `${order.deliveryAddress.state}, `}
                      <br />
                      {order.deliveryAddress.pincode}
                    </p>
                    {order.deliveryAddress.phoneNumber && (
                      <p className="text-gray-600 text-sm mt-1">
                        Phone: {order.deliveryAddress.phoneNumber}
                      </p>
                    )}
                  </>
                ) : (
                  <>
                    <p className="font-medium">{order.customerName}</p>
                    <p className="text-gray-600 text-sm mt-1">
                      No delivery address provided
                    </p>
                    {order.customerPhone && (
                      <p className="text-gray-600 text-sm mt-1">
                        Phone: {order.customerPhone}
                      </p>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
        </Card>

        {/* Payment Info */}
        <Card>
          <h3 className="font-medium text-gray-900 mb-4">Payment Information</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <p className="text-gray-500">Payment Status</p>
              <PaymentStatusBadge status={order.paymentStatus} />
            </div>

            <div className="flex justify-between items-center">
              <p className="text-gray-500">Payment Method</p>
              <span className="capitalize font-medium">
                {order.paymentMethod === 'cod' ? 'Cash on Delivery' : order.paymentMethod}
              </span>
            </div>

            <div className="flex justify-between items-center">
              <p className="text-gray-500">Date</p>
              <span className="text-gray-700">{formatDate(order.createdAt)}</span>
            </div>
          </div>
        </Card>
      </div>

      {/* Order Items */}
      <Card>
        <h3 className="font-medium text-gray-900 mb-4">Order Items</h3>
        <div className="overflow-x-auto -mx-5">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Item
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Price
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Quantity
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Subtotal
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {order.items.map((item, index) => {
                const isReturned = item.returned || (item.returnedQuantity && item.returnedQuantity > 0);
                const returnedQty = item.returnedQuantity || 0;
                const effectiveQty = item.quantity - returnedQty;
                const effectiveSubtotal = item.price * effectiveQty;

                return (
                  <tr key={item.id || `item-${index}`} className={isReturned ? 'bg-red-50' : ''}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-primary-600">{item.productName}</div>
                      {item.productId && <div className="text-xs text-gray-500">ID: {item.productId}</div>}
                      {item.tamilName && <div className="text-xs text-gray-500">{item.tamilName}</div>}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-500">
                      ₹{item.price.toFixed(2)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-500">
                      {isReturned ? (
                        <span>
                          {effectiveQty} / {item.quantity} {item.unit || ''}
                        </span>
                      ) : (
                        <span>{item.quantity} {item.unit || ''}</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-right">
                      {isReturned ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          {returnedQty === item.quantity ? 'Returned' : `Partial Return (${returnedQty})`}
                        </span>
                      ) : (
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          order.status === 'delivered'
                            ? 'bg-green-100 text-green-800'
                            : order.status === 'out_for_delivery'
                              ? 'bg-blue-100 text-blue-800'
                              : 'bg-gray-100 text-gray-800'
                        }`}>
                          {order.status === 'delivered'
                            ? 'Delivered'
                            : order.status === 'out_for_delivery'
                              ? 'Out for Delivery'
                              : order.status.charAt(0).toUpperCase() + order.status.slice(1).replace('_', ' ')}
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-right font-medium text-gray-900">
                      {isReturned ? (
                        <div>
                          <span className="line-through text-gray-400 mr-2">₹{item.subtotal.toFixed(2)}</span>
                          <span>₹{effectiveSubtotal.toFixed(2)}</span>
                        </div>
                      ) : (
                        <span>₹{item.subtotal.toFixed(2)}</span>
                      )}
                    </td>
                  </tr>
                );
              })}
            </tbody>
            <tfoot className="bg-gray-50">
              <tr>
                <td colSpan={4} className="px-6 py-3 text-right text-sm font-medium text-gray-500">
                  Subtotal
                </td>
                <td className="px-6 py-3 text-right text-sm font-medium text-gray-900">
                  ₹{order.subtotal.toFixed(2)}
                </td>
              </tr>
              {typeof order.tax === 'number' && order.tax > 0 && (
                <tr>
                  <td colSpan={4} className="px-6 py-3 text-right text-sm font-medium text-gray-500">
                    Tax
                  </td>
                  <td className="px-6 py-3 text-right text-sm font-medium text-gray-900">
                    ₹{order.tax.toFixed(2)}
                  </td>
                </tr>
              )}
              {order.deliveryCharge && (
                <tr>
                  <td colSpan={4} className="px-6 py-3 text-right text-sm font-medium text-gray-500">
                    Delivery Charge
                  </td>
                  <td className="px-6 py-3 text-right text-sm font-medium text-gray-900">
                    ₹{order.deliveryCharge.toFixed(2)}
                  </td>
                </tr>
              )}
              {order.hasReturns && (
                <tr>
                  <td colSpan={4} className="px-6 py-3 text-right text-sm font-medium text-red-500">
                    Returns/Refunds
                  </td>
                  <td className="px-6 py-3 text-right text-sm font-medium text-red-500">
                    -₹{(order.subtotal - order.items.reduce((sum, item) => {
                      const returnedQty = item.returnedQuantity || 0;
                      const effectiveQty = item.quantity - returnedQty;
                      return sum + (item.price * effectiveQty);
                    }, 0)).toFixed(2)}
                  </td>
                </tr>
              )}
              <tr>
                <td colSpan={4} className="px-6 py-4 text-right text-base font-semibold text-gray-900">
                  Total
                </td>
                <td className="px-6 py-4 text-right text-base font-semibold text-gray-900">
                  ₹{order.total.toFixed(2)}
                </td>
              </tr>
            </tfoot>
          </table>
        </div>
      </Card>

      {/* Order Actions */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 flex flex-wrap gap-3 justify-end">
        {order.status !== 'cancelled' && (
          <>
            <Button
              variant="danger"
              icon={<XCircle size={16} />}
              onClick={() => updateOrderStatus('cancelled')}
            >
              Cancel Order
            </Button>

            {order.status === 'placed' && (
              <Button
                variant="primary"
                icon={<Package size={16} />}
                onClick={() => updateOrderStatus('processing')}
              >
                Mark as Processing
              </Button>
            )}

            {order.status === 'processing' && (
              <Button
                variant="primary"
                icon={<Truck size={16} />}
                onClick={() => updateOrderStatus('out_for_delivery')}
              >
                Mark as Out for Delivery
              </Button>
            )}

            {order.status === 'out_for_delivery' && (
              <>
                <Button
                  variant="primary"
                  icon={<CheckCircle size={16} />}
                  onClick={() => updateOrderStatus('delivered')}
                >
                  Mark as Delivered
                </Button>
                <Button
                  variant="warning"
                  icon={<RotateCcw size={16} />}
                  onClick={() => setShowReturnModal(true)}
                >
                  Return Items
                </Button>
              </>
            )}
          </>
        )}
      </div>
    </div>
  );
};

// Helper function to determine the status of each step in the timeline
const getStepStatus = (currentStatus: Order['status'], step: Order['status']) => {
  const statusOrder = {
    placed: 0,
    processing: 1,
    out_for_delivery: 2,
    delivered: 3,
    cancelled: -1, // Special case
  };

  if (currentStatus === 'cancelled') {
    return step === 'placed' ? 'completed' : 'cancelled';
  }

  if (statusOrder[currentStatus] === statusOrder[step]) {
    return 'current';
  } else if (statusOrder[currentStatus] > statusOrder[step]) {
    return 'completed';
  } else {
    return 'upcoming';
  }
};

interface StatusStepProps {
  icon: React.ReactNode;
  label: string;
  date: string;
  status: 'completed' | 'current' | 'upcoming' | 'cancelled';
  onClick: () => void;
}

const StatusStep = ({ icon, label, date, status, onClick }: StatusStepProps) => {
  const statusStyles = {
    completed: 'bg-green-500 text-white border-green-500',
    current: 'bg-primary-500 text-white border-primary-500 animate-pulse',
    upcoming: 'bg-white text-gray-400 border-gray-300',
    cancelled: 'bg-red-500 text-white border-red-500',
  };

  const textStyles = {
    completed: 'text-gray-900',
    current: 'text-gray-900 font-medium',
    upcoming: 'text-gray-400',
    cancelled: 'text-gray-400',
  };

  return (
    <div className="flex flex-col items-center min-w-[120px]">
      <button
        className={`h-12 w-12 rounded-full border-2 flex items-center justify-center transition-colors ${statusStyles[status]}`}
        onClick={onClick}
        disabled={status === 'cancelled'}
      >
        {icon}
      </button>
      <span className={`mt-2 text-sm font-medium ${textStyles[status]}`}>{label}</span>
      <span className="mt-1 text-xs text-gray-500">{date}</span>
    </div>
  );
};

interface StatusConnectorProps {
  status: 'completed' | 'current' | 'upcoming' | 'cancelled';
}

const StatusConnector = ({ status }: StatusConnectorProps) => {
  const statusStyles = {
    completed: 'bg-green-500',
    current: 'bg-gray-300',
    upcoming: 'bg-gray-300',
    cancelled: 'bg-gray-300',
  };

  return (
    <div className="flex-grow flex items-center justify-center mx-2 px-2">
      <div className={`h-1 w-full ${statusStyles[status]}`} />
    </div>
  );
};