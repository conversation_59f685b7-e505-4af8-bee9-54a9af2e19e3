import { useState, useEffect } from 'react';
import { X, AlertCircle, MinusCircle, PlusCircle } from 'lucide-react';
import { Button } from '../ui/Button';
import { Modal } from '../ui/Modal';
import { Order, OrderItem } from '../../types';
import { addReturn } from '../../services/returnService';
import { updateOrderItems } from '../../services/orderService';

interface ReturnItemsModalProps {
  isOpen: boolean;
  onClose: () => void;
  order: Order;
  onSuccess: () => void;
}

interface ReturnItem extends OrderItem {
  returnQuantity: number;
  isSelected: boolean;
  disabled: boolean;
}

export function ReturnItemsModal({ isOpen, onClose, order, onSuccess }: ReturnItemsModalProps) {
  const [returnItems, setReturnItems] = useState<ReturnItem[]>([]);
  const [returnReason, setReturnReason] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize return items from order items
  useEffect(() => {
    if (isOpen && order && order.items) {
      const items = order.items.map(item => {
        // Check if item is already fully returned
        const isFullyReturned = item.returned ||
          (item.returnedQuantity && item.returnedQuantity >= item.quantity);

        return {
          ...item,
          returnQuantity: 0,
          isSelected: false,
          // Add a disabled flag for fully returned items
          disabled: isFullyReturned
        };
      });
      setReturnItems(items);
    }
  }, [isOpen, order]);

  // Handle item selection - prevent selecting disabled items
  const handleItemSelection = (itemId: string) => {
    setReturnItems(prevItems =>
      prevItems.map(item =>
        item.id === itemId && !item.disabled
          ? { ...item, isSelected: !item.isSelected, returnQuantity: !item.isSelected ? 1 : 0 }
          : item
      )
    );
  };

  // Handle quantity change
  const handleQuantityChange = (itemId: string, newQuantity: number) => {
    setReturnItems(prevItems =>
      prevItems.map(item => {
        if (item.id === itemId) {
          // Calculate remaining quantity that can be returned
          const remainingQty = item.quantity - (item.returnedQuantity || 0);
          // Ensure quantity is within valid range (1 to remaining quantity)
          const validQuantity = Math.max(1, Math.min(newQuantity, remainingQty));
          return { ...item, returnQuantity: validQuantity };
        }
        return item;
      })
    );
  };

  // Calculate total refund amount
  const calculateRefundAmount = () => {
    return returnItems
      .filter(item => item.isSelected)
      .reduce((total, item) => total + (item.price * item.returnQuantity), 0);
  };

  // Process returns
  const handleProcessReturns = async () => {
    try {
      setLoading(true);
      setError(null);

      const selectedItems = returnItems.filter(item => item.isSelected && item.returnQuantity > 0);

      if (selectedItems.length === 0) {
        setError('Please select at least one item to return');
        return;
      }

      if (!returnReason.trim()) {
        setError('Please provide a reason for the return');
        return;
      }

      // Create return records for each selected item
      const returnPromises = selectedItems.map(async (item) => {
        const returnData = {
          branchCode: 'main',
          customerId: order.customerId || '',
          customerName: order.customerName || '',
          orderId: order.orderID || parseInt(order.id.slice(0, 7), 16) || Math.floor(Math.random() * 10000000),
          productId: item.productId,
          productName: item.productName,
          productImage: '',
          price: item.price,
          quantity: item.returnQuantity,
          minimumQuantity: 1,
          unit: item.unit || 'Pc',
          modeOfPayment: order.paymentMethod,
          returnStatus: 'approved', // Set as approved since admin is initiating the return
          returnReason: returnReason
        };

        return addReturn(returnData);
      });

      // Wait for all returns to be created
      await Promise.all(returnPromises);

      // Update the order with the returned items
      const updatedItems = order.items.map(orderItem => {
        const returnItem = returnItems.find(item => item.id === orderItem.id && item.isSelected);
        if (returnItem) {
          // If the entire quantity is returned, mark as returned
          if (returnItem.returnQuantity >= orderItem.quantity) {
            return {
              ...orderItem,
              returned: true,
              returnedQuantity: orderItem.quantity
            };
          } else {
            // Partial return
            return {
              ...orderItem,
              returnedQuantity: returnItem.returnQuantity
            };
          }
        }
        return orderItem;
      });

      // Update the order in the database
      await updateOrderItems(order.id, updatedItems);

      // Call the success callback
      onSuccess();
      onClose();
    } catch (err) {
      console.error('Error processing returns:', err);
      setError('Failed to process returns. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Return Items"
      maxWidth="lg"
    >
      <div className="p-6 space-y-6">
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-start">
            <AlertCircle className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" />
            <span>{error}</span>
          </div>
        )}

        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Select Items to Return</h3>

          <div className="border border-gray-200 rounded-md overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Select
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Item
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Price
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Ordered Qty
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Return Qty
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {returnItems.map((item) => (
                  <tr key={item.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        checked={item.isSelected}
                        onChange={() => handleItemSelection(item.id)}
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{item.productName}</div>
                      <div className="text-sm text-gray-500">{item.productId}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">₹{item.price.toFixed(2)}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{item.quantity} {item.unit}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {item.isSelected && (
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => handleQuantityChange(item.id, item.returnQuantity - 1)}
                            disabled={item.returnQuantity <= 1}
                            className="text-gray-500 hover:text-gray-700 disabled:opacity-50"
                          >
                            <MinusCircle className="h-5 w-5" />
                          </button>
                          <span className="text-sm font-medium w-8 text-center">{item.returnQuantity}</span>
                          <button
                            onClick={() => handleQuantityChange(item.id, item.returnQuantity + 1)}
                            disabled={item.returnQuantity >= item.quantity}
                            className="text-gray-500 hover:text-gray-700 disabled:opacity-50"
                          >
                            <PlusCircle className="h-5 w-5" />
                          </button>
                        </div>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          <div className="space-y-4">
            <div>
              <label htmlFor="returnReason" className="block text-sm font-medium text-gray-700">
                Return Reason
              </label>
              <textarea
                id="returnReason"
                rows={3}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="Please provide a reason for the return"
                value={returnReason}
                onChange={(e) => setReturnReason(e.target.value)}
              />
            </div>

            <div className="bg-gray-50 p-4 rounded-md">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-700">Total Refund Amount:</span>
                <span className="text-lg font-bold text-primary-600">₹{calculateRefundAmount().toFixed(2)}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3">
        <Button variant="outline" onClick={onClose}>
          Cancel
        </Button>
        <Button
          variant="primary"
          onClick={handleProcessReturns}
          disabled={loading || returnItems.filter(item => item.isSelected).length === 0}
          isLoading={loading}
        >
          Process Returns
        </Button>
      </div>
    </Modal>
  );
}



