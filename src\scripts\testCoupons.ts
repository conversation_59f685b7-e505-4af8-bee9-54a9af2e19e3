import { addCoupon, getCoupons, validateCoupon, calculateDiscount } from '../services/couponService';
import { Coupon } from '../types';

const createTestCoupons = async () => {
  try {
    console.log('Creating test coupons...');

    // Test coupon 1: Flat discount
    const flatCoupon: Omit<Coupon, 'id' | 'createdAt' | 'updatedAt'> = {
      code: 'FLAT50',
      type: 'FLAT',
      flatAmount: 50,
      minCartTotal: 200,
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
      usageLimitGlobal: 100,
      usageLimitPerUser: 1,
      usedCount: 0,
      isActive: true,
      description: 'Get ₹50 off on orders above ₹200'
    };

    // Test coupon 2: Percentage discount
    const percentCoupon: Omit<Coupon, 'id' | 'createdAt' | 'updatedAt'> = {
      code: 'SAVE20',
      type: 'PERCENT',
      percent: 20,
      maxDiscount: 100,
      minCartTotal: 500,
      expiresAt: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString(), // 60 days from now
      usageLimitGlobal: 500,
      usageLimitPerUser: 3,
      usedCount: 0,
      isActive: true,
      description: 'Get 20% off (max ₹100) on orders above ₹500'
    };

    // Test coupon 3: New user coupon
    const newUserCoupon: Omit<Coupon, 'id' | 'createdAt' | 'updatedAt'> = {
      code: 'NEWUSER25',
      type: 'PERCENT',
      percent: 25,
      maxDiscount: 150,
      minCartTotal: 300,
      startAt: new Date().toISOString(),
      expiresAt: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString(), // 90 days from now
      usageLimitGlobal: 1000,
      usageLimitPerUser: 1,
      usedCount: 0,
      isActive: true,
      description: 'Welcome offer: 25% off (max ₹150) for new users'
    };

    // Add the coupons
    const result1 = await addCoupon(flatCoupon);
    console.log('Flat coupon created:', result1);

    const result2 = await addCoupon(percentCoupon);
    console.log('Percent coupon created:', result2);

    const result3 = await addCoupon(newUserCoupon);
    console.log('New user coupon created:', result3);

    // Test validation
    console.log('\nTesting coupon validation...');
    
    const validation1 = await validateCoupon('FLAT50', 'test-user-1', 250);
    console.log('FLAT50 validation (₹250 cart):', validation1);

    const validation2 = await validateCoupon('SAVE20', 'test-user-1', 600);
    console.log('SAVE20 validation (₹600 cart):', validation2);

    const validation3 = await validateCoupon('NEWUSER25', 'test-user-1', 400);
    console.log('NEWUSER25 validation (₹400 cart):', validation3);

    // Test discount calculation
    console.log('\nTesting discount calculation...');
    
    if (validation1.valid && validation1.coupon) {
      const discount1 = calculateDiscount(validation1.coupon, 250);
      console.log('FLAT50 discount on ₹250:', discount1);
    }

    if (validation2.valid && validation2.coupon) {
      const discount2 = calculateDiscount(validation2.coupon, 600);
      console.log('SAVE20 discount on ₹600:', discount2);
    }

    if (validation3.valid && validation3.coupon) {
      const discount3 = calculateDiscount(validation3.coupon, 400);
      console.log('NEWUSER25 discount on ₹400:', discount3);
    }

    // List all coupons
    console.log('\nListing all coupons...');
    const allCoupons = await getCoupons(10);
    console.log('Total coupons:', allCoupons.coupons.length);
    allCoupons.coupons.forEach(coupon => {
      console.log(`- ${coupon.code}: ${coupon.type} ${coupon.type === 'FLAT' ? '₹' + coupon.flatAmount : coupon.percent + '%'}`);
    });

  } catch (error) {
    console.error('Error creating test coupons:', error);
  }
};

// Run the test if this file is executed directly
if (typeof window === 'undefined') {
  createTestCoupons();
}

export { createTestCoupons };
