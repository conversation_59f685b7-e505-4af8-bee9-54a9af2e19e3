import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Product } from '../types';
import {
  getAllProducts,
  getProductById,
  addProduct as addProductToFirebase,
  updateProduct as updateProductInFirebase,
  deleteProduct as deleteProductFromFirebase,
  uploadProductImage
} from '../services/productService';

interface ProductContextType {
  products: Product[];
  loading: boolean;
  error: string | null;
  addProduct: (product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => Promise<string | null>;
  updateProduct: (id: string, product: Partial<Product>) => Promise<boolean>;
  deleteProduct: (id: string) => Promise<boolean>;
  updateStock: (id: string, quantity: number) => Promise<boolean>;
  refreshProducts: () => Promise<void>;
  uploadImage: (file: File, productId: string) => Promise<string | null>;
}

const ProductContext = createContext<ProductContextType | undefined>(undefined);

export const useProducts = () => {
  const context = useContext(ProductContext);
  if (!context) {
    throw new Error('useProducts must be used within a ProductProvider');
  }
  return context;
};

interface ProductProviderProps {
  children: ReactNode;
}

export const ProductProvider = ({ children }: ProductProviderProps) => {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch products on component mount
  useEffect(() => {
    refreshProducts();
  }, []);

  // Refresh products from Firebase
  const refreshProducts = async (): Promise<void> => {
    try {
      setLoading(true);
      setError(null);
      const fetchedProducts = await getAllProducts();
      setProducts(fetchedProducts);
    } catch (err) {
      console.error('Error fetching products:', err);
      setError('Failed to load products. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Add a new product
  const addProduct = async (product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): Promise<string | null> => {
    try {
      const newProductId = await addProductToFirebase(product);
      if (newProductId) {
        await refreshProducts(); // Refresh the products list
        return newProductId;
      }
      return null;
    } catch (err) {
      console.error('Error adding product:', err);
      setError('Failed to add product. Please try again.');
      return null;
    }
  };

  // Update an existing product
  const updateProduct = async (id: string, updates: Partial<Product>): Promise<boolean> => {
    try {
      const success = await updateProductInFirebase(id, updates);
      if (success) {
        await refreshProducts(); // Refresh the products list
      }
      return success;
    } catch (err) {
      console.error('Error updating product:', err);
      setError('Failed to update product. Please try again.');
      return false;
    }
  };

  // Delete a product
  const deleteProduct = async (id: string): Promise<boolean> => {
    try {
      const success = await deleteProductFromFirebase(id);
      if (success) {
        await refreshProducts(); // Refresh the products list
      }
      return success;
    } catch (err) {
      console.error('Error deleting product:', err);
      setError('Failed to delete product. Please try again.');
      return false;
    }
  };

  // Update product stock
  const updateStock = async (id: string, quantity: number): Promise<boolean> => {
    try {
      // Get the current product
      const product = products.find(p => p.id === id);
      if (!product) return false;

      // Calculate new stock
      const newStock = Math.max(0, product.stock - quantity);

      // Update the product
      return await updateProduct(id, { stock: newStock });
    } catch (err) {
      console.error('Error updating stock:', err);
      setError('Failed to update stock. Please try again.');
      return false;
    }
  };

  // Upload product image
  const uploadImage = async (file: File, productId: string): Promise<string | null> => {
    try {
      return await uploadProductImage(file, productId);
    } catch (err) {
      console.error('Error uploading image:', err);
      setError('Failed to upload image. Please try again.');
      return null;
    }
  };

  return (
    <ProductContext.Provider value={{
      products,
      loading,
      error,
      addProduct,
      updateProduct,
      deleteProduct,
      updateStock,
      refreshProducts,
      uploadImage,
    }}>
      {children}
    </ProductContext.Provider>
  );
};