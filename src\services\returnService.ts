import { db, auth } from '../firebase/config';
import { collection, doc, getDoc, getDocs, query, where, orderBy, updateDoc, addDoc, Timestamp } from 'firebase/firestore';
import { checkAuth } from './authService';

// Collection name
const COLLECTION_NAME = 'Returns';

// Return status types
export type ReturnStatus = 'pending' | 'approved' | 'rejected' | 'completed';

// Return interface
export interface Return {
  id: string;
  branchCode: string;
  customerId: string;
  customerName: string;
  orderId: number;
  productId: string;
  productName: string;
  productImage: string;
  price: number;
  quantity: number;
  minimumQuantity: number;
  unit: string;
  modeOfPayment: string;
  returnStatus: ReturnStatus;
  returnInitiatedTime: string;
  returnReason?: string;
}

// Map Firestore document to Return type
const mapDocToReturn = (doc: any): Return => {
  const data = doc.data();

  // Convert Firestore timestamp to string
  const returnInitiatedTime = data.returnInitiatedTime instanceof Timestamp
    ? data.returnInitiatedTime.toDate().toISOString()
    : data.returnInitiatedTime;

  // Log the order ID fields for debugging
  console.log(`Return ${doc.id} order ID fields:`, {
    orderID: data.orderID,
    orderId: data.orderId,
    finalOrderId: data.orderID || data.orderId || 0
  });

  return {
    id: doc.id,
    branchCode: data.branchCode || '',
    customerId: data.customerID || data.customerId || '',
    customerName: data.customerName || '',
    orderId: data.orderID || data.orderId || 0,
    productId: data.productID || data.productId || '',
    productName: data.productName || '',
    productImage: data.productImage || '',
    price: data.price || 0,
    quantity: data.quantity || 0,
    minimumQuantity: data.minimumQuantity || 1,
    unit: data.unit || '',
    modeOfPayment: data.modeOfPayment || '',
    returnStatus: data.returnStatus || 'pending',
    returnInitiatedTime: returnInitiatedTime || new Date().toISOString(),
    returnReason: data.returnReason || '',
  };
};

// Get all returns
export const getAllReturns = async (): Promise<Return[]> => {
  try {
    // Check if user is authenticated
    const isAuthenticated = await checkAuth();
    if (!isAuthenticated) {
      console.log('User not authenticated');
      return [];
    }

    console.log('Fetching all returns');
    const returnsRef = collection(db, COLLECTION_NAME);

    try {
      // Query with ordering by returnInitiatedTime
      const q = query(
        returnsRef,
        orderBy('returnInitiatedTime', 'desc')
      );

      const querySnapshot = await getDocs(q);
      console.log(`Found ${querySnapshot.docs.length} returns`);

      // Map documents to Return type
      return querySnapshot.docs.map(mapDocToReturn);
    } catch (error) {
      console.log('Ordered query failed, falling back to simple query:', error);

      // If the ordered query fails, fall back to a simple query
      const simpleQ = query(returnsRef);
      const querySnapshot = await getDocs(simpleQ);
      console.log(`Found ${querySnapshot.docs.length} returns with simple query`);

      // Map documents to Return type and sort manually
      const returns = querySnapshot.docs.map(mapDocToReturn);
      return returns.sort((a, b) =>
        new Date(b.returnInitiatedTime).getTime() - new Date(a.returnInitiatedTime).getTime()
      );
    }
  } catch (error) {
    console.error('Error getting returns:', error);
    return [];
  }
};

// Get returns by customer ID
export const getReturnsByCustomerId = async (customerId: string): Promise<Return[]> => {
  try {
    // Check if user is authenticated
    const isAuthenticated = await checkAuth();
    if (!isAuthenticated) {
      console.log('User not authenticated');
      return [];
    }

    console.log(`Fetching returns for customer ID: ${customerId}`);
    const returnsRef = collection(db, COLLECTION_NAME);

    // Query returns by customer ID
    const q = query(
      returnsRef,
      where('customerID', '==', customerId),
      orderBy('returnInitiatedTime', 'desc')
    );

    try {
      const querySnapshot = await getDocs(q);
      console.log(`Found ${querySnapshot.docs.length} returns for customer ${customerId}`);
      return querySnapshot.docs.map(mapDocToReturn);
    } catch (error) {
      console.log('Customer ID query failed, trying alternative field name:', error);

      // Try with alternative field name
      const altQ = query(
        returnsRef,
        where('customerId', '==', customerId),
        orderBy('returnInitiatedTime', 'desc')
      );

      const altQuerySnapshot = await getDocs(altQ);
      console.log(`Found ${altQuerySnapshot.docs.length} returns with alternative field name`);
      return altQuerySnapshot.docs.map(mapDocToReturn);
    }
  } catch (error) {
    console.error(`Error getting returns for customer ${customerId}:`, error);
    return [];
  }
};

// Update return status
export const updateReturnStatus = async (
  returnId: string,
  newStatus: ReturnStatus
): Promise<boolean> => {
  try {
    // Check if user is authenticated
    const isAuthenticated = await checkAuth();
    if (!isAuthenticated) {
      console.log('User not authenticated');
      return false;
    }

    console.log(`Updating return ${returnId} status to ${newStatus}`);
    const returnRef = doc(db, COLLECTION_NAME, returnId);

    await updateDoc(returnRef, {
      returnStatus: newStatus,
      updatedAt: Timestamp.now()
    });

    console.log(`Successfully updated return ${returnId} status to ${newStatus}`);
    return true;
  } catch (error) {
    console.error(`Error updating return ${returnId} status:`, error);
    return false;
  }
};

// Get a single return by ID
export const getReturnById = async (returnId: string): Promise<Return | null> => {
  try {
    // Check if user is authenticated
    const isAuthenticated = await checkAuth();
    if (!isAuthenticated) {
      console.log('User not authenticated');
      return null;
    }

    console.log(`Fetching return with ID: ${returnId}`);
    const returnRef = doc(db, COLLECTION_NAME, returnId);
    const returnDoc = await getDoc(returnRef);

    if (returnDoc.exists()) {
      console.log(`Found return with ID: ${returnId}`);
      return mapDocToReturn(returnDoc);
    } else {
      console.log(`Return with ID ${returnId} not found`);
      return null;
    }
  } catch (error) {
    console.error(`Error getting return ${returnId}:`, error);
    return null;
  }
};

// Add a new return
export const addReturn = async (returnData: Omit<Return, 'id' | 'returnInitiatedTime'>): Promise<string | null> => {
  try {
    // Check if user is authenticated
    const isAuthenticated = await checkAuth();
    if (!isAuthenticated) {
      console.log('User not authenticated');
      return null;
    }

    console.log('Adding new return with data:', returnData);
    console.log('Order ID in return data:', {
      orderId: returnData.orderId,
      orderID: returnData.orderID
    });

    // Prepare the return document
    const returnDoc = {
      ...returnData,
      // Ensure both field naming conventions are included for better compatibility
      ...(returnData.customerId && { customerID: returnData.customerId }),
      ...(returnData.customerID && { customerId: returnData.customerID }),
      ...(returnData.productId && { productID: returnData.productId }),
      ...(returnData.productID && { productId: returnData.productID }),
      ...(returnData.orderId && { orderID: returnData.orderId }),
      ...(returnData.orderID && { orderId: returnData.orderID }),
      returnInitiatedTime: Timestamp.now(),
      returnStatus: returnData.returnStatus || 'pending',
      returnReason: returnData.returnReason || ''
    };

    console.log('Final return document to be saved:', returnDoc);

    // Add the return to Firestore
    const docRef = await addDoc(collection(db, COLLECTION_NAME), returnDoc);
    console.log(`Return added with ID: ${docRef.id}`);

    return docRef.id;
  } catch (error) {
    console.error('Error adding return:', error);
    return null;
  }
};
