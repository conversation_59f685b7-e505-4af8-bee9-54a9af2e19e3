rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Temporary rule to allow all read and write operations until October 5, 2024
    // This must be the FIRST rule to take precedence
    match /{document=**} {
      allow read, write: if request.time < timestamp.date(2024, 10, 5);
    }

    // The rules below will only apply AFTER October 5, 2024
    // They are included here for future reference

    match /Farmers/{document} {
      allow create: if true;
      allow read: if true;
      allow write: if request.auth != null;
      allow delete: if request.auth != null;
    }

    match /farmer/{document} {
      allow create: if true;
      allow read: if true;
      allow write: if request.auth != null;
      allow delete: if request.auth != null;
    }

    match /orders_online_unpaid/{document} {
      allow create: if true;
      allow read: if true;
      allow write: if request.auth != null;
      allow delete: if request.auth != null;
    }

    match /OnlinePayStatus/{document} {
      allow create: if true;
      allow read: if true;
      allow write: if request.auth != null;
      allow delete: if request.auth != null;
    }

    match /algoliaTemp/{document} {
      allow create: if true;
      allow read: if true;
      allow write: if request.auth != null;
      allow delete: if request.auth != null;
    }

    match /Returns/{document} {
      allow create: if true;
      allow read: if true;
      allow write: if request.auth != null;
      allow delete: if request.auth != null;
    }

    match /Transactions/{document} {
      allow create: if true;
      allow read: if true;
      allow write: if request.auth != null;
      allow delete: if request.auth != null;
    }

    match /DeleteAccountRequest/{document} {
      allow create: if true;
      allow read: if true;
      allow write: if request.auth != null;
      allow delete: if request.auth != null;
    }

    match /DeliveryAgents/{document} {
      allow create: if request.auth != null;
      allow read: if request.auth != null;
      allow write: if request.auth != null;
      allow delete: if request.auth != null;
    }

    match /Support/{document} {
      allow create: if true;
      allow read: if true;
      allow write: if request.auth != null;
      allow delete: if request.auth != null;
    }

    match /Drivers/{document} {
      allow create: if true;
      allow read: if true;
      allow write: if false;
      allow delete: if false;
    }

    match /Pincodes/{document} {
      allow create: if true;
      allow read: if true;
      allow write: if request.auth != null;
      allow delete: if request.auth != null;
    }

    match /Orders/{document} {
      allow create: if true;
      allow read: if true;
      allow write: if request.auth != null;
      allow delete: if request.auth != null;
    }

    match /Categories/{document} {
      allow create: if true;
      allow read: if true;
      allow write: if request.auth != null;
      allow delete: if request.auth != null;
    }

    match /Products/{document} {
      allow create: if true;
      allow read: if true;
      allow write: if request.auth != null;
      allow delete: if request.auth != null;
    }

    // Allow admin users to access all Users documents
    match /Users/<USER>
      allow create: if request.auth.uid == document ||
                     (request.auth != null &&
                      get(/databases/$(database)/documents/Users/<USER>'admin');
      allow read: if request.auth.uid == document ||
                   (request.auth != null &&
                    get(/databases/$(database)/documents/Users/<USER>'admin');
      allow write: if request.auth.uid == document ||
                    (request.auth != null &&
                     get(/databases/$(database)/documents/Users/<USER>'admin');
      allow delete: if request.auth.uid == document ||
                     (request.auth != null &&
                      get(/databases/$(database)/documents/Users/<USER>'admin');
    }

    // Special access for FlutterFlow
    match /{document=**} {
      allow read, write: if request.auth != null && request.auth.token.email.matches("<EMAIL>");
    }

    // Default deny rule - this should always be last
    match /{document=**} {
      allow read, write: if false;
    }
  }
}


