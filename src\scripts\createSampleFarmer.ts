import { addFarmer } from '../firebase/farmers';

const createSampleFarmer = async () => {
  try {
    const sampleFarmer = {
      farmerName: "<PERSON><PERSON>",
      farmName: "Organic Valley Farms",
      farmLocation: "Organic Farm, Nashik",
      experience: "10+ Years Experience",
      philosophy: "I believe in sustainable farming practices that not only produce the best quality food but also protect our environment for future generations.",
      certifications: ["Certified Organic Farm", "Quality Assured"],
      tags: ["100% Organic", "No chemical pesticides", "Best Price", "Fast Delivery", "Quality Assured"]
    };

    const farmerId = await addFarmer(sampleFarmer);
    console.log(`Sample farmer created with ID: ${farmerId}`);
  } catch (error) {
    console.error('Error creating sample farmer:', error);
  }
};

createSampleFarmer();