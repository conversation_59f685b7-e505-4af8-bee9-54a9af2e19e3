import { useState, useEffect } from 'react';
import { X, Search, ShoppingBag, Package, User, Calendar } from 'lucide-react';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Select } from '../ui/Select';
import { Order } from '../../types/index';
import { getAllOrders, getOrderById } from '../../services/orderService';
import { addReturn } from '../../services/returnService';

interface CreateReturnModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export function CreateReturnModal({ isOpen, onClose, onSuccess }: CreateReturnModalProps) {
  const [step, setStep] = useState<'select-order' | 'select-product' | 'confirm'>('select-order');
  const [orders, setOrders] = useState<Order[]>([]);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [selectedProductId, setSelectedProductId] = useState<string>('');
  const [quantity, setQuantity] = useState<number>(1);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch orders on component mount
  useEffect(() => {
    if (isOpen) {
      fetchOrders();
    }
  }, [isOpen]);

  // Fetch orders from the API
  const fetchOrders = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await getAllOrders();
      // Filter out orders that are cancelled
      const filteredOrders = data.filter(order => order.status !== 'cancelled');
      console.log('Fetched orders for return creation:', filteredOrders);
      setOrders(filteredOrders);
    } catch (err) {
      console.error('Error fetching orders:', err);
      setError('Failed to load orders. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Filter orders based on search query
  const filteredOrders = orders.filter(order => {
    const matchesSearch =
      order.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      order.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (order.customerPhone && order.customerPhone.includes(searchQuery));

    return matchesSearch;
  });

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  // Handle order selection
  const handleOrderSelect = (order: Order) => {
    setSelectedOrder(order);
    setStep('select-product');
  };

  // Handle product selection
  const handleProductSelect = (productId: string) => {
    setSelectedProductId(productId);
    // Find the product in the order to set the initial quantity
    const product = selectedOrder?.items.find(item => item.productId === productId);
    if (product) {
      setQuantity(1); // Default to 1 or you could use product.quantity
    }
    setStep('confirm');
  };

  // Handle return creation
  const handleCreateReturn = async () => {
    if (!selectedOrder || !selectedProductId) {
      setError('Please select an order and product');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Find the selected product in the order
      const product = selectedOrder.items.find(item => item.productId === selectedProductId);

      if (!product) {
        setError('Selected product not found in order');
        setLoading(false);
        return;
      }

      // Create return data
      const returnData = {
        branchCode: 'test', // Default branch code
        customerId: selectedOrder.customerId,
        customerName: selectedOrder.customerName,
        orderId: selectedOrder.orderID || parseInt(selectedOrder.id.slice(0, 7), 16) || Math.floor(Math.random() * 10000000), // Use orderID if available, otherwise convert part of ID to number or use random
        productId: product.productId,
        productName: product.productName,
        productImage: '', // No image available in the order item
        price: product.price,
        quantity: quantity,
        minimumQuantity: 1,
        unit: product.unit || 'Pc',
        modeOfPayment: selectedOrder.paymentMethod,
        returnStatus: 'approved' // Set as approved since admin is creating the return
      };

      console.log('Creating return with data:', returnData);
      const returnId = await addReturn(returnData);

      if (returnId) {
        console.log('Return created successfully with ID:', returnId);
        onSuccess();
        onClose();
      } else {
        setError('Failed to create return. Please try again.');
      }
    } catch (err) {
      console.error('Error creating return:', err);
      setError('Failed to create return. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Reset state when modal is closed
  const handleClose = () => {
    setStep('select-order');
    setSelectedOrder(null);
    setSelectedProductId('');
    setQuantity(1);
    setSearchQuery('');
    setError(null);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-3xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <h2 className="text-xl font-semibold text-gray-800">
            {step === 'select-order' && 'Create Return - Select Order'}
            {step === 'select-product' && 'Create Return - Select Product'}
            {step === 'confirm' && 'Create Return - Confirm Details'}
          </h2>
          <button
            onClick={handleClose}
            className="text-gray-500 hover:text-gray-700 focus:outline-none"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto" style={{ maxHeight: 'calc(90vh - 130px)' }}>
          {error && (
            <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-4">
              <div className="flex items-center">
                <p className="text-red-700">{error}</p>
              </div>
            </div>
          )}

          {/* Step 1: Select Order */}
          {step === 'select-order' && (
            <div>
              <div className="mb-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search orders by ID or customer name..."
                    className="pl-10 pr-4 py-2 w-full border-2 border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>

              {loading ? (
                <div className="flex justify-center items-center h-64">
                  <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
                </div>
              ) : filteredOrders.length === 0 ? (
                <div className="text-center py-8">
                  <ShoppingBag className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">No orders found</p>
                </div>
              ) : (
                <div className="grid gap-4">
                  {filteredOrders.map(order => (
                    <div
                      key={order.id}
                      className="border border-gray-200 rounded-lg p-4 hover:border-primary-500 cursor-pointer transition-colors"
                      onClick={() => handleOrderSelect(order)}
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="font-medium text-gray-900">Order {order.orderID ? `#${order.orderID}` : `#${order.id.slice(0, 8)}`}</h3>
                          <div className="mt-1 text-sm text-gray-600">
                            <div className="flex items-center">
                              <Calendar className="h-4 w-4 mr-1" />
                              <span>{formatDate(order.createdAt)}</span>
                            </div>
                            <div className="flex items-center mt-1">
                              <User className="h-4 w-4 mr-1" />
                              <span>{order.customerName}</span>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <span className="font-medium text-gray-900">₹{order.total.toFixed(2)}</span>
                          <div className="text-sm text-gray-600 mt-1">{order.items.length} items</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Step 2: Select Product */}
          {step === 'select-product' && selectedOrder && (
            <div>
              <div className="mb-4">
                <h3 className="font-medium text-gray-900 mb-2">Order {selectedOrder.orderID ? `#${selectedOrder.orderID}` : `#${selectedOrder.id.slice(0, 8)}`}</h3>
                <div className="text-sm text-gray-600">
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-1" />
                    <span>{formatDate(selectedOrder.createdAt)}</span>
                  </div>
                  <div className="flex items-center mt-1">
                    <User className="h-4 w-4 mr-1" />
                    <span>{selectedOrder.customerName}</span>
                  </div>
                </div>
              </div>

              <h3 className="font-medium text-gray-900 mb-2">Select Product for Return</h3>
              <div className="grid gap-3">
                {selectedOrder.items.map(item => (
                  <div
                    key={item.productId || item.id}
                    className="border border-gray-200 rounded-lg p-3 hover:border-primary-500 cursor-pointer transition-colors"
                    onClick={() => handleProductSelect(item.productId || item.id)}
                  >
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <div className="h-10 w-10 bg-gray-100 rounded-md flex items-center justify-center mr-3">
                          <Package className="h-6 w-6 text-gray-400" />
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">{item.productName}</h4>
                          <div className="text-sm text-gray-600">
                            {item.quantity} {item.unit} × ₹{item.price.toFixed(2)}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <span className="font-medium text-gray-900">₹{(item.price * item.quantity).toFixed(2)}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Step 3: Confirm Return Details */}
          {step === 'confirm' && selectedOrder && (
            <div>
              <div className="mb-4">
                <h3 className="font-medium text-gray-900 mb-2">Return Details</h3>

                {selectedOrder.items
                  .filter(item => item.productId === selectedProductId || item.id === selectedProductId)
                  .map(item => (
                    <div key={item.productId || item.id} className="border border-gray-200 rounded-lg p-4 mb-4">
                      <div className="flex items-start">
                        <div className="h-12 w-12 bg-gray-100 rounded-md flex items-center justify-center mr-3">
                          <Package className="h-8 w-8 text-gray-400" />
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">{item.productName}</h4>
                          <div className="text-sm text-gray-600 mt-1">
                            Original Order: {item.quantity} {item.unit} × ₹{item.price.toFixed(2)}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}

                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Return Quantity
                  </label>
                  <Input
                    type="number"
                    min="1"
                    max={selectedOrder.items.find(item => item.productId === selectedProductId)?.quantity || 1}
                    value={quantity}
                    onChange={(e) => setQuantity(parseInt(e.target.value) || 1)}
                  />
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-200 flex justify-between">
          <Button
            variant="outline"
            onClick={() => {
              if (step === 'select-order') {
                handleClose();
              } else if (step === 'select-product') {
                setStep('select-order');
              } else if (step === 'confirm') {
                setStep('select-product');
              }
            }}
          >
            {step === 'select-order' ? 'Cancel' : 'Back'}
          </Button>

          {step === 'select-order' && (
            <Button
              variant="primary"
              disabled={loading}
              onClick={fetchOrders}
            >
              Refresh Orders
            </Button>
          )}

          {step === 'confirm' && (
            <Button
              variant="primary"
              disabled={loading}
              isLoading={loading}
              onClick={handleCreateReturn}
            >
              Create Return
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
