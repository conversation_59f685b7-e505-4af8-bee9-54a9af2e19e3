import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import {
  Search,
  Plus,
  RefreshCw,
  User,
  Leaf,
  MapPin,
  Award,
  Tag,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  X
} from 'lucide-react';
import { Card } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { Badge } from '../../components/ui/Badge';
import { collection, getDocs, query, orderBy } from 'firebase/firestore';
import { db } from '../../firebase/config';
import { Farmer } from '../../firebase/farmers';

export function FarmersList() {
  const [farmers, setFarmers] = useState<Farmer[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 8;

  const fetchFarmers = async () => {
    setLoading(true);
    setError(null);
    try {
      const farmersQuery = query(collection(db, 'Farmers'), orderBy('createdAt', 'desc'));
      const querySnapshot = await getDocs(farmersQuery);

      const farmersData = querySnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          farmerName: data.farmerName || '',
          farmName: data.farmName || '',
          farmLocation: data.farmLocation || '',
          experience: data.experience || '',
          philosophy: data.philosophy || '',
          certifications: data.certifications || [],
          tags: data.tags || [],
          createdAt: data.createdAt?.toDate?.()
            ? data.createdAt.toDate().toISOString()
            : (data.createdAt || new Date().toISOString()),
          updatedAt: data.updatedAt?.toDate?.()
            ? data.updatedAt.toDate().toISOString()
            : (data.updatedAt || new Date().toISOString())
        } as Farmer;
      });

      setFarmers(farmersData);
    } catch (err) {
      console.error('Error fetching farmers:', err);
      setError('Failed to load farmers. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchFarmers();
  }, []);

  const handleRefresh = () => {
    fetchFarmers();
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1);
  };

  const handleClearSearch = () => {
    setSearchQuery('');
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  };

  // Filter farmers based on search query
  const filteredFarmers = farmers.filter(farmer => {
    const searchLower = searchQuery.toLowerCase();
    return (
      farmer.farmerName.toLowerCase().includes(searchLower) ||
      farmer.farmName.toLowerCase().includes(searchLower) ||
      farmer.farmLocation.toLowerCase().includes(searchLower) ||
      farmer.tags.some(tag => tag.toLowerCase().includes(searchLower))
    );
  });

  // Pagination logic
  const totalPages = Math.ceil(filteredFarmers.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentFarmers = filteredFarmers.slice(indexOfFirstItem, indexOfLastItem);

  const Pagination = () => {
    if (totalPages <= 1) return null;

    return (
      <div className="flex justify-center mt-8">
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(1)}
            disabled={currentPage === 1}
          >
            <ChevronsLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>

          <span className="text-sm text-gray-600 px-2">
            Page {currentPage} of {totalPages}
          </span>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(totalPages)}
            disabled={currentPage === totalPages}
          >
            <ChevronsRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    );
  };

  return (
    <div className="p-6 animate-fade-in">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Farmers</h1>
          <p className="text-gray-600">Meet the people who grow your food</p>
        </div>
        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={loading}
            className="shadow-sm hover:shadow transition-shadow"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Link to="/farmers/new">
            <Button
              variant="primary"
              className="shadow-md hover:shadow-lg transition-shadow"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Farmer
            </Button>
          </Link>
        </div>
      </div>

      {/* Search */}
      <div className="mb-6">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Search className="w-5 h-5 text-gray-400" />
          </div>
          <input
            ref={searchInputRef}
            type="text"
            className="block w-full p-3 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
            placeholder="Search farmers by name, farm name, or location..."
            value={searchQuery}
            onChange={handleSearchChange}
          />
          {searchQuery && (
            <button
              className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
              onClick={handleClearSearch}
              type="button"
              aria-label="Clear search"
            >
              <X className="w-5 h-5" />
            </button>
          )}
        </div>
      </div>

      {/* Farmers List */}
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="flex flex-col items-center">
            <RefreshCw className="w-12 h-12 text-primary-500 animate-spin mb-4" />
            <p className="text-gray-600">Loading farmers...</p>
          </div>
        </div>
      ) : filteredFarmers.length === 0 ? (
        <div className="flex justify-center items-center h-64">
          <div className="flex flex-col items-center">
            <User className="w-12 h-12 text-gray-400 mb-4" />
            <p className="text-gray-600">No farmers found</p>
            <p className="text-gray-500 text-sm mt-2">Try adjusting your search or add a new farmer</p>
            <Link to="/farmers/new">
              <Button
                variant="primary"
                className="mt-4"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add New Farmer
              </Button>
            </Link>
          </div>
        </div>
      ) : (
        <>
          <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {currentFarmers.map((farmer) => (
              <Link key={farmer.id} to={`/farmers/${farmer.id}`}>
                <Card className="h-full overflow-hidden hover:shadow-lg transition-all duration-300 border border-gray-200 hover:border-primary-200">
                  <div className="p-6">
                    <div className="flex items-center mb-4">
                      <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mr-3">
                        <User className="w-6 h-6 text-primary-600" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">{farmer.farmerName}</h3>
                        <p className="text-sm text-gray-500">{farmer.experience}</p>
                      </div>
                    </div>

                    <div className="mb-4">
                      <div className="flex items-start mb-2">
                        <Leaf className="w-4 h-4 text-green-600 mt-1 mr-2 flex-shrink-0" />
                        <p className="text-gray-700">{farmer.farmName}</p>
                      </div>
                      <div className="flex items-start mb-2">
                        <MapPin className="w-4 h-4 text-gray-500 mt-1 mr-2 flex-shrink-0" />
                        <p className="text-gray-700">{farmer.farmLocation}</p>
                      </div>
                    </div>

                    <div className="mb-4">
                      <p className="text-gray-700 italic text-sm line-clamp-2">"{farmer.philosophy}"</p>
                    </div>

                    <div className="mb-4">
                      {farmer.certifications.map((cert, index) => (
                        <div key={index} className="flex items-center mb-1">
                          <Award className="w-4 h-4 text-amber-500 mr-2" />
                          <span className="text-sm text-gray-700">{cert}</span>
                        </div>
                      ))}
                    </div>

                    <div className="flex flex-wrap gap-2">
                      {farmer.tags.slice(0, 3).map((tag, index) => (
                        <Badge key={index} variant="secondary" className="flex items-center">
                          <Tag className="w-3 h-3 mr-1" />
                          {tag}
                        </Badge>
                      ))}
                      {farmer.tags.length > 3 && (
                        <Badge variant="outline">+{farmer.tags.length - 3} more</Badge>
                      )}
                    </div>
                  </div>
                </Card>
              </Link>
            ))}
          </div>

          <Pagination />
        </>
      )}
    </div>
  );
}
