import { useState, useEffect } from 'react';
import { X, Plus, User, Mail, Phone, MapPin } from 'lucide-react';
import { Button } from '../ui/Button';
import { Modal } from '../ui/Modal';
import { Input } from '../ui/Input';
import { Select } from '../ui/Select';
import { Customer, Address } from '../../types';

interface AddCustomerFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (customer: Customer) => void;
  isFromPOS?: boolean;
  initialData?: Customer; // Add this prop
  isEditing?: boolean; // Add this prop
}

export const AddCustomerForm = ({
  isOpen,
  onClose,
  onSave,
  isFromPOS = false,
  initialData,
  isEditing = false
}: AddCustomerFormProps) => {
  console.log('AddCustomerForm rendered with isOpen:', isOpen);
  console.log('initialData:', initialData);
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [addresses, setAddresses] = useState<Partial<Address>[]>([
    {
      type: 'home',
      line1: '',
      line2: '',
      pincode: '',
      isDefault: true,
    },
  ]);

  // Load initial data if editing
  useEffect(() => {
    if (initialData && isOpen) {
      setName(initialData.name);
      setEmail(initialData.email);
      setPhone(initialData.phone);
      setAddresses(initialData.addresses.map(addr => ({...addr})));
    }
  }, [initialData, isOpen]);

  const handleAddAddress = () => {
    setAddresses([
      ...addresses,
      {
        type: 'home',
        line1: '',
        line2: '',
        pincode: '',
        isDefault: false,
      },
    ]);
  };

  const handleRemoveAddress = (index: number) => {
    const newAddresses = [...addresses];
    newAddresses.splice(index, 1);

    // If we removed the default address and there are other addresses, make the first one default
    if (addresses[index].isDefault && newAddresses.length > 0) {
      newAddresses[0].isDefault = true;
    }

    setAddresses(newAddresses);
  };

  const handleAddressChange = (index: number, field: keyof Address, value: string | boolean) => {
    const newAddresses = [...addresses];
    newAddresses[index] = { ...newAddresses[index], [field]: value };
    setAddresses(newAddresses);
  };

  const handleSetDefault = (index: number) => {
    const newAddresses = addresses.map((address, i) => ({
      ...address,
      isDefault: i === index,
    }));
    setAddresses(newAddresses);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Form submitted');

    // For editing, we'll skip validation
    if (!isEditing && !isFormValid()) {
      console.log('Form validation failed');
      return;
    }

    try {
      // Ensure phone number has +91 prefix
      let formattedPhone = phone;
      if (formattedPhone && !formattedPhone.startsWith('+91')) {
        formattedPhone = '+91' + formattedPhone;
      }

      // Prepare customer data
      const customerData: Omit<Customer, 'id' | 'createdAt'> = {
        name,
        phone: formattedPhone,
        addresses: addresses.map((addr, index) => {
          // Create a complete address object with all required fields
          return {
            id: addr.id || `addr-${index + 1}`,
            type: addr.type || 'home',
            line1: addr.line1 || '',
            line2: addr.line2 || undefined,
            city: addr.city || '',
            state: addr.state || undefined,
            pincode: addr.pincode || '',
            isDefault: addr.isDefault || false,
            // Use the customer's phone number for the address
            phoneNumber: formattedPhone
          } as Address;
        }),
        totalOrders: isEditing && initialData ? initialData.totalOrders : 0,
        totalSpent: isEditing && initialData ? initialData.totalSpent : 0,
        lastOrderDate: isEditing && initialData ? initialData.lastOrderDate : undefined,
        status: isEditing && initialData ? initialData.status : 'active'
      };

      // Only add email if it's provided
      if (email) {
        customerData.email = email;
      }

      console.log('Saving customer data:', customerData);

      // If editing, preserve the ID and createdAt
      if (isEditing && initialData) {
        const editedCustomer = {
          ...customerData,
          id: initialData.id,
          createdAt: initialData.createdAt
        } as Customer;

        console.log('Edited customer data:', editedCustomer);
        onSave(editedCustomer);
      } else {
        onSave(customerData as any);
        resetForm();
      }
      onClose();
    } catch (error) {
      console.error('Error in handleSubmit:', error);
      alert('An error occurred while saving. Please try again.');
    }
  };

  const resetForm = () => {
    setName('');
    setEmail('');
    setPhone('');
    setAddresses([
      {
        type: 'home',
        line1: '',
        line2: '',
        pincode: '',
        isDefault: true,
      },
    ]);
  };

  const isFormValid = () => {
    console.log('Checking form validity...');
    console.log('Name:', name);
    console.log('Phone:', phone);
    console.log('Addresses:', addresses);

    // Name and phone are required
    if (!name || !phone) {
      console.log('Form invalid: Missing name or phone');
      return false;
    }

    // At least one address is required
    if (addresses.length === 0) {
      console.log('Form invalid: No addresses');
      return false;
    }

    // For editing, we'll be more lenient - if there's at least one address, we'll allow it
    if (isEditing) {
      // Just make sure there's at least one address with some data
      const hasValidAddress = addresses.some(address =>
        address && (address.line1 || address.pincode)
      );

      if (!hasValidAddress) {
        console.log('Form invalid: No valid addresses for editing');
        return false;
      }

      console.log('Form valid for editing');
      return true;
    }

    // For new customers, each address must have required fields
    for (const address of addresses) {
      if (!address.line1 || !address.pincode) {
        console.log('Form invalid: Address missing required fields', address);
        return false;
      }
    }

    console.log('Form valid');
    return true;
  };



  // Log when modal renders
  console.log('Modal rendering with isOpen:', isOpen);

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={isEditing ? "Edit Customer" : "Add New Customer"}
      maxWidth="2xl"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Customer Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Customer Information</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Input
                id="name"
                label="Name"
                type="text"
                value={name}
                onChange={(e) => setName(e.target.value)}
                required
                startIcon={<User className="h-4 w-4 text-gray-400" />}
              />
            </div>

            <div>
              <Input
                id="phone"
                label="Phone"
                type="tel"
                value={phone}
                onChange={(e) => {
                  let inputValue = e.target.value;

                  // Remove any non-digit characters except for the + sign at the beginning
                  inputValue = inputValue.replace(/[^\d+]/g, '');

                  // If the input is not empty and doesn't start with +91, add it
                  if (inputValue && !inputValue.startsWith('+91') && !inputValue.startsWith('+')) {
                    // If the user is typing a new number (not editing an existing one)
                    if (phone === '' || phone === '+91') {
                      inputValue = '+91' + inputValue;
                    }
                  }

                  setPhone(inputValue);
                }}
                placeholder="+91 XXXXX XXXXX"
                required
                startIcon={<Phone className="h-4 w-4 text-gray-400" />}
                helperText="Indian mobile number with country code (+91)"
              />
            </div>

            <div className="md:col-span-2">
              <Input
                id="email"
                label="Email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                startIcon={<Mail className="h-4 w-4 text-gray-400" />}
              />
            </div>
          </div>
        </div>

        {/* Addresses */}
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-900">Addresses</h3>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleAddAddress}
              icon={<Plus size={16} />}
            >
              Add Address
            </Button>
          </div>

          {addresses.map((address, index) => (
            <div key={index} className="border border-gray-200 rounded-md p-4 space-y-4">
              <div className="flex justify-between items-center">
                <div className="flex items-center space-x-2">
                  <Select
                    value={address.type}
                    onChange={(e) => handleAddressChange(index, 'type', e.target.value)}
                    options={[
                      { value: 'home', label: 'Home' },
                      { value: 'office', label: 'Office' },
                      { value: 'other', label: 'Other' }
                    ]}
                  />

                  {address.isDefault ? (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                      Default
                    </span>
                  ) : (
                    <button
                      type="button"
                      onClick={() => handleSetDefault(index)}
                      className="text-xs text-primary-600 hover:text-primary-800"
                    >
                      Set as default
                    </button>
                  )}
                </div>

                {addresses.length > 1 && (
                  <button
                    type="button"
                    onClick={() => handleRemoveAddress(index)}
                    className="text-gray-400 hover:text-gray-500"
                  >
                    <X size={16} />
                  </button>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="md:col-span-2">
                  <Input
                    label="Address Line 1"
                    type="text"
                    value={address.line1 || ''}
                    onChange={(e) => handleAddressChange(index, 'line1', e.target.value)}
                    required
                    startIcon={<MapPin className="h-4 w-4 text-gray-400" />}
                  />
                </div>

                <div className="md:col-span-2">
                  <Input
                    label="Address Line 2"
                    type="text"
                    value={address.line2 || ''}
                    onChange={(e) => handleAddressChange(index, 'line2', e.target.value)}
                  />
                </div>

                <div className="md:col-span-2">
                  <Input
                    label="Pincode"
                    type="text"
                    value={address.pincode || ''}
                    onChange={(e) => handleAddressChange(index, 'pincode', e.target.value)}
                    required
                  />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <Button
            type="button"
            variant="outline"
            onClick={() => {
              resetForm();
              onClose();
            }}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="primary"
            disabled={isEditing ? false : !isFormValid()}
          >
            {isEditing ? 'Save Changes' : (isFromPOS ? 'Save & Select' : 'Save Customer')}
          </Button>
        </div>
      </form>
    </Modal>
  );
};







