import React, { useState, useEffect } from 'react';
import { Plus, RefreshCw, Search, MapPin } from 'lucide-react';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Loader } from '../ui/Loader';
import { DeliveryChargeCard } from './DeliveryChargeCard';
import { DeliveryChargeForm } from './DeliveryChargeForm';
import { DeliveryCharge } from '../../types';
import { 
  getDeliveryCharges, 
  addDeliveryCharge, 
  updateDeliveryCharge, 
  deleteDeliveryCharge,
  searchDeliveryCharges 
} from '../../services/deliveryChargeService';

export const DeliveryChargeList = () => {
  const [deliveryCharges, setDeliveryCharges] = useState<DeliveryCharge[]>([]);
  const [filteredDeliveryCharges, setFilteredDeliveryCharges] = useState<DeliveryCharge[]>([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingDeliveryCharge, setEditingDeliveryCharge] = useState<DeliveryCharge | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);

  // Load delivery charges
  const loadDeliveryCharges = async () => {
    setLoading(true);
    try {
      const result = await getDeliveryCharges(100); // Load more delivery charges for now
      setDeliveryCharges(result.deliveryCharges);
      setFilteredDeliveryCharges(result.deliveryCharges);
    } catch (error) {
      console.error('Error loading delivery charges:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadDeliveryCharges();
  }, []);

  // Handle search
  useEffect(() => {
    const performSearch = async () => {
      if (searchQuery.trim()) {
        setIsSearching(true);
        try {
          const searchResults = await searchDeliveryCharges(searchQuery);
          setFilteredDeliveryCharges(searchResults);
        } catch (error) {
          console.error('Error searching delivery charges:', error);
          setFilteredDeliveryCharges([]);
        } finally {
          setIsSearching(false);
        }
      } else {
        setFilteredDeliveryCharges(deliveryCharges);
      }
    };

    const debounceTimer = setTimeout(performSearch, 300);
    return () => clearTimeout(debounceTimer);
  }, [searchQuery, deliveryCharges]);

  const handleAddDeliveryCharge = async (deliveryChargeData: Omit<DeliveryCharge, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      const result = await addDeliveryCharge(deliveryChargeData);
      if (result) {
        await loadDeliveryCharges(); // Reload to get the latest data
        setShowForm(false);
      } else {
        alert('Failed to add delivery charge. Please try again.');
      }
    } catch (error) {
      console.error('Error adding delivery charge:', error);
      if (error instanceof Error && error.message === 'Pincode already exists') {
        alert('This pincode already exists. Please use a different pincode.');
      } else {
        alert('Error adding delivery charge. Please try again.');
      }
    }
  };

  const handleEditDeliveryCharge = async (deliveryChargeData: Omit<DeliveryCharge, 'id' | 'createdAt' | 'updatedAt'>) => {
    if (!editingDeliveryCharge) return;

    try {
      const result = await updateDeliveryCharge(editingDeliveryCharge.id, deliveryChargeData);
      if (result) {
        await loadDeliveryCharges(); // Reload to get the latest data
        setEditingDeliveryCharge(null);
        setShowForm(false);
      } else {
        alert('Failed to update delivery charge. Please try again.');
      }
    } catch (error) {
      console.error('Error updating delivery charge:', error);
      if (error instanceof Error && error.message === 'Pincode already exists') {
        alert('This pincode already exists. Please use a different pincode.');
      } else {
        alert('Error updating delivery charge. Please try again.');
      }
    }
  };

  const handleDeleteDeliveryCharge = async (deliveryCharge: DeliveryCharge) => {
    if (!confirm(`Are you sure you want to delete the delivery charge for pincode "${deliveryCharge.pincode}"?`)) {
      return;
    }

    try {
      const result = await deleteDeliveryCharge(deliveryCharge.id);
      if (result) {
        await loadDeliveryCharges(); // Reload to get the latest data
      } else {
        alert('Failed to delete delivery charge. Please try again.');
      }
    } catch (error) {
      console.error('Error deleting delivery charge:', error);
      alert('Error deleting delivery charge. Please try again.');
    }
  };

  const openEditForm = (deliveryCharge: DeliveryCharge) => {
    setEditingDeliveryCharge(deliveryCharge);
    setShowForm(true);
  };

  const closeForm = () => {
    setShowForm(false);
    setEditingDeliveryCharge(null);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <Loader size="large" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Delivery Charges</h2>
          <p className="text-gray-600">Manage delivery charges for different pincodes and areas</p>
        </div>
        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={loadDeliveryCharges}
            disabled={loading}
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button
            variant="primary"
            onClick={() => setShowForm(true)}
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Delivery Charge
          </Button>
        </div>
      </div>

      {/* Search */}
      <div className="flex items-center gap-4">
        <div className="flex-1 max-w-md">
          <Input
            placeholder="Search by pincode or area name..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            startIcon={<Search className="w-4 h-4 text-gray-400" />}
          />
        </div>
        <div className="flex items-center text-sm text-gray-600">
          <MapPin className="w-4 h-4 mr-2" />
          {isSearching ? 'Searching...' : `${filteredDeliveryCharges.length} of ${deliveryCharges.length} areas`}
        </div>
      </div>

      {/* Delivery Charges Grid */}
      {filteredDeliveryCharges.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-500 mb-4">
            {deliveryCharges.length === 0 ? 'No delivery charges found' : 'No delivery charges match your search'}
          </div>
          {deliveryCharges.length === 0 && (
            <Button
              variant="primary"
              onClick={() => setShowForm(true)}
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Your First Delivery Charge
            </Button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredDeliveryCharges.map((deliveryCharge) => (
            <DeliveryChargeCard
              key={deliveryCharge.id}
              deliveryCharge={deliveryCharge}
              onEdit={openEditForm}
              onDelete={handleDeleteDeliveryCharge}
            />
          ))}
        </div>
      )}

      {/* Delivery Charge Form Modal */}
      <DeliveryChargeForm
        isOpen={showForm}
        onClose={closeForm}
        onSave={editingDeliveryCharge ? handleEditDeliveryCharge : handleAddDeliveryCharge}
        initialData={editingDeliveryCharge || undefined}
        isEditing={!!editingDeliveryCharge}
      />
    </div>
  );
};
