import React, { useState, useEffect } from 'react';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';
import { X, Package, ShoppingCart, AlertCircle } from 'lucide-react';
import { Product, ProductVariant } from '../../types';
import { getProductVariants } from '../../services/productVariantService';

interface ProductVariantSelectorProps {
  product: Product;
  isOpen: boolean;
  onClose: () => void;
  onAddToCart: (product: Product, variant?: ProductVariant, quantity?: number) => void;
}

export function ProductVariantSelector({ 
  product, 
  isOpen, 
  onClose, 
  onAddToCart 
}: ProductVariantSelectorProps) {
  const [variants, setVariants] = useState<ProductVariant[]>([]);
  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(null);
  const [quantity, setQuantity] = useState(1);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch variants when modal opens
  useEffect(() => {
    if (isOpen && product.hasVariants) {
      fetchVariants();
    }
  }, [isOpen, product.id, product.hasVariants]);

  // Reset state when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setSelectedVariant(null);
      setQuantity(1);
      setError(null);
    }
  }, [isOpen]);

  const fetchVariants = async () => {
    try {
      setLoading(true);
      setError(null);
      const fetchedVariants = await getProductVariants(product.id);
      setVariants(fetchedVariants);
      
      // Auto-select default variant if available
      if (fetchedVariants.length > 0) {
        const defaultVariant = product.defaultVariantID 
          ? fetchedVariants.find(v => v.id === product.defaultVariantID)
          : fetchedVariants[0];
        setSelectedVariant(defaultVariant || fetchedVariants[0]);
      }
    } catch (err) {
      console.error('Error fetching variants:', err);
      setError('Failed to load product variants');
    } finally {
      setLoading(false);
    }
  };

  const handleAddToCart = () => {
    if (product.hasVariants && !selectedVariant) {
      setError('Please select a variant');
      return;
    }

    if (selectedVariant && quantity > selectedVariant.stock) {
      setError(`Only ${selectedVariant.stock} items available in stock`);
      return;
    }

    if (!product.hasVariants && quantity > (product.stock || 0)) {
      setError(`Only ${product.stock} items available in stock`);
      return;
    }

    onAddToCart(product, selectedVariant || undefined, quantity);
    onClose();
  };

  const getAvailableStock = () => {
    if (product.hasVariants && selectedVariant) {
      return selectedVariant.stock;
    }
    return product.stock || 0;
  };

  const getCurrentPrice = () => {
    if (product.hasVariants && selectedVariant) {
      return selectedVariant.price;
    }
    return product.price;
  };

  const getCurrentMRP = () => {
    if (product.hasVariants && selectedVariant) {
      return selectedVariant.mrp;
    }
    return product.price; // Fallback to price if no MRP
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex justify-between items-start mb-4">
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-gray-900">{product.name}</h3>
              {product.tamilName && (
                <p className="text-sm text-gray-600">{product.tamilName}</p>
              )}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="w-5 h-5" />
            </Button>
          </div>

          {/* Product Image */}
          {product.images && product.images.length > 0 && (
            <div className="mb-4">
              <img
                src={product.images[0]}
                alt={product.name}
                className="w-full h-48 object-cover rounded-lg"
              />
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg flex items-center text-red-700">
              <AlertCircle className="w-4 h-4 mr-2" />
              <span className="text-sm">{error}</span>
            </div>
          )}

          {/* Loading State */}
          {loading && (
            <div className="mb-4 text-center py-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
              <p className="text-sm text-gray-600 mt-2">Loading variants...</p>
            </div>
          )}

          {/* Variants Selection */}
          {product.hasVariants && !loading && (
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-700 mb-3">Select Variant:</h4>
              <div className="space-y-2">
                {variants.map((variant) => (
                  <div
                    key={variant.id}
                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                      selectedVariant?.id === variant.id
                        ? 'border-primary-500 bg-primary-50'
                        : 'border-gray-200 hover:border-gray-300'
                    } ${variant.stock <= 0 ? 'opacity-50 cursor-not-allowed' : ''}`}
                    onClick={() => {
                      if (variant.stock > 0) {
                        setSelectedVariant(variant);
                        setError(null);
                      }
                    }}
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex flex-wrap gap-1 mb-1">
                          {Object.entries(variant.variationValues).map(([type, value]) => (
                            <Badge key={type} variant="outline" size="sm">
                              {type}: {value}
                            </Badge>
                          ))}
                        </div>
                        <p className="text-sm text-gray-600">SKU: {variant.sku}</p>
                        <div className="flex items-center gap-2 mt-1">
                          <span className="font-medium text-primary-600">₹{variant.price}</span>
                          {variant.mrp > variant.price && (
                            <span className="text-sm text-gray-500 line-through">₹{variant.mrp}</span>
                          )}
                        </div>
                      </div>
                      <div className="text-right">
                        <span className={`text-xs font-medium px-2 py-1 rounded-full ${
                          variant.stock > 10 
                            ? 'bg-green-100 text-green-700' 
                            : variant.stock > 0 
                              ? 'bg-amber-100 text-amber-700'
                              : 'bg-red-100 text-red-700'
                        }`}>
                          {variant.stock > 0 ? `${variant.stock} in stock` : 'Out of stock'}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Quantity Selection */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">Quantity:</label>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setQuantity(Math.max(1, quantity - 1))}
                disabled={quantity <= 1}
              >
                -
              </Button>
              <span className="w-12 text-center font-medium">{quantity}</span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const maxStock = getAvailableStock();
                  if (quantity < maxStock) {
                    setQuantity(quantity + 1);
                    setError(null);
                  }
                }}
                disabled={quantity >= getAvailableStock()}
              >
                +
              </Button>
              <span className="text-sm text-gray-500">
                Max: {getAvailableStock()}
              </span>
            </div>
          </div>

          {/* Price Summary */}
          <div className="mb-6 p-3 bg-gray-50 rounded-lg">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Unit Price:</span>
              <span className="font-medium">₹{getCurrentPrice()}</span>
            </div>
            {getCurrentMRP() > getCurrentPrice() && (
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">MRP:</span>
                <span className="text-sm text-gray-500 line-through">₹{getCurrentMRP()}</span>
              </div>
            )}
            <div className="flex justify-between items-center mt-2 pt-2 border-t border-gray-200">
              <span className="font-medium text-gray-900">Total:</span>
              <span className="font-bold text-primary-600">₹{(getCurrentPrice() * quantity).toFixed(2)}</span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={onClose}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={handleAddToCart}
              disabled={
                loading || 
                getAvailableStock() <= 0 || 
                (product.hasVariants && !selectedVariant)
              }
              className="flex-1"
            >
              <ShoppingCart className="w-4 h-4 mr-2" />
              Add to Cart
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
