import React, { useState, useEffect } from 'react';
import { Badge } from '../ui/Badge';
import { Button } from '../ui/Button';
import { Select } from '../ui/Select';
import { X, Plus, Tag } from 'lucide-react';
import { Category } from '../../types';
import { getAllCategories } from '../../services/productService';

interface MultiCategorySelectorProps {
  selectedCategoryIDs: string[];
  primaryCategoryID?: string;
  onCategoriesChange: (categoryIDs: string[], primaryCategoryID?: string) => void;
  label?: string;
  required?: boolean;
}

export function MultiCategorySelector({
  selectedCategoryIDs,
  primaryCategoryID,
  onCategoriesChange,
  label = "Categories",
  required = false
}: MultiCategorySelectorProps) {
  const [categories, setCategories] = useState<Category[]>([]);
  const [allCategories, setAllCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState('');

  // Fetch categories on component mount
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);
        const fetchedCategories = await getAllCategories();
        
        // Flatten categories and subcategories for selection
        const flatCategories: Category[] = [];
        
        fetchedCategories.forEach(category => {
          // Add main category
          flatCategories.push({
            ...category,
            name: category.name,
            level: 0
          });
          
          // Add subcategories
          if (category.subcategories) {
            category.subcategories.forEach(subcategory => {
              flatCategories.push({
                ...subcategory,
                name: `${category.name} > ${subcategory.name}`,
                level: 1,
                parentCategoryID: category.id
              });
            });
          }
        });
        
        setCategories(fetchedCategories);
        setAllCategories(flatCategories);
      } catch (err) {
        console.error('Error fetching categories:', err);
      } finally {
        setLoading(false);
      }
    };
    
    fetchCategories();
  }, []);

  // Add a category to the selection
  const addCategory = () => {
    if (selectedCategory && !selectedCategoryIDs.includes(selectedCategory)) {
      const newCategoryIDs = [...selectedCategoryIDs, selectedCategory];
      const newPrimaryCategory = primaryCategoryID || selectedCategory;
      
      onCategoriesChange(newCategoryIDs, newPrimaryCategory);
      setSelectedCategory('');
    }
  };

  // Remove a category from the selection
  const removeCategory = (categoryId: string) => {
    const newCategoryIDs = selectedCategoryIDs.filter(id => id !== categoryId);
    let newPrimaryCategory = primaryCategoryID;
    
    // If removing the primary category, set the first remaining category as primary
    if (primaryCategoryID === categoryId) {
      newPrimaryCategory = newCategoryIDs.length > 0 ? newCategoryIDs[0] : undefined;
    }
    
    onCategoriesChange(newCategoryIDs, newPrimaryCategory);
  };

  // Set primary category
  const setPrimaryCategory = (categoryId: string) => {
    if (selectedCategoryIDs.includes(categoryId)) {
      onCategoriesChange(selectedCategoryIDs, categoryId);
    }
  };

  // Get category name by ID
  const getCategoryName = (categoryId: string): string => {
    const category = allCategories.find(cat => cat.id === categoryId);
    return category ? category.name : 'Unknown Category';
  };

  // Get available categories for selection (excluding already selected ones)
  const availableCategories = allCategories.filter(
    category => !selectedCategoryIDs.includes(category.id)
  );

  if (loading) {
    return (
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
        <div className="text-sm text-gray-500">Loading categories...</div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <label className="block text-sm font-medium text-gray-700">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      
      {/* Category Selection */}
      <div className="flex gap-2">
        <div className="flex-1">
          <Select
            options={[
              { value: "", label: "Select a category to add" },
              ...availableCategories.map(category => ({
                value: category.id,
                label: category.name
              }))
            ]}
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            disabled={availableCategories.length === 0}
          />
        </div>
        <Button
          type="button"
          variant="outline"
          onClick={addCategory}
          disabled={!selectedCategory}
        >
          <Plus className="w-4 h-4" />
        </Button>
      </div>

      {/* Selected Categories */}
      {selectedCategoryIDs.length > 0 && (
        <div className="space-y-3">
          <div className="text-sm font-medium text-gray-700">Selected Categories:</div>
          <div className="space-y-2">
            {selectedCategoryIDs.map((categoryId) => (
              <div key={categoryId} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-2">
                  <Tag className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium">{getCategoryName(categoryId)}</span>
                  {primaryCategoryID === categoryId && (
                    <Badge variant="primary" size="sm">Primary</Badge>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  {primaryCategoryID !== categoryId && selectedCategoryIDs.length > 1 && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setPrimaryCategory(categoryId)}
                    >
                      Set as Primary
                    </Button>
                  )}
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => removeCategory(categoryId)}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Help Text */}
      <div className="text-xs text-gray-500">
        <p>• Select multiple categories that this product belongs to</p>
        <p>• The primary category will be used for main navigation and breadcrumbs</p>
        <p>• Products will appear in all selected categories</p>
      </div>

      {/* Validation Message */}
      {required && selectedCategoryIDs.length === 0 && (
        <div className="text-sm text-red-600">
          Please select at least one category
        </div>
      )}
    </div>
  );
}
