import React, { useState, useEffect, useRef } from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  Gift,
  Home,
  Settings,
  ShoppingBag,
  Users,
  Tag,
  ClipboardList,
  ChevronDown,
  ChevronRight,
  ChevronLeft,
  Store,
  PanelLeft,
  RotateCcw,
  Leaf
} from 'lucide-react';

interface NavItemProps {
  to: string;
  icon: React.ReactNode;
  label: string;
  isActive: boolean;
  collapsed?: boolean;
}

const NavItem = ({ to, icon, label, isActive, collapsed = false }: NavItemProps) => (
  <Link
    to={to}
    className={`
      flex items-center
      ${collapsed ? 'justify-center' : 'space-x-3'}
      px-4 py-3 text-sm rounded-md
      transition-all duration-300 ease-in-out
      ${
        isActive
          ? 'bg-primary-500 text-white font-medium'
          : 'text-gray-700 hover:bg-primary-50 hover:text-primary-500'
      }
    `}
    title={collapsed ? label : undefined}
  >
    <span className="transition-transform duration-300 ease-in-out">{icon}</span>
    {!collapsed && (
      <span className="transition-opacity duration-300 ease-in-out whitespace-nowrap overflow-hidden">{label}</span>
    )}
  </Link>
);

interface NavGroupProps {
  icon: React.ReactNode;
  label: string;
  children: React.ReactNode;
  defaultOpen?: boolean;
  isActive: boolean;
  collapsed?: boolean;
}

const NavGroup = ({ icon, label, children, defaultOpen = false, isActive, collapsed = false }: NavGroupProps) => {
  const [isOpen, setIsOpen] = useState(defaultOpen || isActive);
  const [showSubmenu, setShowSubmenu] = useState(false);
  const submenuRef = useRef<HTMLDivElement>(null);

  // Close submenu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (submenuRef.current && !submenuRef.current.contains(event.target as Node)) {
        setShowSubmenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  if (collapsed) {
    return (
      <div className="mb-1 relative" title={label} ref={submenuRef}>
        <button
          className={`
            flex w-full items-center justify-center
            px-4 py-3 text-sm rounded-md
            transition-all duration-300 ease-in-out
            ${
              isActive
                ? 'bg-primary-500/10 text-primary-500 font-medium'
                : 'text-gray-700 hover:bg-primary-50 hover:text-primary-500'
            }
          `}
          onClick={() => {
            setShowSubmenu(!showSubmenu);
          }}
        >
          <span className="transition-transform duration-300 ease-in-out">{icon}</span>
        </button>

        {/* Floating submenu for collapsed state */}
        {showSubmenu && (
          <div
            className="fixed left-16 top-0 mt-12 z-[100] bg-white rounded-md shadow-lg border border-gray-200 py-2 w-48 animate-fade-in"
            style={{ marginTop: submenuRef.current ? submenuRef.current.offsetTop : 0 }}
          >
            <div className="py-1">
              <Link
                to="/products"
                className="flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200"
                onClick={() => setShowSubmenu(false)}
              >
                <Gift size={16} />
                <span>All Products</span>
              </Link>
              <Link
                to="/products/categories"
                className="flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200"
                onClick={() => setShowSubmenu(false)}
              >
                <Tag size={16} />
                <span>Categories</span>
              </Link>
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="mb-1">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`
          flex w-full items-center justify-between
          px-4 py-3 text-sm rounded-md
          transition-all duration-300 ease-in-out
          ${
            isActive
              ? 'bg-primary-500/10 text-primary-500 font-medium'
              : 'text-gray-700 hover:bg-primary-50 hover:text-primary-500'
          }
        `}
      >
        <div className="flex items-center space-x-3">
          <span className="transition-transform duration-300 ease-in-out">{icon}</span>
          <span className="transition-opacity duration-300 ease-in-out">{label}</span>
        </div>
        <span className="transition-transform duration-300 ease-in-out">
          {isOpen ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
        </span>
      </button>
      {isOpen && (
        <div className="mt-1 ml-4 pl-4 border-l border-gray-200 transition-all duration-300 ease-in-out overflow-hidden">
          {children}
        </div>
      )}
    </div>
  );
};

interface SideNavProps {
  collapsed?: boolean;
  toggleCollapse?: () => void;
}

export const SideNav = ({ collapsed = false, toggleCollapse }: SideNavProps) => {
  const location = useLocation();
  const pathname = location.pathname;

  const isActive = (path: string) => {
    if (path === '/') return pathname === '/';
    return pathname.startsWith(path);
  };

  return (
    <div
      className={`
        h-full overflow-y-auto flex flex-col bg-white border-r border-gray-200 shadow-sm
        transition-all duration-300 ease-in-out
        ${collapsed ? 'w-16' : 'w-64'}
      `}
    >
      <div className={`${collapsed ? 'px-2' : 'px-6'} py-6 flex items-center justify-center border-b border-gray-100 transition-all duration-300 ease-in-out`}>
        {collapsed ? (
          <div className="w-8 h-8 rounded-full bg-primary-100 flex items-center justify-center transition-all duration-300 ease-in-out">
            <span className="text-primary-600 font-bold text-lg">V</span>
          </div>
        ) : (
          <div className="flex flex-col items-center transition-opacity duration-300 ease-in-out">
            <img
              src="https://firebasestorage.googleapis.com/v0/b/vrisham-cad24.appspot.com/o/users%2FYkIVaNNLfvOUuJhhVZKZ35YZqi72%2Fuploads%2Fvrisham%20logo.png?alt=media&token=92d39ef7-3ebb-44a0-9ac7-a998e5249b10"
              alt="Vrisham Organic"
              className="h-10 w-auto"
            />
            <span className="text-primary-600 font-semibold text-sm mt-1">Admin Panel</span>
          </div>
        )}
      </div>

      {/* Collapse/Expand button on the side */}
      <button
        onClick={toggleCollapse}
        className={`
          absolute top-1/2 -right-3 transform -translate-y-1/2
          bg-white border border-gray-200 rounded-full p-1.5
          shadow-md text-gray-500 hover:text-primary-500 hover:border-primary-200
          transition-all duration-300 ease-in-out
          focus:outline-none focus:ring-2 focus:ring-primary-200 focus:border-primary-300
          z-10
        `}
        title={collapsed ? "Expand Sidebar" : "Collapse Sidebar"}
        aria-label={collapsed ? "Expand Sidebar" : "Collapse Sidebar"}
      >
        {collapsed ? (
          <ChevronRight size={18} className="transition-transform duration-300 ease-in-out" />
        ) : (
          <ChevronLeft size={18} className="transition-transform duration-300 ease-in-out" />
        )}
      </button>

      <div className={`pt-4 ${collapsed ? 'px-2' : 'px-3'} space-y-1 flex-1 transition-all duration-300 ease-in-out`}>
        {/* Navigation items */}
        <NavItem
          to="/dashboard"
          icon={<Home size={18} />}
          label="Dashboard"
          isActive={isActive('/dashboard')}
          collapsed={collapsed}
        />

        <NavGroup
          icon={<ShoppingBag size={18} />}
          label="Products"
          isActive={isActive('/products')}
          collapsed={collapsed}
          defaultOpen={isActive('/products')}
        >
          <NavItem
            to="/products"
            icon={<Gift size={16} />}
            label="All Products"
            isActive={pathname === '/products'}
            collapsed={collapsed}
          />
          <NavItem
            to="/products/categories"
            icon={<Tag size={16} />}
            label="Categories"
            isActive={pathname === '/products/categories'}
            collapsed={collapsed}
          />
          <NavItem
            to="/products/variation-types"
            icon={<Tag size={16} />}
            label="Variation Types"
            isActive={pathname === '/products/variation-types'}
            collapsed={collapsed}
          />
        </NavGroup>

        <NavItem
          to="/pos"
          icon={<Store size={18} />}
          label="Point of Sale"
          isActive={isActive('/pos')}
          collapsed={collapsed}
        />

        <NavItem
          to="/orders"
          icon={<ClipboardList size={18} />}
          label="Orders"
          isActive={isActive('/orders')}
          collapsed={collapsed}
        />

        <NavItem
          to="/customers"
          icon={<Users size={18} />}
          label="Customers"
          isActive={isActive('/customers')}
          collapsed={collapsed}
        />

        <NavItem
          to="/returns"
          icon={<RotateCcw size={18} />}
          label="Returns"
          isActive={isActive('/returns')}
          collapsed={collapsed}
        />

        <NavItem
          to="/farmers"
          icon={<Leaf size={18} />}
          label="Farmers"
          isActive={isActive('/farmers')}
          collapsed={collapsed}
        />

        <NavItem
          to="/settings"
          icon={<Settings size={18} />}
          label="Settings"
          isActive={isActive('/settings')}
          collapsed={collapsed}
        />
      </div>
    </div>
  );
}






