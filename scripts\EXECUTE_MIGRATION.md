# Execute Product ID Migration for vrisham-cad24

## Quick Setup & Execution Guide

### Step 1: Get Firebase Service Account Key

1. Go to [Firebase Console](https://console.firebase.google.com/project/vrisham-cad24/settings/serviceaccounts/adminsdk)
2. Click **"Generate new private key"**
3. Download the JSON file
4. Rename it to `serviceAccountKey.json`
5. Place it in your `scripts/` folder

### Step 2: Install Dependencies

```bash
# Navigate to your project root
cd /path/to/your/vrisham/project

# Install Firebase Admin SDK
npm install firebase-admin
```

### Step 3: Execute Migration

```bash
# 1. Check current status (see what needs migration)
node scripts/check-product-id-status.js

# 2. Run dry run (preview changes - SAFE, no actual changes)
node scripts/migrate-add-product-id.js --dry-run

# 3. Execute migration (actually adds Product IDs)
node scripts/migrate-add-product-id.js --execute

# 4. Verify results
node scripts/check-product-id-status.js
```

## Expected Output

### Before Migration (check-product-id-status.js):
```
🔍 Checking Product ID implementation status...

📊 PRODUCT ANALYSIS
Total products found: 25
✅ Products with Product ID: 0
❌ Products without Product ID: 25
🔢 Highest Product ID found: 0

📊 VARIANT ANALYSIS
Total variants found: 8
✅ Variants with productNumericId: 0
❌ Variants without productNumericId: 8

⚠️  INCOMPLETE: Migration needed
```

### Dry Run Output (--dry-run):
```
DRY RUN: Preview of Product ID migration...
Found 25 products to check...
  Would assign Product ID 1 to: Organic Tomatoes (abc123)
  Would assign Product ID 2 to: Fresh Spinach (def456)
  ...
Products to migrate: 25
Variants to migrate: 8
Next available Product ID: 26
```

### Migration Execution (--execute):
```
Starting migration: Adding Product ID to existing products...
Found 25 products to migrate...

Processing product: abc123 (Organic Tomatoes)
  Assigning Product ID 1 to product abc123
  ✓ Successfully assigned Product ID 1 to product
  Found 2 variants for this product
    Assigning productNumericId 1 to variant var1
    ✓ Successfully assigned productNumericId to variant var1
    ...

✅ Migration completed successfully!
Total products processed: 25
Products migrated (new Product ID): 25
Total variants processed: 8
Variants migrated (new productNumericId): 8
```

### After Migration (check-product-id-status.js):
```
📊 PRODUCT ANALYSIS
Total products found: 25
✅ Products with Product ID: 25
❌ Products without Product ID: 0
🔢 Highest Product ID found: 25

✅ COMPLETE: All products and variants have Product IDs
🚀 Your system is ready to use Product ID features!
```

## Verification Steps

### 1. Check Firebase Console
- Go to [Firestore Database](https://console.firebase.google.com/project/vrisham-cad24/firestore)
- Open any product document
- You should see a `productId` field with a numeric value

### 2. Check Variants
- Navigate to Products → [any product] → Variants → [any variant]
- You should see a `productNumericId` field

### 3. Test Frontend
- Create a new product (should auto-suggest next Product ID)
- Edit existing product (should show existing Product ID)
- Search by Product ID in products list

## Troubleshooting

### "Cannot find module './serviceAccountKey.json'"
- Ensure the service account key is downloaded and placed in `scripts/serviceAccountKey.json`
- Check the filename is exactly `serviceAccountKey.json` (case-sensitive)

### "Permission denied" or "Insufficient permissions"
- Ensure the service account has "Firebase Admin SDK Admin Service Agent" role
- Re-download the service account key if needed

### "No products found"
- Verify you're connected to the correct project (vrisham-cad24)
- Check that products exist in the Firestore "Products" collection

### Migration appears to hang
- Large databases may take time - be patient
- Check console for progress messages
- Interrupt with Ctrl+C if needed and re-run

## Rollback (Emergency Only)

If something goes wrong, you can remove Product ID fields:

```javascript
// Create rollback.js in scripts/ folder
const admin = require('firebase-admin');
const serviceAccount = require('./serviceAccountKey.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  projectId: 'vrisham-cad24'
});

const db = admin.firestore();

async function rollback() {
  console.log('Rolling back Product ID migration...');
  
  const products = await db.collection('Products').get();
  
  for (const doc of products.docs) {
    await doc.ref.update({
      productId: admin.firestore.FieldValue.delete()
    });
    
    // Remove from variants too
    const variants = await doc.ref.collection('Variants').get();
    for (const variant of variants.docs) {
      await variant.ref.update({
        productNumericId: admin.firestore.FieldValue.delete()
      });
    }
  }
  
  console.log('Rollback completed');
}

rollback().then(() => process.exit(0));
```

## Success Indicators

✅ **Migration Successful When:**
- All products have `productId` field with unique positive integers
- All variants have `productNumericId` matching their parent
- Frontend forms show Product ID fields
- Search by Product ID works
- CSV import/export includes Product ID

🎉 **You're Done!** Your Product ID system is now fully operational.
