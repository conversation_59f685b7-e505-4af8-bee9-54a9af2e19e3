# Bulk Product Management System

## Overview

The Bulk Product Management system provides comprehensive CSV import/export functionality for products, including support for product variants, pre-order settings, and all product fields.

## Features

### CSV Import
- **Bulk Upload**: Import multiple products from CSV files
- **SKU-based Matching**: Use SKU as unique identifier for products
- **Overwrite Protection**: Option to overwrite existing products or skip them
- **Validation**: Comprehensive validation with detailed error reporting
- **Variant Support**: Import products with variants in the same CSV
- **Progress Tracking**: Real-time import progress and results

### CSV Export
- **Complete Export**: Export all products with full field data
- **Variant Inclusion**: Option to include/exclude product variants
- **Filtering**: Export by category or status
- **Image URLs**: Include product image URLs in export

### CSV Template
- **Sample Data**: Pre-filled template with example products
- **Field Documentation**: All supported fields with examples
- **Variant Examples**: Shows how to structure variant data

## CSV Format

### Required Fields
- `name`: Product name
- `barcode`: Unique product identifier (barcode)
- `price`: Selling price (₹)
- `stock`: Available quantity
- `unit`: Unit of measurement (kg, piece, etc.)

### Optional Fields
- `description`: Product description
- `mrp`: Maximum Retail Price (₹)
- `status`: available | out_of_stock | coming_soon
- `isVisible`: true | false
- `category`: Legacy category ID
- `categoryIDs`: Comma-separated category IDs
- `categoryPath`: Full category path (e.g., "Vegetables>Leafy Greens")
- `primaryCategoryID`: Main category ID
- `farmerId`: Associated farmer ID
- `nutrition`: Nutrition information
- `storageInstruction`: Storage instructions
- `images`: Comma-separated image URLs

### Pre-order Fields
- `isPreOrder`: true | false
- `preOrderStartAt`: ISO date string (e.g., 2024-01-01T00:00:00.000Z)
- `preOrderEndAt`: ISO date string
- `harvestOffsetDays`: -1 (yesterday), 0 (today), 1 (tomorrow)

### Variant Fields
- `hasVariants`: true | false (for main product)
- `defaultVariantID`: Default variant ID
- `isVariant`: true (for variant rows)
- `parentBarcode`: Barcode of parent product (for variants)
- `variationValues`: JSON string (e.g., {"Size":"Small","Color":"Red"})
- `variantImage`: Variant-specific image URL

## Usage

### Importing Products

1. **Download Template**: Click "Download CSV Template" to get a sample file
2. **Fill Data**: Add your product data following the template format
3. **Upload File**: Click "Bulk Upload" and select your CSV file
4. **Configure Options**:
   - **Overwrite existing**: Update products with matching SKUs
   - **Validate only**: Check for errors without importing
   - **Skip invalid rows**: Continue import despite some errors
5. **Review Results**: Check import summary and error details

### Exporting Products

1. **Click Export**: Click "Export All Products" button
2. **Configure Options**:
   - **Include variants**: Export variant data as separate rows
   - **Include images**: Include image URLs in export
3. **Download**: CSV file will be automatically downloaded

### Working with Variants

#### Main Product Row
```csv
name,barcode,hasVariants,price,mrp,stock,categoryPath,...
"T-Shirt",TSHIRT001,true,500,600,0,"Clothing>Apparel",...
```

#### Variant Rows
```csv
name,barcode,isVariant,parentBarcode,variationValues,price,mrp,stock,categoryPath,...
"T-Shirt",TSHIRT001-S,true,TSHIRT001,"{""Size"":""Small""}",450,550,10,"Clothing>Apparel",...
"T-Shirt",TSHIRT001-M,true,TSHIRT001,"{""Size"":""Medium""}",500,600,15,"Clothing>Apparel",...
```

## Validation Rules

### Product Validation
- Name is required and non-empty
- Barcode is required and unique
- Price must be a positive number
- Stock must be non-negative
- Status must be valid enum value

### Category Validation
- Category IDs must exist in database
- Primary category must be in categoryIDs list

### Farmer Validation
- Farmer ID must exist in database if provided

### Pre-order Validation
- Start date must be before end date
- Dates must be valid ISO format
- Harvest offset must be -1, 0, or 1

### Variant Validation
- Parent Barcode must exist for variant rows
- Variation values must be valid JSON
- Variant Barcodes must be unique

## Error Handling

### Import Errors
- **Row-level errors**: Specific validation failures with row numbers
- **Reference errors**: Invalid category/farmer IDs
- **Duplicate errors**: Barcode conflicts
- **Format errors**: Invalid data types or formats

### Export Errors
- **Database errors**: Connection or query failures
- **Permission errors**: Insufficient access rights

## Technical Implementation

### Key Components
- `BulkProductManager.tsx`: Main UI component
- `bulkProductService.ts`: Core import/export logic
- `csvUtils.ts`: CSV parsing and generation utilities
- `bulkProduct.ts`: Type definitions

### Dependencies
- `papaparse`: CSV parsing library
- `firebase/firestore`: Database operations

### Database Collections
- `Products`: Main product collection
- `Products/{id}/Variants`: Product variants sub-collection
- `Categories`: Product categories
- `Farmers`: Farmer information
- `VariationTypes`: Variant type definitions

## Best Practices

### CSV Preparation
1. Use the provided template as starting point
2. Ensure Barcodes are unique across all products
3. Validate category and farmer IDs before import
4. Use categoryPath format like "Vegetables>Leafy Greens" for better organization
5. Use proper date formats for pre-order fields
6. Test with small batches first

### Import Strategy
1. Start with validation-only mode
2. Fix all errors before actual import
3. Use overwrite option carefully
4. Keep backup of existing data
5. Monitor import results closely

### Performance Considerations
- Large imports (>100 products) may take several minutes
- Variant processing adds additional time
- Network connectivity affects upload speed
- Database write limits may throttle large imports

## Troubleshooting

### Common Issues
1. **CSV Format Errors**: Ensure proper CSV encoding (UTF-8)
2. **Date Format Issues**: Use ISO 8601 format for dates
3. **JSON Parsing Errors**: Escape quotes in variation values
4. **Reference Errors**: Verify category/farmer IDs exist
5. **Permission Errors**: Ensure proper Firebase access

### Support
For technical issues or questions, refer to the application logs or contact the development team.
