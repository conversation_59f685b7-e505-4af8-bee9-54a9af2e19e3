import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import {
  Search,
  Filter,
  ChevronDown,
  Plus,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Package,
  RefreshCw,
  AlertCircle,
  User,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  X
} from 'lucide-react';
import { Card } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { Badge } from '../../components/ui/Badge';
import { AddCustomerForm } from '../../components/customers/AddCustomerForm';
import { Customer } from '../../types';
import { useCustomers } from '../../contexts/CustomerContext';

export function CustomersList() {
  const {
    customers,
    loading,
    error,
    refreshCustomers,
    addCustomer,
    isInitialized,
    totalCustomers,
    inactiveCustomers,
    totalOrders,
    pageSize,
    currentPage,
    hasNextPage,
    loadNextPage,
    loadPreviousPage,
    goToPage,
    setPageSize,
    searchCustomers
  } = useCustomers();
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [isSearchMode, setIsSearchMode] = useState<boolean>(false);
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const [searchDebounceTimer, setSearchDebounceTimer] = useState<NodeJS.Timeout | null>(null);
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [showAddCustomerForm, setShowAddCustomerForm] = useState(false);
  const [localError, setLocalError] = useState<string | null>(null);

  // Load customers when the component mounts
  useEffect(() => {
    if (!isInitialized) {
      refreshCustomers();
    }
  }, [isInitialized, refreshCustomers]);

  // Add debug logging with safety checks
  useEffect(() => {
    try {
      if (Array.isArray(customers)) {
        console.log('CustomersList - Current customers count:', customers.length);
      } else {
        console.log('CustomersList - Customers is not an array:', typeof customers);
      }
    } catch (error) {
      console.error('Error in debug logging:', error);
    }
  }, [customers]);

  // Handle refresh button click
  const handleRefresh = () => {
    console.log('Refreshing customers...');
    refreshCustomers();
  };

  // Apply status filter only (search is now handled by the server)
  const filteredCustomers = React.useMemo(() => {
    // Safety check for customers array
    if (!Array.isArray(customers)) {
      console.error('Customers is not an array:', customers);
      return [];
    }

    return customers.filter(customer => {
      // Basic validation
      if (!customer) return false;

      // Status filter
      if (statusFilter !== 'all' && customer.status !== statusFilter) {
        return false;
      }

      return true;
    });
  }, [customers, statusFilter]);

  // Add debug logging with more safety checks
  try {
    // Only log if filteredCustomers is an array
    if (Array.isArray(filteredCustomers)) {
      console.log(`Filtered ${filteredCustomers.length} customers from ${Array.isArray(customers) ? customers.length : 0} total`);

      // Only log first customer if it exists and there's a mismatch between input and output
      if (Array.isArray(customers) && customers.length > 0 && filteredCustomers.length === 0) {
        // Don't log the entire customer object, just basic info
        const firstCustomer = customers[0];
        if (firstCustomer) {
          console.log('First customer info:', {
            id: firstCustomer.id,
            name: firstCustomer.name,
            status: firstCustomer.status
          });
        }
      }
    } else {
      console.log('filteredCustomers is not an array');
    }
  } catch (logError) {
    console.error('Error in debug logging:', logError);
  }

  // Calculate total pages based on total customers count
  const totalPages = Math.ceil(totalCustomers / pageSize);

  // Track previous search query to prevent infinite loops
  const prevSearchQueryRef = React.useRef<string>('');
  // Create a ref for the search input to maintain focus
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Handle search input change with a simpler approach
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setSearchQuery(newValue);

    // Clear any existing timer
    if (searchDebounceTimer) {
      clearTimeout(searchDebounceTimer);
    }

    // If clearing the search
    if (!newValue.trim() && isSearchMode) {
      console.log('Search cleared, refreshing customer list');
      setIsSearchMode(false);
      refreshCustomers();
      return;
    }

    // Skip if empty
    if (!newValue.trim()) {
      return;
    }

    // Set up new debounce timer
    const timer = setTimeout(() => {
      if (newValue.trim()) {
        console.log(`Searching for: "${newValue}"`);
        setIsSearchMode(true);
        setIsSearching(true);

        searchCustomers(newValue)
          .finally(() => {
            setIsSearching(false);
          });
      }
    }, 500);

    setSearchDebounceTimer(timer);
  };

  // Handle clear search button click
  const handleClearSearch = () => {
    console.log('Clear search button clicked');

    // Clear any existing timer
    if (searchDebounceTimer) {
      clearTimeout(searchDebounceTimer);
      setSearchDebounceTimer(null);
    }

    // Clear search and refresh
    setSearchQuery('');
    setIsSearchMode(false);
    refreshCustomers();

    // Focus the search input after clearing
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  };

  // Clean up timer on unmount
  useEffect(() => {
    return () => {
      if (searchDebounceTimer) {
        clearTimeout(searchDebounceTimer);
      }
    };
  }, [searchDebounceTimer]);

  // Handle page size change
  const handlePageSizeChange = (newSize: number) => {
    setPageSize(newSize);
  };

  const formatDate = (dateInput?: string | null | any) => {
    if (!dateInput) return 'N/A';

    try {
      let date;

      // Check if the date is a Firestore Timestamp object
      if (dateInput && typeof dateInput === 'object' && 'seconds' in dateInput && 'nanoseconds' in dateInput) {
        // Convert Firestore Timestamp to JavaScript Date
        date = new Date(dateInput.seconds * 1000);
      } else if (typeof dateInput === 'string') {
        // If it's a string, create a Date object
        date = new Date(dateInput);
      } else {
        // If it's already a Date object or something else
        date = new Date(dateInput);
      }

      return date.toLocaleDateString('en-IN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch (error) {
      console.error('Error formatting date:', error, dateInput);
      return 'Invalid Date';
    }
  };

  // Helper function to format phone numbers
  const formatPhoneNumber = (phone: string) => {
    if (!phone) return 'N/A';

    // If phone number is already formatted, return it
    if (phone.includes(' ') || phone.includes('-')) return phone;

    // If it's an international number starting with +
    if (phone.startsWith('+')) {
      // For Indian numbers (+91XXXXXXXXXX)
      if (phone.startsWith('+91') && phone.length === 13) {
        return `+91 ${phone.substring(3, 8)} ${phone.substring(8)}`;
      }
      // Generic international format
      return phone.replace(/(\+\d{2})(\d{5})(\d+)/, '$1 $2 $3');
    }

    // For 10-digit numbers (most common case)
    if (phone.length === 10) {
      return phone.replace(/(\d{5})(\d{5})/, '$1 $2');
    }

    return phone;
  };

  // Pagination component
  const Pagination = () => {
    // Hide pagination in search mode or when there's only one page
    if (isSearchMode || totalPages <= 1) return null;

    return (
      <div className="flex flex-col md:flex-row justify-between items-center mt-8 gap-4">
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600">Show</span>
          <select
            className="border border-gray-300 rounded-md p-1 text-sm"
            value={pageSize}
            onChange={(e) => handlePageSizeChange(Number(e.target.value))}
          >
            <option value={10}>10</option>
            <option value={20}>20</option>
            <option value={30}>30</option>
            <option value={50}>50</option>
          </select>
          <span className="text-sm text-gray-600">customers per page</span>
        </div>

        <div className="flex justify-center items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => goToPage(1)}
            disabled={currentPage === 1 || loading}
          >
            <ChevronsLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => loadPreviousPage()}
            disabled={currentPage === 1 || loading}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>

          <div className="flex items-center gap-1 mx-2">
            <span className="text-sm font-medium">
              Page {currentPage} of {totalPages || '?'}
            </span>
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={() => loadNextPage()}
            disabled={!hasNextPage || loading}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              // We can only go to the last page if we know the total
              if (totalPages > 0) {
                goToPage(totalPages);
              }
            }}
            disabled={!hasNextPage || loading || totalPages <= 0}
          >
            <ChevronsRight className="h-4 w-4" />
          </Button>
        </div>

        <div className="text-sm text-gray-600">
          {isSearchMode
            ? `Found ${Array.isArray(customers) ? customers.length : 0} customers matching "${searchQuery}"`
            : `Showing ${Array.isArray(customers) ? customers.length : 0} of ${totalCustomers || 0} customers`
          }
        </div>
      </div>
    );
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Customers</h1>
          <p className="text-gray-500">Manage your customer relationships</p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={loading}
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button
            variant="primary"
            onClick={() => setShowAddCustomerForm(true)}
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Customer
          </Button>
        </div>
      </div>

      {/* Error message */}
      {(error || localError) && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center text-red-700">
          <AlertCircle className="w-5 h-5 mr-2" />
          <span>{error || localError}</span>
        </div>
      )}

      {/* Search mode indicator */}
      {isSearchMode && !error && !localError && (
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg flex items-center text-blue-700">
          <Search className="w-5 h-5 mr-2" />
          <span>Showing search results for "{searchQuery}" across all customers</span>
        </div>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <div className="p-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">
                {totalCustomers}
              </h3>
              <Badge variant="primary">Total</Badge>
            </div>
            <p className="text-gray-600 text-sm">Total Customers</p>
          </div>
        </Card>

        <Card>
          <div className="p-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">
                {totalOrders.toLocaleString()}
              </h3>
              <Badge variant="secondary">Orders</Badge>
            </div>
            <p className="text-gray-600 text-sm">Total Orders</p>
          </div>
        </Card>
      </div>

      {/* Filters & Search */}
      <div className="flex flex-col sm:flex-row gap-3">
        <div className="relative flex-grow">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Search className="w-4 h-4 text-gray-400" />
          </div>
          <input
            ref={searchInputRef}
            type="text"
            className="block w-full p-2.5 pl-10 text-sm text-gray-900 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
            placeholder="Search by name, email or phone..."
            value={searchQuery}
            onChange={handleSearchChange}
          />
          {searchQuery && (
            <button
              className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
              onClick={handleClearSearch}
              type="button"
              aria-label="Clear search"
            >
              <X className="w-5 h-5" />
            </button>
          )}
        </div>

        <div className="relative">
          <Button
            variant="outline"
            size="md"
            className="w-full sm:w-auto"
            icon={<Filter size={16} />}
            onClick={() => setIsFilterOpen(!isFilterOpen)}
          >
            Status Filter
            <ChevronDown size={16} className="ml-1" />
          </Button>

          {isFilterOpen && (
            <div className="absolute z-10 mt-2 w-48 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
              <div className="py-1">
                <button
                  className={`block px-4 py-2 text-sm w-full text-left ${
                    statusFilter === 'all' ? 'bg-primary-50 text-primary-700' : 'text-gray-700 hover:bg-gray-100'
                  }`}
                  onClick={() => {
                    setStatusFilter('all');
                    setIsFilterOpen(false);
                    setCurrentPage(1); // Reset to first page on filter change
                  }}
                >
                  All Customers
                </button>
                <button
                  className={`block px-4 py-2 text-sm w-full text-left ${
                    statusFilter === 'active' ? 'bg-primary-50 text-primary-700' : 'text-gray-700 hover:bg-gray-100'
                  }`}
                  onClick={() => {
                    setStatusFilter('active');
                    setIsFilterOpen(false);
                    setCurrentPage(1); // Reset to first page on filter change
                  }}
                >
                  Active Customers
                </button>
                <button
                  className={`block px-4 py-2 text-sm w-full text-left ${
                    statusFilter === 'inactive' ? 'bg-primary-50 text-primary-700' : 'text-gray-700 hover:bg-gray-100'
                  }`}
                  onClick={() => {
                    setStatusFilter('inactive');
                    setIsFilterOpen(false);
                    setCurrentPage(1); // Reset to first page on filter change
                  }}
                >
                  Inactive Customers
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Customers List */}
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="flex flex-col items-center">
            <RefreshCw className="w-12 h-12 text-primary-500 animate-spin mb-4" />
            <p className="text-gray-600">Loading customers...</p>
          </div>
        </div>
      ) : filteredCustomers.length === 0 ? (
        <div className="flex justify-center items-center h-64">
          <div className="flex flex-col items-center">
            <User className="w-12 h-12 text-gray-400 mb-4" />
            <p className="text-gray-600">No customers found</p>
            <p className="text-gray-500 text-sm mt-2">Try adjusting your search or filters</p>
            <Button
              variant="primary"
              className="mt-4"
              onClick={() => setShowAddCustomerForm(true)}
            >
              <Plus className="w-4 h-4 mr-2" />
              Add New Customer
            </Button>
          </div>
        </div>
      ) : (
        <>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {filteredCustomers.map(customer => {
              // Skip rendering if customer is invalid
              if (!customer || !customer.id) {
                return null;
              }

              // Wrap each customer card in a try-catch to prevent one bad card from breaking the entire list
              try {
                return (
                  <Link key={customer.id} to={`/customers/${customer.id}`}>
                    <Card className="hover:shadow-lg transition-shadow">
                      <div className="p-4">
                        <div className="flex justify-between items-start mb-4">
                          <div>
                            <h3 className="text-lg font-semibold text-gray-900">
                              {customer.name && customer.name !== 'Unknown Customer'
                                ? customer.name
                                : (customer.phone
                                    ? formatPhoneNumber(customer.phone)
                                    : 'Unnamed Customer')}
                            </h3>
                            <Badge
                              variant={customer.status === 'active' ? 'success' : 'gray'}
                              className="mt-1"
                            >
                              {customer.status || 'unknown'}
                            </Badge>
                          </div>
                          <Badge variant="secondary">
                            {customer.totalOrders || 0} orders
                          </Badge>
                        </div>

                        <div className="space-y-2 mb-4">
                          {customer.email && (
                            <div className="flex items-center text-gray-600">
                              <Mail className="w-4 h-4 mr-2" />
                              <span className="text-sm">{customer.email}</span>
                            </div>
                          )}
                          {customer.phone && (
                            <div className="flex items-center text-gray-600">
                              <Phone className="w-4 h-4 mr-2" />
                              <span className="text-sm">{customer.phone}</span>
                            </div>
                          )}
                          {customer.addresses && Array.isArray(customer.addresses) && customer.addresses.length > 0 && (
                            <div className="flex items-start text-gray-600">
                              <MapPin className="w-4 h-4 mr-2 mt-0.5 flex-shrink-0" />
                              <span className="text-sm truncate max-w-[200px]" title={(() => {
                                try {
                                  const defaultAddress = customer.addresses.find(a => a && a.isDefault) || customer.addresses[0];
                                  if (!defaultAddress) return 'No address';

                                  // Format the full address for the tooltip
                                  const addressParts = [];
                                  if (defaultAddress.line1) addressParts.push(defaultAddress.line1);
                                  if (defaultAddress.line2) addressParts.push(defaultAddress.line2);
                                  if (defaultAddress.city) addressParts.push(defaultAddress.city);
                                  if (defaultAddress.state) addressParts.push(defaultAddress.state);
                                  if (defaultAddress.pincode) addressParts.push(defaultAddress.pincode);

                                  return addressParts.join(', ');
                                } catch (error) {
                                  return 'Address error';
                                }
                              })()}>
                                {(() => {
                                  try {
                                    const defaultAddress = customer.addresses.find(a => a && a.isDefault) || customer.addresses[0];
                                    if (!defaultAddress) return 'No address';

                                    // Format the address to show line1, city, state
                                    const addressParts = [];
                                    if (defaultAddress.line1) addressParts.push(defaultAddress.line1);
                                    if (defaultAddress.city) addressParts.push(defaultAddress.city);
                                    if (defaultAddress.state) addressParts.push(defaultAddress.state);

                                    return addressParts.length > 0
                                      ? addressParts.join(', ')
                                      : 'Address available';
                                  } catch (addressError) {
                                    console.error('Error getting address:', addressError);
                                    return 'Address error';
                                  }
                                })()}
                              </span>
                            </div>
                          )}
                        </div>

                        <div className="mt-4 pt-4 border-t border-gray-100">
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <div className="flex items-center text-gray-600">
                                <Package className="w-4 h-4 mr-2" />
                                <span className="text-sm">Last Order</span>
                              </div>
                              <p className="text-sm font-medium text-gray-900 mt-1">
                                {customer.lastOrderDate ? formatDate(customer.lastOrderDate) : 'No orders yet'}
                              </p>
                            </div>
                            <div>
                              <div className="flex items-center text-gray-600">
                                <Calendar className="w-4 h-4 mr-2" />
                                <span className="text-sm">Customer Since</span>
                              </div>
                              <p className="text-sm font-medium text-gray-900 mt-1">
                                {formatDate(customer.createdAt)}
                              </p>
                            </div>
                          </div>
                          <div className="mt-4">
                            <p className="text-sm text-gray-600">Total Spent</p>
                            <p className="text-lg font-semibold text-gray-900">₹{(customer.totalSpent || 0).toLocaleString()}</p>
                          </div>
                        </div>
                      </div>
                    </Card>
                  </Link>
                );
              } catch (cardError) {
                console.error('Error rendering customer card:', cardError, customer);
                return (
                  <Card key={customer.id || 'error-card'} className="border border-red-200 bg-red-50">
                    <div className="p-4">
                      <div className="flex items-center text-red-600">
                        <AlertCircle className="w-5 h-5 mr-2" />
                        <h3 className="text-lg font-semibold">Error displaying customer</h3>
                      </div>
                      <p className="text-sm text-red-600 mt-2">There was an error displaying this customer's information.</p>
                    </div>
                  </Card>
                );
              }
            })}
          </div>

          {/* Add pagination component */}
          <Pagination />
        </>
      )}

      {/* Add Customer Form Modal */}
      <AddCustomerForm
        isOpen={showAddCustomerForm}
        onClose={() => setShowAddCustomerForm(false)}
        onSave={async (newCustomer) => {
          try {
            setLocalError(null);
            const customerId = await addCustomer(newCustomer);
            if (customerId) {
              await refreshCustomers();
              setShowAddCustomerForm(false);
            } else {
              setLocalError('Failed to add customer. Please try again.');
            }
          } catch (err) {
            console.error('Error adding customer:', err);
            setLocalError('An unexpected error occurred. Please try again.');
          }
        }}
      />
    </div>
  );
}

