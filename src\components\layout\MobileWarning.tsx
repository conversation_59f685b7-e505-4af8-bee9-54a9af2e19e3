import { useState, useEffect } from 'react';
import { Monitor, AlertTriangle } from 'lucide-react';

export const MobileWarning = () => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    // Check if the screen width is less than 768px (typical mobile breakpoint)
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Check on initial load
    checkMobile();

    // Add event listener for window resize
    window.addEventListener('resize', checkMobile);

    // Clean up event listener
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // If not mobile, don't show anything
  if (!isMobile) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-80 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
        <div className="text-center mb-4">
          <h2 className="text-xl font-bold text-red-600">Desktop View Required</h2>
        </div>

        <div className="flex flex-col items-center mb-6">
          <div className="bg-red-100 p-4 rounded-full mb-4">
            <Monitor className="w-12 h-12 text-red-600" />
          </div>

          <div className="flex items-center justify-center mb-4">
            <AlertTriangle className="w-5 h-5 text-amber-500 mr-2" />
            <span className="font-semibold text-amber-600">Mobile Access Restricted</span>
          </div>

          <p className="text-gray-700 text-center mb-3">
            This application is designed exclusively for desktop use.
          </p>

          <p className="text-gray-700 text-center mb-3 font-semibold">
            Please open this application on a desktop or laptop computer.
          </p>

          <p className="text-gray-500 text-sm text-center">
            The application will not function correctly on mobile devices.
          </p>
        </div>
      </div>
    </div>
  );
};
