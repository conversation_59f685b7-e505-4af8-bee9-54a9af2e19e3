import React, { useState, useEffect } from 'react';
import { Card } from '../../components/ui/Card';
import { Badge } from '../../components/ui/Badge';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { Textarea } from '../../components/ui/Textarea';
import { 
  Plus, 
  Edit2, 
  Trash2, 
  Type, 
  FileText, 
  Image, 
  FolderPlus, 
  ChevronRight 
} from 'lucide-react';
import { collection, doc, getDocs, addDoc, updateDoc, deleteDoc, query, orderBy } from 'firebase/firestore';
import { db } from '../../firebase/config';

export function Categories() {
  const [categories, setCategories] = useState([]);
  const [showNewCategoryForm, setShowNewCategoryForm] = useState(false);
  const [showNewSubcategoryForm, setShowNewSubcategoryForm] = useState(null);
  const [editingCategory, setEditingCategory] = useState(null);
  const [editingSubcategory, setEditingSubcategory] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const [newCategory, setNewCategory] = useState({
    name: '',
    branchCode: '',
    image: ''
  });

  const [newSubcategory, setNewSubcategory] = useState({
    name: '',
    branchCode: '',
    image: ''
  });

  const [editCategoryData, setEditCategoryData] = useState({
    name: '',
    branchCode: '',
    image: ''
  });

  const [editSubcategoryData, setEditSubcategoryData] = useState({
    name: '',
    branchCode: '',
    image: ''
  });

  // Fetch categories from Firestore
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const categoriesRef = collection(db, 'Categories');
        const q = query(categoriesRef, orderBy('index'));
        const querySnapshot = await getDocs(q);
        
        const fetchedCategories = [];
        const subcategoriesMap = {};
        
        // First pass: collect all categories
        querySnapshot.docs.forEach(doc => {
          const data = doc.data();
          if (!data.parentCategoryID) { // This is a parent category
            fetchedCategories.push({
              id: doc.id,
              name: data.name || '',
              branchCode: data.branchCode || '',
              image: data.image || '',
              index: data.index || 0,
              isParentCategory: data.isParentCategory || true,
              keyword: data.keyword || [],
              subcategories: []
            });
          } else {
            // Store subcategories temporarily
            if (!subcategoriesMap[data.parentCategoryID]) {
              subcategoriesMap[data.parentCategoryID] = [];
            }
            subcategoriesMap[data.parentCategoryID].push({
              id: doc.id,
              name: data.name || '',
              branchCode: data.branchCode || '',
              image: data.image || '',
              index: data.index || 0,
              keyword: data.keyword || [],
              parentCategoryID: data.parentCategoryID
            });
          }
        });
        
        // Second pass: add subcategories to their parent categories
        fetchedCategories.forEach(category => {
          if (subcategoriesMap[category.id]) {
            category.subcategories = subcategoriesMap[category.id];
          }
        });
        
        // Sort categories by index
        fetchedCategories.sort((a, b) => a.index - b.index);
        
        setCategories(fetchedCategories);
      } catch (err) {
        console.error('Error fetching categories:', err);
        setError('Failed to load categories. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchCategories();
  }, []);

  // Add a new category to Firestore
  const handleAddCategory = async () => {
    if (newCategory.name) {
      try {
        const categoryData = {
          name: newCategory.name,
          branchCode: 'main', // Default value
          image: newCategory.image || '',
          index: categories.length, // Set as last item
          isParentCategory: true,
          keyword: [newCategory.name.toLowerCase()], // Basic keyword for searching
        };
        
        const docRef = await addDoc(collection(db, 'Categories'), categoryData);
        
        // Add to local state
        setCategories([...categories, {
          id: docRef.id,
          ...categoryData,
          subcategories: []
        }]);
        
        // Reset form
        setNewCategory({ name: '', branchCode: '', image: '' });
        setShowNewCategoryForm(false);
      } catch (err) {
        console.error('Error adding category:', err);
        alert('Failed to add category. Please try again.');
      }
    }
  };

  // Add a subcategory to a category in Firestore
  const handleAddSubcategory = async (categoryId) => {
    if (newSubcategory.name) {
      try {
        const subcategoryData = {
          name: newSubcategory.name,
          branchCode: 'main', // Default value
          image: '',
          index: 0, // You might want to calculate this based on existing subcategories
          isParentCategory: false,
          keyword: [newSubcategory.name.toLowerCase()], // Basic keyword for searching
          parentCategoryID: categoryId
        };
        
        const docRef = await addDoc(collection(db, 'Categories'), subcategoryData);
        
        // Update local state
        const updatedCategories = categories.map(cat => {
          if (cat.id === categoryId) {
            return {
              ...cat,
              subcategories: [...(cat.subcategories || []), {
                id: docRef.id,
                ...subcategoryData
              }]
            };
          }
          return cat;
        });
        
        setCategories(updatedCategories);
        setNewSubcategory({ name: '', branchCode: '', image: '' });
        setShowNewSubcategoryForm(null);
      } catch (err) {
        console.error('Error adding subcategory:', err);
        alert('Failed to add subcategory. Please try again.');
      }
    }
  };

  // Delete a category from Firestore
  const handleDeleteCategory = async (categoryId) => {
    if (confirm('Are you sure you want to delete this category?')) {
      try {
        await deleteDoc(doc(db, 'Categories', categoryId));
        setCategories(categories.filter(category => category.id !== categoryId));
      } catch (err) {
        console.error('Error deleting category:', err);
        alert('Failed to delete category. Please try again.');
      }
    }
  };

  // Delete a subcategory from a category in Firestore
  const handleDeleteSubcategory = async (categoryId, subcategoryId) => {
    if (confirm('Are you sure you want to delete this subcategory?')) {
      try {
        const categoryRef = doc(db, 'Categories', categoryId);
        const category = categories.find(cat => cat.id === categoryId);
        
        if (!category) return;
        
        const updatedSubcategories = category.subcategories.filter(
          sub => sub.id !== subcategoryId
        );
        
        // Update the category document with the filtered subcategories
        await updateDoc(categoryRef, {
          subcategories: updatedSubcategories
        });
        
        // Update local state
        const updatedCategories = categories.map(cat => {
          if (cat.id === categoryId) {
            return {
              ...cat,
              subcategories: updatedSubcategories
            };
          }
          return cat;
        });
        
        setCategories(updatedCategories);
      } catch (err) {
        console.error('Error deleting subcategory:', err);
        alert('Failed to delete subcategory. Please try again.');
      }
    }
  };

  // Update a category in Firestore
  const handleSaveCategory = async (categoryId) => {
    if (editCategoryData.name) {
      try {
        const categoryRef = doc(db, 'Categories', categoryId);
        const category = categories.find(cat => cat.id === categoryId);
        
        if (!category) return;
        
        const updatedData = {
          name: editCategoryData.name,
          branchCode: editCategoryData.branchCode,
          image: editCategoryData.image || '',
          index: category.index, // Keep the original index
          isParentCategory: category.isParentCategory,
          keyword: [editCategoryData.name.toLowerCase()] // Update keyword
        };
        
        await updateDoc(categoryRef, updatedData);
        
        // Update local state
        const updatedCategories = categories.map(cat => {
          if (cat.id === categoryId) {
            return {
              ...cat,
              ...updatedData
            };
          }
          return cat;
        });
        
        setCategories(updatedCategories);
        setEditingCategory(null);
      } catch (err) {
        console.error('Error updating category:', err);
        alert('Failed to update category. Please try again.');
      }
    }
  };

  // Update a subcategory in Firestore
  const handleSaveSubcategory = async (categoryId, subcategoryId) => {
    if (editSubcategoryData.name) {
      try {
        const categoryRef = doc(db, 'Categories', categoryId);
        const category = categories.find(cat => cat.id === categoryId);
        
        if (!category) return;
        
        const updatedSubcategories = category.subcategories.map(sub => {
          if (sub.id === subcategoryId) {
            return {
              ...sub,
              name: editSubcategoryData.name,
              branchCode: editSubcategoryData.branchCode,
              image: editSubcategoryData.image || '',
              index: sub.index, // Keep the original index
              keyword: [editSubcategoryData.name.toLowerCase()] // Update keyword
            };
          }
          return sub;
        });
        
        // Update the category document with the updated subcategories
        await updateDoc(categoryRef, {
          subcategories: updatedSubcategories
        });
        
        // Update local state
        const updatedCategories = categories.map(cat => {
          if (cat.id === categoryId) {
            return {
              ...cat,
              subcategories: updatedSubcategories
            };
          }
          return cat;
        });
        
        setCategories(updatedCategories);
        setEditingSubcategory(null);
      } catch (err) {
        console.error('Error updating subcategory:', err);
        alert('Failed to update subcategory. Please try again.');
      }
    }
  };

  // Start editing a category
  const startEditingCategory = (categoryId) => {
    const categoryToEdit = categories.find(cat => cat.id === categoryId);
    if (categoryToEdit) {
      setEditCategoryData({
        name: categoryToEdit.name,
        branchCode: categoryToEdit.branchCode,
        image: categoryToEdit.image || ''
      });
      setEditingCategory(categoryId);
    }
  };

  // Start editing a subcategory
  const startEditingSubcategory = (categoryId, subcategoryId) => {
    const category = categories.find(cat => cat.id === categoryId);
    if (category) {
      const subcategoryToEdit = category.subcategories.find(sub => sub.id === subcategoryId);
      if (subcategoryToEdit) {
        setEditSubcategoryData({
          name: subcategoryToEdit.name,
          branchCode: subcategoryToEdit.branchCode,
          image: subcategoryToEdit.image || ''
        });
        setEditingSubcategory({ categoryId, subcategoryId });
      }
    }
  };

  return (
    <div className="p-6">
      {/* Loading state */}
      {loading && (
        <div className="text-center py-10">
          <p className="text-gray-600">Loading categories...</p>
        </div>
      )}

      {/* Error state */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 p-4 rounded-lg mb-6">
          <p>{error}</p>
        </div>
      )}

      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Categories</h1>
          <p className="text-gray-600 mt-1">Manage your product categories and subcategories</p>
        </div>
        <Button
          variant="primary"
          onClick={() => setShowNewCategoryForm(true)}
        >
          <Plus className="w-4 h-4 mr-2" />
          Add Category
        </Button>
      </div>

      <div className="grid gap-6">
        {/* New Category Form */}
        {showNewCategoryForm && (
          <Card>
            <div className="p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Add New Category</h2>
              <div className="space-y-4">
                <Input
                  label="Name"
                  type="text"
                  value={newCategory.name}
                  onChange={(e) => setNewCategory({ ...newCategory, name: e.target.value })}
                  required
                  startIcon={<Type className="h-4 w-4 text-gray-400" />}
                  placeholder="Enter category name"
                />
                <Input
                  label="Branch Code"
                  type="text"
                  value={newCategory.branchCode}
                  onChange={(e) => setNewCategory({ ...newCategory, branchCode: e.target.value })}
                  startIcon={<Type className="h-4 w-4 text-gray-400" />}
                  placeholder="Enter branch code"
                />
                <Input
                  label="Image URL"
                  type="text"
                  value={newCategory.image}
                  onChange={(e) => setNewCategory({ ...newCategory, image: e.target.value })}
                  startIcon={<Image className="h-4 w-4 text-gray-400" />}
                  placeholder="Enter image URL"
                />
                <div className="flex justify-end space-x-2">
                  <Button
                    variant="outline"
                    onClick={() => setShowNewCategoryForm(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="primary"
                    onClick={handleAddCategory}
                  >
                    Add Category
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        )}

        {/* Categories List */}
        {categories.map(category => (
          <Card key={category.id}>
            <div className="p-6">
              {editingCategory === category.id ? (
                // Edit Category Form
                <div className="space-y-4">
                  <h2 className="text-lg font-semibold text-gray-900 mb-4">Edit Category</h2>
                  <Input
                    label="Name"
                    type="text"
                    value={editCategoryData.name}
                    onChange={(e) => setEditCategoryData({ ...editCategoryData, name: e.target.value })}
                    required
                    startIcon={<Type className="h-4 w-4 text-gray-400" />}
                  />
                  <Input
                    label="Branch Code"
                    type="text"
                    value={editCategoryData.branchCode}
                    onChange={(e) => setEditCategoryData({ ...editCategoryData, branchCode: e.target.value })}
                    startIcon={<Type className="h-4 w-4 text-gray-400" />}
                  />
                  <Input
                    label="Image URL"
                    type="text"
                    value={editCategoryData.image}
                    onChange={(e) => setEditCategoryData({ ...editCategoryData, image: e.target.value })}
                    startIcon={<Image className="h-4 w-4 text-gray-400" />}
                  />
                  <div className="flex justify-end space-x-2">
                    <Button
                      variant="outline"
                      onClick={() => setEditingCategory(null)}
                    >
                      Cancel
                    </Button>
                    <Button
                      variant="primary"
                      onClick={() => handleSaveCategory(category.id)}
                    >
                      Save Changes
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="flex items-start justify-between">
                <div className="flex items-start space-x-4">
                  {category.image && (
                    <img
                      src={category.image}
                      alt={category.name}
                      className="w-16 h-16 rounded-lg object-cover"
                    />
                  )}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{category.name}</h3>
                    <p className="text-gray-600">{category.branchCode}</p>
                    <div className="mt-2">
                      <Badge variant="secondary">
                        {category.subcategories.length} Subcategories
                      </Badge>
                    </div>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => startEditingCategory(category.id)}
                  >
                    <Edit2 className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDeleteCategory(category.id)}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
              )}

              {/* Subcategories */}
              <div className="mt-6">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-sm font-medium text-gray-700">Subcategories</h4>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowNewSubcategoryForm(category.id)}
                  >
                    <FolderPlus className="w-4 h-4 mr-2" />
                    Add Subcategory
                  </Button>
                </div>

                {/* New Subcategory Form */}
                {showNewSubcategoryForm === category.id && (
                  <div className="mb-4 p-4 bg-gray-50 rounded-lg">
                    <div className="space-y-4">
                      <Input
                        label="Name"
                        type="text"
                        value={newSubcategory.name}
                        onChange={(e) => setNewSubcategory({ ...newSubcategory, name: e.target.value })}
                        required
                        startIcon={<Type className="h-4 w-4 text-gray-400" />}
                        placeholder="Enter subcategory name"
                      />
                      <Input
                        label="Branch Code"
                        type="text"
                        value={newSubcategory.branchCode}
                        onChange={(e) => setNewSubcategory({ ...newSubcategory, branchCode: e.target.value })}
                        startIcon={<Type className="h-4 w-4 text-gray-400" />}
                        placeholder="Enter branch code"
                      />
                      <Input
                        label="Image URL"
                        type="text"
                        value={newSubcategory.image}
                        onChange={(e) => setNewSubcategory({ ...newSubcategory, image: e.target.value })}
                        startIcon={<Image className="h-4 w-4 text-gray-400" />}
                        placeholder="Enter image URL"
                      />
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setShowNewSubcategoryForm(null)}
                        >
                          Cancel
                        </Button>
                        <Button
                          variant="primary"
                          size="sm"
                          onClick={() => handleAddSubcategory(category.id)}
                        >
                          Add Subcategory
                        </Button>
                      </div>
                    </div>
                  </div>
                )}

                <div className="space-y-2">
                  {category.subcategories.map(subcategory => (
                    <div
                      key={subcategory.id}
                      className="p-3 bg-gray-50 rounded-lg"
                    >
                      {editingSubcategory &&
                       editingSubcategory.categoryId === category.id &&
                       editingSubcategory.subcategoryId === subcategory.id ? (
                        // Edit Subcategory Form
                        <div className="space-y-4">
                          <Input
                            label="Name"
                            type="text"
                            value={editSubcategoryData.name}
                            onChange={(e) => setEditSubcategoryData({ ...editSubcategoryData, name: e.target.value })}
                            required
                            startIcon={<Type className="h-4 w-4 text-gray-400" />}
                          />
                          <Input
                            label="Branch Code"
                            type="text"
                            value={editSubcategoryData.branchCode}
                            onChange={(e) => setEditSubcategoryData({ ...editSubcategoryData, branchCode: e.target.value })}
                            startIcon={<Type className="h-4 w-4 text-gray-400" />}
                          />
                          <Input
                            label="Image URL"
                            type="text"
                            value={editSubcategoryData.image}
                            onChange={(e) => setEditSubcategoryData({ ...editSubcategoryData, image: e.target.value })}
                            startIcon={<Image className="h-4 w-4 text-gray-400" />}
                          />
                          <div className="flex justify-end space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setEditingSubcategory(null)}
                            >
                              Cancel
                            </Button>
                            <Button
                              variant="primary"
                              size="sm"
                              onClick={() => handleSaveSubcategory(category.id, subcategory.id)}
                            >
                              Save Changes
                            </Button>
                          </div>
                        </div>
                      ) : (
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <ChevronRight className="w-4 h-4 text-gray-400 mr-2" />
                            <div>
                              <p className="text-sm font-medium text-gray-900">{subcategory.name}</p>
                              <p className="text-xs text-gray-600">{subcategory.branchCode}</p>
                            </div>
                          </div>
                          <div className="flex space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => startEditingSubcategory(category.id, subcategory.id)}
                            >
                              <Edit2 className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteSubcategory(category.id, subcategory.id)}
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
}



