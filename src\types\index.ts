// Authentication types
export interface User {
  id: string;
  username: string;
  role: 'admin' | 'manager';
  avatar?: string;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

// Product types
export interface ProductVariant {
  id: string;
  name: string;
  price: number;
  weight: string;
  unit: string;
  stock: number;
  barcode: string;
}

export interface Product {
  id: string;
  name: string;
  tamilName?: string;
  description: string;
  // Legacy fields for backward compatibility
  category: string;
  subcategory?: string;
  // New multi-category system
  categoryIDs?: string[];
  primaryCategoryID?: string;
  images: string[];
  price: number;
  stock: number;
  unit: string;
  barcode: string;
  isVisible: boolean;
  status: 'available' | 'coming_soon' | 'pre_order' | 'out_of_stock';
  harvestDate?: string;
  farmInfo?: {
    name: string;
    location: string;
  };
  farmerId?: string;
  nutrition?: string;
  storageInstruction?: string;
  // New variant system
  hasVariants?: boolean;
  defaultVariantID?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ProductVariant {
  id: string;
  productId: string;
  barcode: string;
  variationValues: Record<string, string>; // e.g., { Size: "Small", Color: "Red" }
  price: number;
  mrp: number;
  stock: number;
  image?: string;
  status: 'active' | 'inActive';
  createdAt: string;
  updatedAt: string;
}

export interface VariationType {
  id: string;
  name: string; // e.g., "Size", "Color", "Weight"
  options: string[]; // e.g., ["Small", "Medium", "Large"]
  description?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Category {
  id: string;
  name: string;
  tamilName?: string;
  slug?: string;
  description?: string;
  image?: string;
  subcategories?: SubCategory[];
  // New fields for enhanced category system
  branchCode?: string;
  index?: number;
  isParentCategory?: boolean;
  keyword?: string[];
  parentCategoryID?: string | null;
  level?: number; // 0 = root, 1 = sub, etc.
}

export interface SubCategory {
  id: string;
  name: string;
  tamilName?: string;
  slug?: string;
  parentId?: string;
  parentCategoryID?: string;
  description?: string;
  image?: string;
  branchCode?: string;
  index?: number;
  keyword?: string[];
}

// Order types
export interface OrderItem {
  id: string;
  productId: string;
  productName: string;
  tamilName?: string;
  price: number;
  quantity: number;
  unit: string;
  subtotal: number;
  returned?: boolean;
  returnedQuantity?: number;
  // New variant support
  variantID?: string | null;
  variationValues?: Record<string, string> | null;
}

export interface Order {
  id: string;
  orderID?: number;
  customerId?: string;
  customerName?: string;
  customerPhone?: string;
  customerEmail?: string;
  items: OrderItem[];
  subtotal: number;
  tax: number;
  total: number;
  status: 'placed' | 'processing' | 'out_for_delivery' | 'delivered' | 'cancelled' | string;
  paymentStatus: 'pending' | 'paid' | 'refunded' | 'failed' | 'unpaid' | string;
  paymentMethod: 'cash' | 'card' | 'upi' | 'cod' | string;
  createdAt: string;
  updatedAt: string;
  deliveryAddress?: {
    type?: string;
    line1?: string;
    line2?: string;
    city?: string;
    pincode?: string;
    phoneNumber?: string;
  };
  deliveryCharge?: number;
  cancellationReason?: string;
  hasReturns?: boolean;
  // Coupon fields
  couponCode?: string | null;
  couponDiscount?: number | null;
}

// Customer types
export interface Address {
  id: string;
  type: 'home' | 'office' | 'other' | string;
  line1: string;
  line2?: string;
  city: string;
  state?: string; // Make state optional
  pincode: string;
  isDefault?: boolean;
  phoneNumber?: string;
}

export interface Customer {
  id: string;
  name: string;
  phone: string;
  email?: string; // Make email optional
  addresses: Address[];
  totalOrders: number;
  totalSpent: number;
  lastOrderDate: string | null;
  createdAt: string;
  status: 'active' | 'inactive';
}

// Dashboard types
export interface SalesData {
  period: 'daily' | 'weekly' | 'monthly' | 'yearly';
  data: {
    labels: string[];
    values: number[];
  };
}

export interface OrderSummary {
  totalOrders: number;
  newOrders: number;
  processing: number;
  outForDelivery: number;
  delivered: number;
  cancelled: number;
}

export interface ProductSummary {
  totalProducts: number;
  lowStock: number;
  outOfStock: number;
}

export interface CustomerSummary {
  totalCustomers: number;
  newCustomers: number;
  returningCustomers: number;
}

// Cart types
export interface CartItem {
  id: string;
  productId: string;
  productName: string;
  tamilName?: string;
  price: number;
  quantity: number;
  unit: string;
  subtotal: number;
  product?: Product;
  // New variant support
  variantID?: string | null;
  variationValues?: Record<string, string> | null;
  variant?: ProductVariant;
}

// Farmer types
export interface Farmer {
  id: string;
  farmerName: string;
  farmName: string;
  farmLocation: string;
  experience: string;
  philosophy: string;
  certifications: string[];
  tags: string[];
  createdAt: string;
  updatedAt: string;
}

// POS types
export interface CustomerModalContentProps {
  customerSearchQuery: string;
  onSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onSearchKeyDown: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  onSearch: () => void;
  onClearSearch: () => void;
  onClose: () => void;
  onAddNewCustomer: () => void;
  onSelectCustomer: (customer: Customer) => void;
  isLoadingCustomers: boolean;
  isSearchingCustomers: boolean;
  filteredCustomers: Customer[];
  formatPhoneNumber: (phone: string) => string;
}

// Coupon types
export interface Coupon {
  id: string;
  code: string;
  type: 'FLAT' | 'PERCENT';
  flatAmount?: number;
  percent?: number;
  maxDiscount?: number | null;
  minCartTotal?: number | null;
  startAt?: string | null;
  expiresAt: string;
  usageLimitGlobal?: number | null;
  usageLimitPerUser?: number | null;
  usedCount: number;
  isActive: boolean;
  description?: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface Redemption {
  id: string;
  uid: string;
  orderID: number;
  discountAmount: number;
  redeemedAt: string;
  branchCode?: string;
}

// Delivery Charges (Pincodes) types
export interface DeliveryCharge {
  id: string;
  pincode: string;
  areaName: string;
  branchCode: string;
  branchName: string;
  deliveryCharge: number;
  createdAt?: string;
  updatedAt?: string;
}
