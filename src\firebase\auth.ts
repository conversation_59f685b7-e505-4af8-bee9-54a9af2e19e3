import { 
  createUserWithEmailAndPassword, 
  signInWithEmailAndPassword, 
  signOut, 
  UserCredential,
  updateProfile,
  User as FirebaseUser
} from 'firebase/auth';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import { auth, db } from './config';
import { User } from '../types';

// Sign in with email and password
export const signIn = async (email: string, password: string): Promise<User | null> => {
  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    const userData = await getUserData(userCredential.user);
    
    // Check if user has admin role
    if (userData && userData.role === 'admin') {
      return userData;
    } else {
      console.error('Access denied: User does not have admin privileges');
      await signOut(auth); // Sign out non-admin users
      return null;
    }
  } catch (error) {
    console.error('Error signing in:', error);
    return null;
  }
};

// Sign out
export const logout = async (): Promise<void> => {
  try {
    await signOut(auth);
  } catch (error) {
    console.error('Error signing out:', error);
  }
};

// Create a new user
export const createUser = async (
  email: string, 
  password: string, 
  displayName: string,
  role: 'admin' | 'customer' = 'customer'
): Promise<User | null> => {
  try {
    // Create the user in Firebase Authentication
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const { user } = userCredential;
    
    // Update the user's display name
    await updateProfile(user, { displayName });
    
    // Create a document in the users collection
    const userData = {
      id: user.uid,
      username: displayName,
      email: user.email,
      role,
      createdAt: new Date().toISOString(),
    };
    
    await setDoc(doc(db, 'users', user.uid), userData);
    
    return {
      id: user.uid,
      username: displayName,
      role,
      avatar: user.photoURL || undefined,
    };
  } catch (error) {
    console.error('Error creating user:', error);
    return null;
  }
};

// Get user data from Firestore
export const getUserData = async (user: FirebaseUser): Promise<User | null> => {
  try {
    const userDoc = await getDoc(doc(db, 'Users', user.uid));
    
    if (userDoc.exists()) {
      const userData = userDoc.data();
      return {
        id: user.uid,
        username: userData.username || user.displayName || '',
        role: userData.role || 'customer',
        avatar: user.photoURL || undefined,
      };
    } else {
      // If user document doesn't exist, create one with default values
      const newUser = {
        id: user.uid,
        username: user.displayName || '',
        email: user.email || '',
        role: 'customer',
        createdAt: new Date().toISOString(),
      };
      
      await setDoc(doc(db, 'Users', user.uid), newUser);
      
      return {
        id: user.uid,
        username: user.displayName || '',
        role: 'customer',
        avatar: user.photoURL || undefined,
      };
    }
  } catch (error) {
    console.error('Error getting user data:', error);
    return null;
  }
};
