import React, { useState, useRef } from 'react';
import { Upload, Download, FileText, AlertCircle, CheckCircle, X, Loader2 } from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { Badge } from '../ui/Badge';
import { 
  BulkImportResult, 
  BulkImportOptions, 
  BulkExportOptions,
  BulkProductRow 
} from '../../types/bulkProduct';
import { parseCSVFile, generateCSVTemplate, generateCSVContent, downloadCSV } from '../../utils/csvUtils';
import { importProducts, exportProducts } from '../../services/bulkProductService';

interface BulkProductManagerProps {
  onImportComplete?: () => void;
}

export function BulkProductManager({ onImportComplete }: BulkProductManagerProps) {
  const [isImporting, setIsImporting] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [importResult, setImportResult] = useState<BulkImportResult | null>(null);
  const [showImportModal, setShowImportModal] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);
  const [csvData, setCsvData] = useState<BulkProductRow[]>([]);
  const [importOptions, setImportOptions] = useState<BulkImportOptions>({
    overwriteExisting: false,
    validateOnly: false,
    skipInvalidRows: true
  });
  const [exportOptions, setExportOptions] = useState<BulkExportOptions>({
    includeVariants: true,
    includeImages: true,
    categoryFilter: 'All',
    statusFilter: 'All'
  });
  
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle file upload
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.name.endsWith('.csv')) {
      alert('Please select a CSV file');
      return;
    }

    try {
      setIsImporting(true);
      const data = await parseCSVFile(file);
      setCsvData(data);
      setShowImportModal(true);
    } catch (error) {
      console.error('Error parsing CSV:', error);
      alert('Error parsing CSV file. Please check the format and try again.');
    } finally {
      setIsImporting(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  // Handle import
  const handleImport = async () => {
    if (!csvData.length) return;

    try {
      setIsImporting(true);
      const result = await importProducts(csvData, importOptions);
      setImportResult(result);
      
      if (result.success && onImportComplete) {
        onImportComplete();
      }
    } catch (error) {
      console.error('Error importing products:', error);
      setImportResult({
        success: false,
        totalRows: csvData.length,
        successCount: 0,
        errorCount: 1,
        skippedCount: 0,
        errors: [{ row: 0, message: `Import failed: ${error}` }],
        warnings: []
      });
    } finally {
      setIsImporting(false);
    }
  };

  // Handle export
  const handleExport = async () => {
    try {
      setIsExporting(true);
      const { products, variants } = await exportProducts(exportOptions);
      const csvContent = generateCSVContent(products, variants);
      
      const timestamp = new Date().toISOString().split('T')[0];
      const filename = `products-export-${timestamp}.csv`;
      
      downloadCSV(csvContent, filename);
      setShowExportModal(false);
    } catch (error) {
      console.error('Error exporting products:', error);
      alert('Error exporting products. Please try again.');
    } finally {
      setIsExporting(false);
    }
  };

  // Download template
  const handleDownloadTemplate = () => {
    const templateContent = generateCSVTemplate();
    downloadCSV(templateContent, 'product-import-template.csv');
  };

  // Reset import state
  const resetImport = () => {
    setShowImportModal(false);
    setImportResult(null);
    setCsvData([]);
  };

  return (
    <div className="space-y-4">
      {/* Action Buttons */}
      <div className="flex flex-wrap gap-3">
        <Button
          variant="outline"
          onClick={() => fileInputRef.current?.click()}
          disabled={isImporting}
          className="shadow-sm hover:shadow transition-shadow"
        >
          {isImporting ? (
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
          ) : (
            <Upload className="w-4 h-4 mr-2" />
          )}
          Bulk Upload
        </Button>

        <Button
          variant="outline"
          onClick={() => setShowExportModal(true)}
          disabled={isExporting}
          className="shadow-sm hover:shadow transition-shadow"
        >
          {isExporting ? (
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
          ) : (
            <Download className="w-4 h-4 mr-2" />
          )}
          Export All Products
        </Button>

        <Button
          variant="outline"
          onClick={handleDownloadTemplate}
          className="shadow-sm hover:shadow transition-shadow"
        >
          <FileText className="w-4 h-4 mr-2" />
          Download CSV Template
        </Button>
      </div>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept=".csv"
        onChange={handleFileUpload}
        className="hidden"
      />

      {/* Import Modal */}
      {showImportModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">Import Products</h3>
                <Button variant="ghost" size="sm" onClick={resetImport}>
                  <X className="w-4 h-4" />
                </Button>
              </div>

              {!importResult ? (
                <div className="space-y-4">
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <p className="text-sm text-blue-800">
                      Found <strong>{csvData.length}</strong> rows in the CSV file.
                    </p>
                  </div>

                  {/* Import Options */}
                  <div className="space-y-3">
                    <h4 className="font-medium">Import Options</h4>
                    
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={importOptions.overwriteExisting}
                        onChange={(e) => setImportOptions(prev => ({
                          ...prev,
                          overwriteExisting: e.target.checked
                        }))}
                        className="rounded border-gray-300"
                      />
                      <span className="text-sm">Overwrite existing products (match by SKU)</span>
                    </label>

                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={importOptions.validateOnly}
                        onChange={(e) => setImportOptions(prev => ({
                          ...prev,
                          validateOnly: e.target.checked
                        }))}
                        className="rounded border-gray-300"
                      />
                      <span className="text-sm">Validate only (don't import)</span>
                    </label>

                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={importOptions.skipInvalidRows}
                        onChange={(e) => setImportOptions(prev => ({
                          ...prev,
                          skipInvalidRows: e.target.checked
                        }))}
                        className="rounded border-gray-300"
                      />
                      <span className="text-sm">Skip invalid rows and continue</span>
                    </label>
                  </div>

                  <div className="flex justify-end space-x-3">
                    <Button variant="outline" onClick={resetImport}>
                      Cancel
                    </Button>
                    <Button 
                      variant="primary" 
                      onClick={handleImport}
                      disabled={isImporting}
                    >
                      {isImporting ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          {importOptions.validateOnly ? 'Validating...' : 'Importing...'}
                        </>
                      ) : (
                        importOptions.validateOnly ? 'Validate' : 'Import'
                      )}
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  {/* Import Results */}
                  <div className={`border rounded-lg p-4 ${
                    importResult.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                  }`}>
                    <div className="flex items-center space-x-2 mb-2">
                      {importResult.success ? (
                        <CheckCircle className="w-5 h-5 text-green-600" />
                      ) : (
                        <AlertCircle className="w-5 h-5 text-red-600" />
                      )}
                      <h4 className={`font-medium ${
                        importResult.success ? 'text-green-800' : 'text-red-800'
                      }`}>
                        {importResult.success ? 'Import Successful' : 'Import Failed'}
                      </h4>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>Total Rows: <Badge variant="secondary">{importResult.totalRows}</Badge></div>
                      <div>Successful: <Badge variant="success">{importResult.successCount}</Badge></div>
                      <div>Errors: <Badge variant="destructive">{importResult.errorCount}</Badge></div>
                      <div>Skipped: <Badge variant="warning">{importResult.skippedCount}</Badge></div>
                    </div>
                  </div>

                  {/* Errors */}
                  {importResult.errors.length > 0 && (
                    <div className="space-y-2">
                      <h5 className="font-medium text-red-800">Errors:</h5>
                      <div className="max-h-40 overflow-y-auto space-y-1">
                        {importResult.errors.map((error, index) => (
                          <div key={index} className="text-sm bg-red-100 border border-red-200 rounded p-2">
                            <span className="font-medium">Row {error.row}:</span> {error.message}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Warnings */}
                  {importResult.warnings.length > 0 && (
                    <div className="space-y-2">
                      <h5 className="font-medium text-yellow-800">Warnings:</h5>
                      <div className="max-h-40 overflow-y-auto space-y-1">
                        {importResult.warnings.map((warning, index) => (
                          <div key={index} className="text-sm bg-yellow-100 border border-yellow-200 rounded p-2">
                            <span className="font-medium">Row {warning.row}:</span> {warning.message}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="flex justify-end">
                    <Button variant="primary" onClick={resetImport}>
                      Close
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </Card>
        </div>
      )}

      {/* Export Modal */}
      {showExportModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-md">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">Export Products</h3>
                <Button variant="ghost" size="sm" onClick={() => setShowExportModal(false)}>
                  <X className="w-4 h-4" />
                </Button>
              </div>

              <div className="space-y-4">
                <div className="space-y-3">
                  <h4 className="font-medium">Export Options</h4>
                  
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={exportOptions.includeVariants}
                      onChange={(e) => setExportOptions(prev => ({
                        ...prev,
                        includeVariants: e.target.checked
                      }))}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm">Include product variants</span>
                  </label>

                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={exportOptions.includeImages}
                      onChange={(e) => setExportOptions(prev => ({
                        ...prev,
                        includeImages: e.target.checked
                      }))}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm">Include image URLs</span>
                  </label>
                </div>

                <div className="flex justify-end space-x-3">
                  <Button variant="outline" onClick={() => setShowExportModal(false)}>
                    Cancel
                  </Button>
                  <Button 
                    variant="primary" 
                    onClick={handleExport}
                    disabled={isExporting}
                  >
                    {isExporting ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Exporting...
                      </>
                    ) : (
                      'Export'
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
}
