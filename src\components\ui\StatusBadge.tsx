import { Badge } from './Badge';

interface OrderStatusBadgeProps {
  status: 'placed' | 'processing' | 'out_for_delivery' | 'delivered' | 'cancelled' | string;
}

interface ReturnStatusBadgeProps {
  status: 'pending' | 'approved' | 'rejected' | 'completed' | string;
}

export const OrderStatusBadge = ({ status }: OrderStatusBadgeProps) => {
  const statusConfig = {
    placed: { variant: 'info', label: 'Order Placed' },
    processing: { variant: 'primary', label: 'Processing' },
    out_for_delivery: { variant: 'warning', label: 'Out for Delivery' },
    delivered: { variant: 'success', label: 'Delivered' },
    cancelled: { variant: 'danger', label: 'Cancelled' },
    // Add any other known statuses from the database
    confirmed: { variant: 'primary', label: 'Confirmed' },
    assigned: { variant: 'warning', label: 'Assigned' },
    picked: { variant: 'warning', label: 'Picked' },
    completed: { variant: 'success', label: 'Completed' },
    // Default for any other status
    default: { variant: 'gray', label: 'Unknown' }
  };

  // Use the status config if it exists, otherwise use the default
  const config = statusConfig[status] || statusConfig.default;

  // For debugging
  if (!statusConfig[status]) {
    console.log(`Unknown order status: ${status}, using default styling`);
  }

  return (
    <Badge variant={config.variant as any}>
      {status === 'default' ? status : config.label}
    </Badge>
  );
};

interface ProductStatusBadgeProps {
  status: 'available' | 'coming_soon' | 'pre_order' | 'out_of_stock' | 'active' | 'inactive' | string;
}

export const ProductStatusBadge = ({ status }: ProductStatusBadgeProps) => {
  const statusConfig = {
    available: { variant: 'success', label: 'Available' },
    coming_soon: { variant: 'info', label: 'Coming Soon' },
    pre_order: { variant: 'warning', label: 'Pre-Order' },
    out_of_stock: { variant: 'danger', label: 'Out of Stock' },
    active: { variant: 'success', label: 'Active' },
    inactive: { variant: 'gray', label: 'Inactive' },
    // Default for any other status
    default: { variant: 'gray', label: 'Unknown' }
  };

  // Use the status config if it exists, otherwise use the default
  const config = statusConfig[status] || statusConfig.default;

  // For debugging
  if (!statusConfig[status]) {
    console.log(`Unknown product status: ${status}, using default styling`);
  }

  return (
    <Badge variant={config.variant as any}>
      {status === 'default' ? status : config.label}
    </Badge>
  );
};

interface PaymentStatusBadgeProps {
  status: 'pending' | 'paid' | 'refunded' | 'failed' | 'unpaid' | string;
}

export const PaymentStatusBadge = ({ status }: PaymentStatusBadgeProps) => {
  const statusConfig = {
    pending: { variant: 'warning', label: 'Pending' },
    unpaid: { variant: 'warning', label: 'Unpaid' },
    paid: { variant: 'success', label: 'Paid' },
    refunded: { variant: 'info', label: 'Refunded' },
    failed: { variant: 'danger', label: 'Failed' },
    // Default for any other status
    default: { variant: 'gray', label: 'Unknown' }
  };

  // Use the status config if it exists, otherwise use the default
  const config = statusConfig[status] || statusConfig.default;

  // For debugging
  if (!statusConfig[status]) {
    console.log(`Unknown payment status: ${status}, using default styling`);
  }

  return (
    <Badge variant={config.variant as any}>
      {status === 'default' ? status : config.label}
    </Badge>
  );
};

export const ReturnStatusBadge = ({ status }: ReturnStatusBadgeProps) => {
  const statusConfig = {
    pending: { variant: 'warning', label: 'Pending' },
    approved: { variant: 'success', label: 'Approved' },
    rejected: { variant: 'danger', label: 'Rejected' },
    completed: { variant: 'info', label: 'Completed' },
    // Default for any other status
    default: { variant: 'gray', label: 'Unknown' }
  };

  // Use the status config if it exists, otherwise use the default
  const config = statusConfig[status] || statusConfig.default;

  // For debugging
  if (!statusConfig[status]) {
    console.log(`Unknown return status: ${status}, using default styling`);
  }

  return (
    <Badge variant={config.variant as any}>
      {status === 'default' ? status : config.label}
    </Badge>
  );
};