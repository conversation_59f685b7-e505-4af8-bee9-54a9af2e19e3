import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  setDoc,
  query,
  where,
  orderBy,
  serverTimestamp,
  Timestamp,
  limit,
  startAfter,
  QueryDocumentSnapshot,
  getCountFromServer
} from 'firebase/firestore';
import { db } from '../firebase/config';
import { DeliveryCharge } from '../types';
import { checkAuth } from './authService';

// Collection name
const COLLECTION_NAME = 'Pincodes';

// Helper function to convert Firestore timestamp to string
const timestampToString = (timestamp: any): string => {
  if (!timestamp) return '';
  if (timestamp.toDate) {
    return timestamp.toDate().toISOString();
  }
  return timestamp;
};

// Helper function to map Firestore document to DeliveryCharge type
const mapDocToDeliveryCharge = (doc: any): DeliveryCharge => {
  const data = doc.data();
  return {
    id: doc.id,
    pincode: data.pincode || '',
    areaName: data.areaName || '',
    branchCode: data.branchCode || 'test',
    branchName: data.branchName || 'test default',
    deliveryCharge: data.deliveryCharge || 0,
    createdAt: timestampToString(data.createdAt),
    updatedAt: timestampToString(data.updatedAt)
  };
};

// Helper function to map DeliveryCharge type to Firestore document
const mapDeliveryChargeToDoc = (deliveryCharge: Omit<DeliveryCharge, 'id' | 'createdAt' | 'updatedAt'>) => {
  return {
    pincode: deliveryCharge.pincode,
    areaName: deliveryCharge.areaName,
    branchCode: deliveryCharge.branchCode || 'test',
    branchName: deliveryCharge.branchName || 'test default',
    deliveryCharge: deliveryCharge.deliveryCharge
  };
};

// Check if pincode already exists
export const checkPincodeExists = async (pincode: string, excludeId?: string): Promise<boolean> => {
  try {
    const q = query(
      collection(db, COLLECTION_NAME),
      where('pincode', '==', pincode)
    );
    
    const querySnapshot = await getDocs(q);
    
    if (excludeId) {
      // If we're editing, exclude the current document from the check
      return querySnapshot.docs.some(doc => doc.id !== excludeId);
    }
    
    return !querySnapshot.empty;
  } catch (error) {
    console.error('Error checking pincode:', error);
    return false;
  }
};

// Get all delivery charges with pagination
export const getDeliveryCharges = async (
  pageSize: number = 30,
  lastDoc?: QueryDocumentSnapshot
): Promise<{ deliveryCharges: DeliveryCharge[]; lastDoc: QueryDocumentSnapshot | null; hasMore: boolean }> => {
  try {
    await checkAuth();

    let q = query(
      collection(db, COLLECTION_NAME),
      orderBy('pincode', 'asc'),
      limit(pageSize + 1)
    );

    if (lastDoc) {
      q = query(
        collection(db, COLLECTION_NAME),
        orderBy('pincode', 'asc'),
        startAfter(lastDoc),
        limit(pageSize + 1)
      );
    }

    const querySnapshot = await getDocs(q);
    const docs = querySnapshot.docs;
    const hasMore = docs.length > pageSize;
    
    if (hasMore) {
      docs.pop(); // Remove the extra document
    }

    const deliveryCharges = docs.map(mapDocToDeliveryCharge);
    const newLastDoc = docs.length > 0 ? docs[docs.length - 1] : null;

    return { deliveryCharges, lastDoc: newLastDoc, hasMore };
  } catch (error) {
    console.error('Error getting delivery charges:', error);
    return { deliveryCharges: [], lastDoc: null, hasMore: false };
  }
};

// Get delivery charge by ID
export const getDeliveryChargeById = async (id: string): Promise<DeliveryCharge | null> => {
  try {
    await checkAuth();
    const docRef = doc(db, COLLECTION_NAME, id);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return mapDocToDeliveryCharge(docSnap);
    }
    return null;
  } catch (error) {
    console.error('Error getting delivery charge:', error);
    return null;
  }
};

// Get delivery charge by pincode
export const getDeliveryChargeByPincode = async (pincode: string): Promise<DeliveryCharge | null> => {
  try {
    const q = query(
      collection(db, COLLECTION_NAME),
      where('pincode', '==', pincode),
      limit(1)
    );
    
    const querySnapshot = await getDocs(q);
    
    if (!querySnapshot.empty) {
      return mapDocToDeliveryCharge(querySnapshot.docs[0]);
    }
    return null;
  } catch (error) {
    console.error('Error getting delivery charge by pincode:', error);
    return null;
  }
};

// Add a new delivery charge
export const addDeliveryCharge = async (
  deliveryCharge: Omit<DeliveryCharge, 'id' | 'createdAt' | 'updatedAt'>
): Promise<string | null> => {
  try {
    await checkAuth();

    // Check if pincode already exists
    const pincodeExists = await checkPincodeExists(deliveryCharge.pincode);
    if (pincodeExists) {
      throw new Error('Pincode already exists');
    }

    const docData = {
      ...mapDeliveryChargeToDoc(deliveryCharge),
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    };

    const docRef = await addDoc(collection(db, COLLECTION_NAME), docData);
    return docRef.id;
  } catch (error) {
    console.error('Error adding delivery charge:', error);
    return null;
  }
};

// Update an existing delivery charge
export const updateDeliveryCharge = async (
  id: string,
  updates: Partial<Omit<DeliveryCharge, 'id' | 'createdAt' | 'updatedAt'>>
): Promise<boolean> => {
  try {
    await checkAuth();
    
    // If pincode is being updated, check if it already exists
    if (updates.pincode) {
      const pincodeExists = await checkPincodeExists(updates.pincode, id);
      if (pincodeExists) {
        throw new Error('Pincode already exists');
      }
    }
    
    const docRef = doc(db, COLLECTION_NAME, id);
    const updateData = {
      ...mapDeliveryChargeToDoc(updates as any),
      updatedAt: serverTimestamp()
    };

    await updateDoc(docRef, updateData);
    return true;
  } catch (error) {
    console.error('Error updating delivery charge:', error);
    return false;
  }
};

// Delete a delivery charge
export const deleteDeliveryCharge = async (id: string): Promise<boolean> => {
  try {
    await checkAuth();
    const docRef = doc(db, COLLECTION_NAME, id);
    await deleteDoc(docRef);
    return true;
  } catch (error) {
    console.error('Error deleting delivery charge:', error);
    return false;
  }
};

// Search delivery charges by pincode or area name
export const searchDeliveryCharges = async (searchTerm: string): Promise<DeliveryCharge[]> => {
  try {
    await checkAuth();
    
    // Get all delivery charges and filter client-side for simplicity
    // In a production app, you might want to implement server-side search
    const result = await getDeliveryCharges(1000); // Get a large number for search
    
    const filtered = result.deliveryCharges.filter(charge =>
      charge.pincode.toLowerCase().includes(searchTerm.toLowerCase()) ||
      charge.areaName.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    return filtered;
  } catch (error) {
    console.error('Error searching delivery charges:', error);
    return [];
  }
};

// Get total count of delivery charges
export const getDeliveryChargesCount = async (): Promise<number> => {
  try {
    await checkAuth();
    const q = query(collection(db, COLLECTION_NAME));
    const countSnapshot = await getCountFromServer(q);
    return countSnapshot.data().count;
  } catch (error) {
    console.error('Error getting delivery charges count:', error);
    return 0;
  }
};
