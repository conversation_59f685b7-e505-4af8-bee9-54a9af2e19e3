import <PERSON> from 'papaparse';
import { BulkProductRow, CSV_HEADERS, ValidationResult } from '../types/bulkProduct';
import { Product, ProductVariant } from '../types';

// Parse CSV file to BulkProductRow array
export const parseCSVFile = (file: File): Promise<BulkProductRow[]> => {
  return new Promise((resolve, reject) => {
    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      transformHeader: (header: string) => header.trim(),
      transform: (value: string, header: string) => {
        // Transform specific fields
        if (header === 'isVisible' || header === 'isPreOrder' || header === 'hasVariants' || header === 'isVariant') {
          return value.toLowerCase() === 'true' || value === '1';
        }
        if (header === 'price' || header === 'mrp' || header === 'stock' || header === 'harvestOffsetDays') {
          return parseFloat(value) || 0;
        }
        return value.trim();
      },
      complete: (results) => {
        if (results.errors.length > 0) {
          reject(new Error(`CSV parsing errors: ${results.errors.map(e => e.message).join(', ')}`));
        } else {
          resolve(results.data as BulkProductRow[]);
        }
      },
      error: (error) => {
        reject(error);
      }
    });
  });
};

// Generate CSV content from products and variants
export const generateCSVContent = (products: Product[], variants: ProductVariant[]): string => {
  const rows: BulkProductRow[] = [];

  // Create a map of variants by product ID for quick lookup
  const variantsByProduct = variants.reduce((acc, variant) => {
    if (!acc[variant.productId]) {
      acc[variant.productId] = [];
    }
    acc[variant.productId].push(variant);
    return acc;
  }, {} as Record<string, ProductVariant[]>);

  products.forEach(product => {
    // Add main product row
    const productRow: BulkProductRow = {
      name: product.name,
      description: product.description,
      price: product.price,
      mrp: product.mrp || 0,
      stock: product.stock,
      unit: product.unit,
      barcode: product.barcode,
      status: product.status,
      isVisible: product.isVisible,
      category: product.category,
      categoryIDs: (product.categoryIDs || []).join(','),
      categoryPath: '', // Will be populated with full category path
      primaryCategoryID: product.primaryCategoryID || '',
      farmerId: product.farmerId || '',
      nutrition: product.nutrition || '',
      storageInstruction: product.storageInstruction || '',
      isPreOrder: product.isPreOrder || false,
      preOrderStartAt: formatDateForCSV(product.preOrderStartAt),
      preOrderEndAt: formatDateForCSV(product.preOrderEndAt),
      harvestOffsetDays: product.harvestOffsetDays || 0,
      hasVariants: product.hasVariants || false,
      defaultVariantID: product.defaultVariantID || '',
      images: (product.images || []).join(',')
    };

    rows.push(productRow);

    // Add variant rows if product has variants
    if (product.hasVariants && variantsByProduct[product.id]) {
      variantsByProduct[product.id].forEach(variant => {
        const variantRow: BulkProductRow = {
          ...productRow, // Start with product data
          // Override with variant-specific data
          price: variant.price,
          mrp: variant.mrp,
          stock: variant.stock,
          barcode: variant.barcode,
          status: variant.status === 'active' ? 'available' : 'out_of_stock',
          isVariant: true,
          parentBarcode: product.barcode,
          variationValues: JSON.stringify(variant.variationValues),
          variantImage: variant.image || '',
          // Variant pre-order overrides
          isPreOrder: variant.isPreOrder !== null ? variant.isPreOrder : product.isPreOrder || false,
          preOrderStartAt: formatDateForCSV(variant.preOrderStartAt || product.preOrderStartAt),
          preOrderEndAt: formatDateForCSV(variant.preOrderEndAt || product.preOrderEndAt),
          harvestOffsetDays: variant.harvestOffsetDays !== null ? variant.harvestOffsetDays : (product.harvestOffsetDays || 0),
          images: variant.image ? variant.image : (product.images || []).join(',')
        };

        rows.push(variantRow);
      });
    }
  });

  return Papa.unparse(rows, {
    header: true,
    columns: CSV_HEADERS
  });
};

// Generate CSV template with sample data
export const generateCSVTemplate = (): string => {
  const sampleRows: BulkProductRow[] = [
    {
      name: 'Sample Product 1',
      description: 'This is a sample product description',
      price: 100,
      mrp: 120,
      stock: 50,
      unit: 'kg',
      barcode: 'SAMPLE001',
      status: 'available',
      isVisible: true,
      category: 'CATEGORY_ID_HERE',
      categoryIDs: 'CATEGORY_ID_1,CATEGORY_ID_2',
      categoryPath: 'Vegetables>Leafy Greens',
      primaryCategoryID: 'CATEGORY_ID_1',
      farmerId: 'FARMER_ID_HERE',
      nutrition: 'Rich in vitamins and minerals',
      storageInstruction: 'Store in cool, dry place',
      isPreOrder: false,
      preOrderStartAt: '',
      preOrderEndAt: '',
      harvestOffsetDays: 0,
      hasVariants: false,
      defaultVariantID: '',
      images: 'https://example.com/image1.jpg,https://example.com/image2.jpg'
    },
    {
      name: 'Sample Product with Variants',
      description: 'This product has size variants',
      price: 200,
      mrp: 250,
      stock: 30,
      unit: 'piece',
      barcode: 'SAMPLE002',
      status: 'available',
      isVisible: true,
      category: 'CATEGORY_ID_HERE',
      categoryIDs: 'CATEGORY_ID_1',
      categoryPath: 'Fruits>Seasonal',
      primaryCategoryID: 'CATEGORY_ID_1',
      farmerId: 'FARMER_ID_HERE',
      nutrition: 'Organic and fresh',
      storageInstruction: 'Refrigerate after opening',
      isPreOrder: true,
      preOrderStartAt: '2024-01-01T00:00:00.000Z',
      preOrderEndAt: '2024-01-31T23:59:59.000Z',
      harvestOffsetDays: 1,
      hasVariants: true,
      defaultVariantID: '',
      images: 'https://example.com/product2.jpg'
    },
    {
      name: 'Sample Product with Variants',
      description: 'This product has size variants',
      price: 180,
      mrp: 220,
      stock: 15,
      unit: 'piece',
      barcode: 'SAMPLE002-SMALL',
      status: 'available',
      isVisible: true,
      category: 'CATEGORY_ID_HERE',
      categoryIDs: 'CATEGORY_ID_1',
      categoryPath: 'Fruits>Seasonal',
      primaryCategoryID: 'CATEGORY_ID_1',
      farmerId: 'FARMER_ID_HERE',
      nutrition: 'Organic and fresh',
      storageInstruction: 'Refrigerate after opening',
      isPreOrder: true,
      preOrderStartAt: '2024-01-01T00:00:00.000Z',
      preOrderEndAt: '2024-01-31T23:59:59.000Z',
      harvestOffsetDays: 1,
      hasVariants: true,
      defaultVariantID: '',
      isVariant: true,
      parentBarcode: 'SAMPLE002',
      variationValues: '{"Size":"Small"}',
      variantImage: 'https://example.com/variant-small.jpg',
      images: 'https://example.com/variant-small.jpg'
    }
  ];

  return Papa.unparse(sampleRows, {
    header: true,
    columns: CSV_HEADERS
  });
};

// Validate a single product row
export const validateProductRow = (row: BulkProductRow, rowIndex: number): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Required fields validation
  if (!row.name?.trim()) {
    errors.push('Product name is required');
  }

  if (!row.barcode?.trim()) {
    errors.push('Barcode is required');
  }

  if (!row.unit?.trim()) {
    errors.push('Unit is required');
  }

  // Numeric validations
  if (isNaN(row.price) || row.price < 0) {
    errors.push('Price must be a valid positive number');
  }

  if (isNaN(row.stock) || row.stock < 0) {
    errors.push('Stock must be a valid non-negative number');
  }

  // Status validation
  if (!['available', 'out_of_stock', 'coming_soon'].includes(row.status)) {
    errors.push('Status must be one of: available, out_of_stock, coming_soon');
  }

  // Pre-order validation
  if (row.isPreOrder) {
    if (row.preOrderStartAt && row.preOrderEndAt) {
      const startDate = new Date(row.preOrderStartAt);
      const endDate = new Date(row.preOrderEndAt);

      if (isNaN(startDate.getTime())) {
        errors.push('Pre-order start date is invalid');
      }

      if (isNaN(endDate.getTime())) {
        errors.push('Pre-order end date is invalid');
      }

      if (startDate >= endDate) {
        errors.push('Pre-order start date must be before end date');
      }
    }
  }

  // Variant validation
  if (row.isVariant) {
    if (!row.parentSKU?.trim()) {
      errors.push('Parent SKU is required for variant products');
    }

    if (row.variationValues) {
      try {
        JSON.parse(row.variationValues);
      } catch {
        errors.push('Variation values must be valid JSON');
      }
    }
  }

  // Warnings
  if (!row.description?.trim()) {
    warnings.push('Product description is empty');
  }

  if (row.mrp && row.mrp < row.price) {
    warnings.push('MRP is less than selling price');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

// Helper function to format dates for CSV
const formatDateForCSV = (date: any): string => {
  if (!date) return '';

  try {
    // Handle Firestore timestamp
    if (date.toDate && typeof date.toDate === 'function') {
      return date.toDate().toISOString();
    }

    // Handle regular Date object or string
    return new Date(date).toISOString();
  } catch (error) {
    console.error('Error formatting date for CSV:', error);
    return '';
  }
};

// Download CSV file
export const downloadCSV = (content: string, filename: string): void => {
  const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');

  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};
